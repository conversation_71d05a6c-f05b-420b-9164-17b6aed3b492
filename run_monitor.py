#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恆生指數期貨監控程式啟動器
快速選擇和啟動不同版本的監控程式
"""

import os
import sys
import subprocess

def check_dependencies():
    """檢查依賴項"""
    try:
        import futu
        print("✅ futu-api 已安裝")
        return True
    except ImportError:
        print("❌ 未安裝 futu-api")
        print("請運行: pip install futu-api")
        return False

def check_opend_connection():
    """檢查 OpenD 連接"""
    try:
        from futu import OpenQuoteContext, RET_OK
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        quote_ctx.close()
        print("✅ OpenD 連接正常")
        return True
    except Exception as e:
        print(f"❌ OpenD 連接失敗: {e}")
        print("請確保:")
        print("  1. FUTU OpenD 已啟動")
        print("  2. 已使用牛牛號 22188140 登錄")
        print("  3. 端口 11111 可用")
        return False

def run_program(script_name):
    """運行指定的程式"""
    if not os.path.exists(script_name):
        print(f"❌ 找不到文件: {script_name}")
        return
    
    try:
        print(f"\n🚀 正在啟動 {script_name}...")
        subprocess.run([sys.executable, script_name])
    except KeyboardInterrupt:
        print(f"\n⏹️  {script_name} 已停止")
    except Exception as e:
        print(f"❌ 運行 {script_name} 時發生錯誤: {e}")

def main():
    """主函數"""
    print("🎯 恆生指數期貨監控程式啟動器")
    print("=" * 50)
    
    # 檢查依賴項
    print("\n🔍 檢查系統環境...")
    if not check_dependencies():
        input("\n按 Enter 鍵退出...")
        return
    
    # 檢查 OpenD 連接
    if not check_opend_connection():
        print("\n⚠️  建議先解決連接問題再繼續")
        choice = input("是否仍要繼續? (y/n): ").strip().lower()
        if choice != 'y':
            return
    
    while True:
        print("\n" + "=" * 50)
        print("📌 請選擇要運行的程式:")
        print("1. 修復版監控程式 (fixed_hsi_monitor.py) ⭐推薦")
        print("   - 已修復所有已知問題")
        print("   - 支持實時推送")
        print("   - 穩定可靠")

        print("\n2. 簡易監控程式 (simple_hsi_monitor.py)")
        print("   - 適合新手使用")
        print("   - 基本功能完整")
        print("   - 操作簡單")

        print("\n3. 標準監控程式 (hsi_futures_realtime.py)")
        print("   - 面向對象設計")
        print("   - 支持實時推送")
        print("   - 功能較完整")

        print("\n4. 進階監控程式 (advanced_hsi_monitor.py)")
        print("   - 功能最完整")
        print("   - 支持歷史記錄")
        print("   - 多種顯示格式")
        print("   - 數據導出功能")

        print("\n5. 查看配置文件 (config.py)")
        print("6. 查看使用說明 (README.md)")
        print("7. 退出")
        
        choice = input("\n請輸入選項 (1-7): ").strip()

        if choice == "1":
            run_program("fixed_hsi_monitor.py")

        elif choice == "2":
            run_program("simple_hsi_monitor.py")

        elif choice == "3":
            run_program("hsi_futures_realtime.py")

        elif choice == "4":
            run_program("advanced_hsi_monitor.py")

        elif choice == "5":
            if os.path.exists("config.py"):
                print("\n📄 配置文件內容:")
                print("-" * 30)
                with open("config.py", 'r', encoding='utf-8') as f:
                    print(f.read())
                input("\n按 Enter 鍵繼續...")
            else:
                print("❌ 找不到配置文件")

        elif choice == "6":
            if os.path.exists("README.md"):
                print("\n📖 使用說明:")
                print("-" * 30)
                with open("README.md", 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 只顯示前50行
                    lines = content.split('\n')
                    for i, line in enumerate(lines[:50]):
                        print(line)
                    if len(lines) > 50:
                        print("\n... (更多內容請直接查看 README.md 文件)")
                input("\n按 Enter 鍵繼續...")
            else:
                print("❌ 找不到說明文件")

        elif choice == "7":
            print("👋 程式已退出")
            break
            
        else:
            print("❌ 無效選項，請重新選擇")

if __name__ == "__main__":
    main()
