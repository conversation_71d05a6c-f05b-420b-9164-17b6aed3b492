<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>恆生指數期貨實時監控</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-group {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        select, button {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        select {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }

        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            font-weight: bold;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            text-align: center;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 18px;
        }

        .status.connected {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }

        .status.disconnected {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }

        .status.connecting {
            background: linear-gradient(45deg, #ff9800, #f57c00);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .price-panel {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .price-item:last-child {
            border-bottom: none;
        }

        .price-label {
            font-weight: bold;
            color: #ccc;
        }

        .price-value {
            font-size: 1.2em;
            font-weight: bold;
        }

        .price-up {
            color: #4CAF50;
        }

        .price-down {
            color: #f44336;
        }

        .price-neutral {
            color: #fff;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            height: 600px;
        }

        .volume-chart {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            height: 200px;
            margin-top: 20px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 恆生指數期貨實時監控</h1>
            <p>數據來源: FUTU API | 更新頻率: 1秒 | 牛牛號: 22188140</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="productSelect">選擇產品:</label>
                <select id="productSelect">
                    <option value="HK.HSImain">恆生指數期貨主連</option>
                    <option value="HK.MHImain">小型恆生指數期貨主連</option>
                </select>
            </div>
            
            <div class="control-group">
                <button id="connectBtn" onclick="toggleConnection()">連接</button>
                <button id="refreshBtn" onclick="refreshData()" disabled>手動刷新</button>
            </div>
        </div>

        <div id="status" class="status disconnected">
            ❌ 未連接 - 請點擊連接按鈕開始監控
        </div>

        <div class="main-content">
            <div class="price-panel">
                <h3 style="margin-bottom: 20px; text-align: center;">📊 實時報價</h3>
                <div class="price-item">
                    <span class="price-label">代碼:</span>
                    <span id="code" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">名稱:</span>
                    <span id="name" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">最新價:</span>
                    <span id="lastPrice" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">開盤價:</span>
                    <span id="openPrice" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">最高價:</span>
                    <span id="highPrice" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">最低價:</span>
                    <span id="lowPrice" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">昨收價:</span>
                    <span id="prevClose" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">漲跌額:</span>
                    <span id="change" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">漲跌幅:</span>
                    <span id="changePercent" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">成交量:</span>
                    <span id="volume" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">成交額:</span>
                    <span id="turnover" class="price-value">-</span>
                </div>
                <div class="price-item">
                    <span class="price-label">更新時間:</span>
                    <span id="updateTime" class="price-value">-</span>
                </div>
            </div>

            <div class="chart-container">
                <h3 style="margin-bottom: 15px; text-align: center;">📈 5分鐘K線圖 (含10SMA, 20SMA)</h3>
                <canvas id="candlestickChart"></canvas>
            </div>
        </div>

        <div class="volume-chart">
            <h3 style="margin-bottom: 15px; text-align: center;">📊 成交量</h3>
            <canvas id="volumeChart"></canvas>
        </div>

        <div class="footer">
            <p>⚠️ 本程式僅供學習和研究使用，投資有風險，請謹慎決策</p>
            <p>最後更新: <span id="lastUpdate">-</span></p>
        </div>
    </div>

    <script>
        // 全局變量
        let isConnected = false;
        let updateInterval = null;
        let currentProduct = 'HK.HSImain';
        let candlestickChart = null;
        let volumeChart = null;
        let priceData = [];
        let volumeData = [];
        let klineData = [];

        // API配置
        const API_BASE_URL = 'http://localhost:8080/api';

        // 初始化圖表
        function initCharts() {
            // K線圖
            const candlestickCtx = document.getElementById('candlestickChart').getContext('2d');
            candlestickChart = new Chart(candlestickCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: '價格',
                            data: [],
                            borderColor: '#4CAF50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: '10SMA',
                            data: [],
                            borderColor: '#ff9800',
                            backgroundColor: 'transparent',
                            borderWidth: 1,
                            fill: false
                        },
                        {
                            label: '20SMA',
                            data: [],
                            borderColor: '#2196F3',
                            backgroundColor: 'transparent',
                            borderWidth: 1,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#fff'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#fff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#fff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });

            // 成交量圖
            const volumeCtx = document.getElementById('volumeChart').getContext('2d');
            volumeChart = new Chart(volumeCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '成交量',
                        data: [],
                        backgroundColor: 'rgba(76, 175, 80, 0.6)',
                        borderColor: '#4CAF50',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#fff'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#fff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#fff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // 計算移動平均線
        function calculateSMA(data, period) {
            const sma = [];
            for (let i = 0; i < data.length; i++) {
                if (i < period - 1) {
                    sma.push(null);
                } else {
                    const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
                    sma.push(sum / period);
                }
            }
            return sma;
        }

        // API調用函數
        async function apiCall(endpoint, params = {}) {
            try {
                const url = new URL(`${API_BASE_URL}${endpoint}`);
                Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

                const response = await fetch(url);
                const data = await response.json();

                if (!data.success) {
                    throw new Error(data.message || '未知錯誤');
                }

                return data.data;
            } catch (error) {
                console.error(`API調用失敗 (${endpoint}):`, error);
                throw error;
            }
        }

        // 獲取實時報價
        async function fetchRealTimeQuote() {
            try {
                const data = await apiCall('/quote', { product: currentProduct });
                return data;
            } catch (error) {
                console.error('獲取實時報價失敗:', error);
                return null;
            }
        }

        // 獲取K線數據
        async function fetchKlineData() {
            try {
                const data = await apiCall('/kline', { product: currentProduct, num: 50 });
                return data;
            } catch (error) {
                console.error('獲取K線數據失敗:', error);
                return null;
            }
        }

        // 更新價格顯示
        function updatePriceDisplay(data) {
            document.getElementById('code').textContent = data.code;
            document.getElementById('name').textContent = data.name;
            document.getElementById('lastPrice').textContent = data.last_price.toFixed(1);
            document.getElementById('openPrice').textContent = data.open_price.toFixed(1);
            document.getElementById('highPrice').textContent = data.high_price.toFixed(1);
            document.getElementById('lowPrice').textContent = data.low_price.toFixed(1);
            document.getElementById('prevClose').textContent = data.prev_close_price.toFixed(1);

            const change = data.last_price - data.prev_close_price;
            const changePercent = (change / data.prev_close_price) * 100;
            
            const changeElement = document.getElementById('change');
            const changePercentElement = document.getElementById('changePercent');
            
            const changeClass = change > 0 ? 'price-up' : change < 0 ? 'price-down' : 'price-neutral';
            const changeSymbol = change > 0 ? '📈 +' : change < 0 ? '📉 ' : '➡️ ';
            
            changeElement.textContent = changeSymbol + change.toFixed(1);
            changeElement.className = `price-value ${changeClass}`;
            
            changePercentElement.textContent = changeSymbol + changePercent.toFixed(2) + '%';
            changePercentElement.className = `price-value ${changeClass}`;

            document.getElementById('volume').textContent = data.volume.toLocaleString();
            document.getElementById('turnover').textContent = data.turnover.toLocaleString();
            document.getElementById('updateTime').textContent = data.data_time;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('zh-TW');
        }

        // 更新圖表（使用K線數據）
        async function updateChartsWithKline() {
            try {
                const klineData = await fetchKlineData();
                if (!klineData || klineData.length === 0) return;

                // 準備圖表數據
                const labels = [];
                const prices = [];
                const sma10Data = [];
                const sma20Data = [];
                const volumes = [];

                klineData.forEach(item => {
                    const time = new Date(item.time).toLocaleTimeString('zh-TW', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    labels.push(time);
                    prices.push(item.close);
                    sma10Data.push(item.sma10);
                    sma20Data.push(item.sma20);
                    volumes.push(item.volume);
                });

                // 更新K線圖
                candlestickChart.data.labels = labels;
                candlestickChart.data.datasets[0].data = prices;
                candlestickChart.data.datasets[1].data = sma10Data;
                candlestickChart.data.datasets[2].data = sma20Data;

                // 更新成交量圖
                volumeChart.data.labels = labels;
                volumeChart.data.datasets[0].data = volumes;

                // 重新渲染圖表
                candlestickChart.update('none');
                volumeChart.update('none');

            } catch (error) {
                console.error('更新圖表失敗:', error);
            }
        }

        // 獲取數據
        async function fetchData() {
            if (!isConnected) return;

            try {
                // 獲取實時報價
                const quoteData = await fetchRealTimeQuote();
                if (quoteData) {
                    updatePriceDisplay(quoteData);
                }

                // 每5次更新獲取一次K線數據（減少API調用頻率）
                if (Math.random() < 0.2) {
                    await updateChartsWithKline();
                }

            } catch (error) {
                console.error('獲取數據失敗:', error);
                updateStatus('❌ 數據獲取失敗: ' + error.message, 'disconnected');
            }
        }

        // 更新狀態
        function updateStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        // 切換連接狀態
        async function toggleConnection() {
            const connectBtn = document.getElementById('connectBtn');
            const refreshBtn = document.getElementById('refreshBtn');
            const productSelect = document.getElementById('productSelect');

            if (!isConnected) {
                // 開始連接
                updateStatus('🔄 正在連接FUTU API...', 'connecting');
                connectBtn.textContent = '連接中...';
                connectBtn.disabled = true;

                try {
                    // 連接到FUTU API
                    await apiCall('/connect');

                    isConnected = true;
                    currentProduct = productSelect.value;

                    updateStatus('✅ 已連接 - 正在接收實時數據', 'connected');
                    connectBtn.textContent = '斷開連接';
                    connectBtn.disabled = false;
                    refreshBtn.disabled = false;
                    productSelect.disabled = true;

                    // 初始化圖表數據
                    await updateChartsWithKline();

                    // 開始定時更新
                    updateInterval = setInterval(fetchData, 1000);

                    // 立即獲取一次數據
                    await fetchData();

                } catch (error) {
                    updateStatus('❌ 連接失敗: ' + error.message, 'disconnected');
                    connectBtn.textContent = '連接';
                    connectBtn.disabled = false;
                }

            } else {
                // 斷開連接
                try {
                    await apiCall('/disconnect');
                } catch (error) {
                    console.error('斷開連接錯誤:', error);
                }

                isConnected = false;
                clearInterval(updateInterval);

                updateStatus('❌ 已斷開連接', 'disconnected');
                connectBtn.textContent = '連接';
                refreshBtn.disabled = true;
                productSelect.disabled = false;
            }
        }

        // 手動刷新
        async function refreshData() {
            if (isConnected) {
                await fetchData();
                await updateChartsWithKline();
                updateStatus('🔄 數據已刷新', 'connected');
            }
        }

        // 產品切換
        document.getElementById('productSelect').addEventListener('change', function() {
            if (!isConnected) {
                currentProduct = this.value;
            }
        });

        // 頁面加載完成後初始化
        window.addEventListener('load', function() {
            initCharts();
            updateStatus('❌ 未連接 - 請點擊連接按鈕開始監控', 'disconnected');
        });

        // 頁面關閉前清理
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>
