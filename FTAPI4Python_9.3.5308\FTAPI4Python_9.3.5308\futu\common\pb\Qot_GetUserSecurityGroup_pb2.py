# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Qot_GetUserSecurityGroup.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Common_pb2 as Common__pb2
import Qot_Common_pb2 as Qot__Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Qot_GetUserSecurityGroup.proto',
  package='Qot_GetUserSecurityGroup',
  syntax='proto2',
  serialized_pb=_b('\n\x1eQot_GetUserSecurityGroup.proto\x12\x18Qot_GetUserSecurityGroup\x1a\x0c\x43ommon.proto\x1a\x10Qot_Common.proto\"\x18\n\x03\x43\x32S\x12\x11\n\tgroupType\x18\x01 \x02(\x05\"1\n\tGroupData\x12\x11\n\tgroupName\x18\x01 \x02(\t\x12\x11\n\tgroupType\x18\x02 \x02(\x05\"=\n\x03S2C\x12\x36\n\tgroupList\x18\x01 \x03(\x0b\x32#.Qot_GetUserSecurityGroup.GroupData\"5\n\x07Request\x12*\n\x03\x63\x32s\x18\x01 \x02(\x0b\x32\x1d.Qot_GetUserSecurityGroup.C2S\"n\n\x08Response\x12\x15\n\x07retType\x18\x01 \x02(\x05:\x04-400\x12\x0e\n\x06retMsg\x18\x02 \x01(\t\x12\x0f\n\x07\x65rrCode\x18\x03 \x01(\x05\x12*\n\x03s2c\x18\x04 \x01(\x0b\x32\x1d.Qot_GetUserSecurityGroup.S2C*a\n\tGroupType\x12\x15\n\x11GroupType_Unknown\x10\x00\x12\x14\n\x10GroupType_Custom\x10\x01\x12\x14\n\x10GroupType_System\x10\x02\x12\x11\n\rGroupType_All\x10\x03\x42N\n\x13\x63om.futu.openapi.pbZ7github.com/futuopen/ftapi4go/pb/qotgetusersecuritygroup')
  ,
  dependencies=[Common__pb2.DESCRIPTOR,Qot__Common__pb2.DESCRIPTOR,])

_GROUPTYPE = _descriptor.EnumDescriptor(
  name='GroupType',
  full_name='Qot_GetUserSecurityGroup.GroupType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='GroupType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GroupType_Custom', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GroupType_System', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GroupType_All', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=399,
  serialized_end=496,
)
_sym_db.RegisterEnumDescriptor(_GROUPTYPE)

GroupType = enum_type_wrapper.EnumTypeWrapper(_GROUPTYPE)
GroupType_Unknown = 0
GroupType_Custom = 1
GroupType_System = 2
GroupType_All = 3



_C2S = _descriptor.Descriptor(
  name='C2S',
  full_name='Qot_GetUserSecurityGroup.C2S',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='groupType', full_name='Qot_GetUserSecurityGroup.C2S.groupType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=92,
  serialized_end=116,
)


_GROUPDATA = _descriptor.Descriptor(
  name='GroupData',
  full_name='Qot_GetUserSecurityGroup.GroupData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='groupName', full_name='Qot_GetUserSecurityGroup.GroupData.groupName', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='groupType', full_name='Qot_GetUserSecurityGroup.GroupData.groupType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=118,
  serialized_end=167,
)


_S2C = _descriptor.Descriptor(
  name='S2C',
  full_name='Qot_GetUserSecurityGroup.S2C',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='groupList', full_name='Qot_GetUserSecurityGroup.S2C.groupList', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=169,
  serialized_end=230,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='Qot_GetUserSecurityGroup.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='c2s', full_name='Qot_GetUserSecurityGroup.Request.c2s', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=232,
  serialized_end=285,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Qot_GetUserSecurityGroup.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='retType', full_name='Qot_GetUserSecurityGroup.Response.retType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=True, default_value=-400,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retMsg', full_name='Qot_GetUserSecurityGroup.Response.retMsg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errCode', full_name='Qot_GetUserSecurityGroup.Response.errCode', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s2c', full_name='Qot_GetUserSecurityGroup.Response.s2c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=287,
  serialized_end=397,
)

_S2C.fields_by_name['groupList'].message_type = _GROUPDATA
_REQUEST.fields_by_name['c2s'].message_type = _C2S
_RESPONSE.fields_by_name['s2c'].message_type = _S2C
DESCRIPTOR.message_types_by_name['C2S'] = _C2S
DESCRIPTOR.message_types_by_name['GroupData'] = _GROUPDATA
DESCRIPTOR.message_types_by_name['S2C'] = _S2C
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.enum_types_by_name['GroupType'] = _GROUPTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

C2S = _reflection.GeneratedProtocolMessageType('C2S', (_message.Message,), dict(
  DESCRIPTOR = _C2S,
  __module__ = 'Qot_GetUserSecurityGroup_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetUserSecurityGroup.C2S)
  ))
_sym_db.RegisterMessage(C2S)

GroupData = _reflection.GeneratedProtocolMessageType('GroupData', (_message.Message,), dict(
  DESCRIPTOR = _GROUPDATA,
  __module__ = 'Qot_GetUserSecurityGroup_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetUserSecurityGroup.GroupData)
  ))
_sym_db.RegisterMessage(GroupData)

S2C = _reflection.GeneratedProtocolMessageType('S2C', (_message.Message,), dict(
  DESCRIPTOR = _S2C,
  __module__ = 'Qot_GetUserSecurityGroup_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetUserSecurityGroup.S2C)
  ))
_sym_db.RegisterMessage(S2C)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'Qot_GetUserSecurityGroup_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetUserSecurityGroup.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'Qot_GetUserSecurityGroup_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetUserSecurityGroup.Response)
  ))
_sym_db.RegisterMessage(Response)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ7github.com/futuopen/ftapi4go/pb/qotgetusersecuritygroup'))
# @@protoc_insertion_point(module_scope)
