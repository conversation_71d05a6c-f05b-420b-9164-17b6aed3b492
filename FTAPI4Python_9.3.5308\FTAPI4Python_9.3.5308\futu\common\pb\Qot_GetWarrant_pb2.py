# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Qot_GetWarrant.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Common_pb2 as Common__pb2
import Qot_Common_pb2 as Qot__Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Qot_GetWarrant.proto',
  package='Qot_GetWarrant',
  syntax='proto2',
  serialized_pb=_b('\n\x14Qot_GetWarrant.proto\x12\x0eQot_GetWarrant\x1a\x0c\x43ommon.proto\x1a\x10Qot_Common.proto\"\xdf\x05\n\x03\x43\x32S\x12\r\n\x05\x62\x65gin\x18\x01 \x02(\x05\x12\x0b\n\x03num\x18\x02 \x02(\x05\x12\x11\n\tsortField\x18\x03 \x02(\x05\x12\x0e\n\x06\x61scend\x18\x04 \x02(\x08\x12#\n\x05owner\x18\x05 \x01(\x0b\x32\x14.Qot_Common.Security\x12\x10\n\x08typeList\x18\x06 \x03(\x05\x12\x12\n\nissuerList\x18\x07 \x03(\x05\x12\x17\n\x0fmaturityTimeMin\x18\x08 \x01(\t\x12\x17\n\x0fmaturityTimeMax\x18\t \x01(\t\x12\x11\n\tipoPeriod\x18\n \x01(\x05\x12\x11\n\tpriceType\x18\x0b \x01(\x05\x12\x0e\n\x06status\x18\x0c \x01(\x05\x12\x13\n\x0b\x63urPriceMin\x18\r \x01(\x01\x12\x13\n\x0b\x63urPriceMax\x18\x0e \x01(\x01\x12\x16\n\x0estrikePriceMin\x18\x0f \x01(\x01\x12\x16\n\x0estrikePriceMax\x18\x10 \x01(\x01\x12\x11\n\tstreetMin\x18\x11 \x01(\x01\x12\x11\n\tstreetMax\x18\x12 \x01(\x01\x12\x15\n\rconversionMin\x18\x13 \x01(\x01\x12\x15\n\rconversionMax\x18\x14 \x01(\x01\x12\x0e\n\x06volMin\x18\x15 \x01(\x04\x12\x0e\n\x06volMax\x18\x16 \x01(\x04\x12\x12\n\npremiumMin\x18\x17 \x01(\x01\x12\x12\n\npremiumMax\x18\x18 \x01(\x01\x12\x18\n\x10leverageRatioMin\x18\x19 \x01(\x01\x12\x18\n\x10leverageRatioMax\x18\x1a \x01(\x01\x12\x10\n\x08\x64\x65ltaMin\x18\x1b \x01(\x01\x12\x10\n\x08\x64\x65ltaMax\x18\x1c \x01(\x01\x12\x12\n\nimpliedMin\x18\x1d \x01(\x01\x12\x12\n\nimpliedMax\x18\x1e \x01(\x01\x12\x18\n\x10recoveryPriceMin\x18\x1f \x01(\x01\x12\x18\n\x10recoveryPriceMax\x18  \x01(\x01\x12\x1d\n\x15priceRecoveryRatioMin\x18! \x01(\x01\x12\x1d\n\x15priceRecoveryRatioMax\x18\" \x01(\x01\"\xc5\x07\n\x0bWarrantData\x12#\n\x05stock\x18\x01 \x02(\x0b\x32\x14.Qot_Common.Security\x12#\n\x05owner\x18\x02 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x0c\n\x04type\x18\x03 \x02(\x05\x12\x0e\n\x06issuer\x18\x04 \x02(\x05\x12\x14\n\x0cmaturityTime\x18\x05 \x02(\t\x12\x19\n\x11maturityTimestamp\x18\x06 \x01(\x01\x12\x10\n\x08listTime\x18\x07 \x02(\t\x12\x15\n\rlistTimestamp\x18\x08 \x01(\x01\x12\x15\n\rlastTradeTime\x18\t \x02(\t\x12\x1a\n\x12lastTradeTimestamp\x18\n \x01(\x01\x12\x15\n\rrecoveryPrice\x18\x0b \x01(\x01\x12\x17\n\x0f\x63onversionRatio\x18\x0c \x02(\x01\x12\x0f\n\x07lotSize\x18\r \x02(\x05\x12\x13\n\x0bstrikePrice\x18\x0e \x02(\x01\x12\x16\n\x0elastClosePrice\x18\x0f \x02(\x01\x12\x0c\n\x04name\x18\x10 \x02(\t\x12\x10\n\x08\x63urPrice\x18\x11 \x02(\x01\x12\x16\n\x0epriceChangeVal\x18\x12 \x02(\x01\x12\x12\n\nchangeRate\x18\x13 \x02(\x01\x12\x0e\n\x06status\x18\x14 \x02(\x05\x12\x10\n\x08\x62idPrice\x18\x15 \x02(\x01\x12\x10\n\x08\x61skPrice\x18\x16 \x02(\x01\x12\x0e\n\x06\x62idVol\x18\x17 \x02(\x03\x12\x0e\n\x06\x61skVol\x18\x18 \x02(\x03\x12\x0e\n\x06volume\x18\x19 \x02(\x03\x12\x10\n\x08turnover\x18\x1a \x02(\x01\x12\r\n\x05score\x18\x1b \x02(\x01\x12\x0f\n\x07premium\x18\x1c \x02(\x01\x12\x16\n\x0e\x62reakEvenPoint\x18\x1d \x02(\x01\x12\x10\n\x08leverage\x18\x1e \x02(\x01\x12\x0c\n\x04ipop\x18\x1f \x02(\x01\x12\x1a\n\x12priceRecoveryRatio\x18  \x01(\x01\x12\x17\n\x0f\x63onversionPrice\x18! \x02(\x01\x12\x12\n\nstreetRate\x18\" \x02(\x01\x12\x11\n\tstreetVol\x18# \x02(\x03\x12\x11\n\tamplitude\x18$ \x02(\x01\x12\x11\n\tissueSize\x18% \x02(\x03\x12\x11\n\thighPrice\x18\' \x02(\x01\x12\x10\n\x08lowPrice\x18( \x02(\x01\x12\x19\n\x11impliedVolatility\x18) \x01(\x01\x12\r\n\x05\x64\x65lta\x18* \x01(\x01\x12\x19\n\x11\x65\x66\x66\x65\x63tiveLeverage\x18+ \x02(\x01\x12\x18\n\x10upperStrikePrice\x18, \x01(\x01\x12\x18\n\x10lowerStrikePrice\x18- \x01(\x01\x12\x19\n\x11inLinePriceStatus\x18. \x01(\x05\"_\n\x03S2C\x12\x10\n\x08lastPage\x18\x01 \x02(\x08\x12\x10\n\x08\x61llCount\x18\x02 \x02(\x05\x12\x34\n\x0fwarrantDataList\x18\x03 \x03(\x0b\x32\x1b.Qot_GetWarrant.WarrantData\"+\n\x07Request\x12 \n\x03\x63\x32s\x18\x01 \x02(\x0b\x32\x13.Qot_GetWarrant.C2S\"d\n\x08Response\x12\x15\n\x07retType\x18\x01 \x02(\x05:\x04-400\x12\x0e\n\x06retMsg\x18\x02 \x01(\t\x12\x0f\n\x07\x65rrCode\x18\x03 \x01(\x05\x12 \n\x03s2c\x18\x04 \x01(\x0b\x32\x13.Qot_GetWarrant.S2CBD\n\x13\x63om.futu.openapi.pbZ-github.com/futuopen/ftapi4go/pb/qotgetwarrant')
  ,
  dependencies=[Common__pb2.DESCRIPTOR,Qot__Common__pb2.DESCRIPTOR,])




_C2S = _descriptor.Descriptor(
  name='C2S',
  full_name='Qot_GetWarrant.C2S',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='begin', full_name='Qot_GetWarrant.C2S.begin', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num', full_name='Qot_GetWarrant.C2S.num', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sortField', full_name='Qot_GetWarrant.C2S.sortField', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ascend', full_name='Qot_GetWarrant.C2S.ascend', index=3,
      number=4, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='owner', full_name='Qot_GetWarrant.C2S.owner', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='typeList', full_name='Qot_GetWarrant.C2S.typeList', index=5,
      number=6, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='issuerList', full_name='Qot_GetWarrant.C2S.issuerList', index=6,
      number=7, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maturityTimeMin', full_name='Qot_GetWarrant.C2S.maturityTimeMin', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maturityTimeMax', full_name='Qot_GetWarrant.C2S.maturityTimeMax', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ipoPeriod', full_name='Qot_GetWarrant.C2S.ipoPeriod', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priceType', full_name='Qot_GetWarrant.C2S.priceType', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='Qot_GetWarrant.C2S.status', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='curPriceMin', full_name='Qot_GetWarrant.C2S.curPriceMin', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='curPriceMax', full_name='Qot_GetWarrant.C2S.curPriceMax', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strikePriceMin', full_name='Qot_GetWarrant.C2S.strikePriceMin', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strikePriceMax', full_name='Qot_GetWarrant.C2S.strikePriceMax', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='streetMin', full_name='Qot_GetWarrant.C2S.streetMin', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='streetMax', full_name='Qot_GetWarrant.C2S.streetMax', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conversionMin', full_name='Qot_GetWarrant.C2S.conversionMin', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conversionMax', full_name='Qot_GetWarrant.C2S.conversionMax', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volMin', full_name='Qot_GetWarrant.C2S.volMin', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volMax', full_name='Qot_GetWarrant.C2S.volMax', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='premiumMin', full_name='Qot_GetWarrant.C2S.premiumMin', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='premiumMax', full_name='Qot_GetWarrant.C2S.premiumMax', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='leverageRatioMin', full_name='Qot_GetWarrant.C2S.leverageRatioMin', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='leverageRatioMax', full_name='Qot_GetWarrant.C2S.leverageRatioMax', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deltaMin', full_name='Qot_GetWarrant.C2S.deltaMin', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deltaMax', full_name='Qot_GetWarrant.C2S.deltaMax', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='impliedMin', full_name='Qot_GetWarrant.C2S.impliedMin', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='impliedMax', full_name='Qot_GetWarrant.C2S.impliedMax', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recoveryPriceMin', full_name='Qot_GetWarrant.C2S.recoveryPriceMin', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recoveryPriceMax', full_name='Qot_GetWarrant.C2S.recoveryPriceMax', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priceRecoveryRatioMin', full_name='Qot_GetWarrant.C2S.priceRecoveryRatioMin', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priceRecoveryRatioMax', full_name='Qot_GetWarrant.C2S.priceRecoveryRatioMax', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=73,
  serialized_end=808,
)


_WARRANTDATA = _descriptor.Descriptor(
  name='WarrantData',
  full_name='Qot_GetWarrant.WarrantData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='stock', full_name='Qot_GetWarrant.WarrantData.stock', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='owner', full_name='Qot_GetWarrant.WarrantData.owner', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='Qot_GetWarrant.WarrantData.type', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='issuer', full_name='Qot_GetWarrant.WarrantData.issuer', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maturityTime', full_name='Qot_GetWarrant.WarrantData.maturityTime', index=4,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maturityTimestamp', full_name='Qot_GetWarrant.WarrantData.maturityTimestamp', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listTime', full_name='Qot_GetWarrant.WarrantData.listTime', index=6,
      number=7, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listTimestamp', full_name='Qot_GetWarrant.WarrantData.listTimestamp', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastTradeTime', full_name='Qot_GetWarrant.WarrantData.lastTradeTime', index=8,
      number=9, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastTradeTimestamp', full_name='Qot_GetWarrant.WarrantData.lastTradeTimestamp', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recoveryPrice', full_name='Qot_GetWarrant.WarrantData.recoveryPrice', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conversionRatio', full_name='Qot_GetWarrant.WarrantData.conversionRatio', index=11,
      number=12, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lotSize', full_name='Qot_GetWarrant.WarrantData.lotSize', index=12,
      number=13, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strikePrice', full_name='Qot_GetWarrant.WarrantData.strikePrice', index=13,
      number=14, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastClosePrice', full_name='Qot_GetWarrant.WarrantData.lastClosePrice', index=14,
      number=15, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Qot_GetWarrant.WarrantData.name', index=15,
      number=16, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='curPrice', full_name='Qot_GetWarrant.WarrantData.curPrice', index=16,
      number=17, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priceChangeVal', full_name='Qot_GetWarrant.WarrantData.priceChangeVal', index=17,
      number=18, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='changeRate', full_name='Qot_GetWarrant.WarrantData.changeRate', index=18,
      number=19, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='Qot_GetWarrant.WarrantData.status', index=19,
      number=20, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bidPrice', full_name='Qot_GetWarrant.WarrantData.bidPrice', index=20,
      number=21, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='askPrice', full_name='Qot_GetWarrant.WarrantData.askPrice', index=21,
      number=22, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bidVol', full_name='Qot_GetWarrant.WarrantData.bidVol', index=22,
      number=23, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='askVol', full_name='Qot_GetWarrant.WarrantData.askVol', index=23,
      number=24, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volume', full_name='Qot_GetWarrant.WarrantData.volume', index=24,
      number=25, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turnover', full_name='Qot_GetWarrant.WarrantData.turnover', index=25,
      number=26, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='score', full_name='Qot_GetWarrant.WarrantData.score', index=26,
      number=27, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='premium', full_name='Qot_GetWarrant.WarrantData.premium', index=27,
      number=28, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='breakEvenPoint', full_name='Qot_GetWarrant.WarrantData.breakEvenPoint', index=28,
      number=29, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='leverage', full_name='Qot_GetWarrant.WarrantData.leverage', index=29,
      number=30, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ipop', full_name='Qot_GetWarrant.WarrantData.ipop', index=30,
      number=31, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priceRecoveryRatio', full_name='Qot_GetWarrant.WarrantData.priceRecoveryRatio', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conversionPrice', full_name='Qot_GetWarrant.WarrantData.conversionPrice', index=32,
      number=33, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='streetRate', full_name='Qot_GetWarrant.WarrantData.streetRate', index=33,
      number=34, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='streetVol', full_name='Qot_GetWarrant.WarrantData.streetVol', index=34,
      number=35, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amplitude', full_name='Qot_GetWarrant.WarrantData.amplitude', index=35,
      number=36, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='issueSize', full_name='Qot_GetWarrant.WarrantData.issueSize', index=36,
      number=37, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='highPrice', full_name='Qot_GetWarrant.WarrantData.highPrice', index=37,
      number=39, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lowPrice', full_name='Qot_GetWarrant.WarrantData.lowPrice', index=38,
      number=40, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='impliedVolatility', full_name='Qot_GetWarrant.WarrantData.impliedVolatility', index=39,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delta', full_name='Qot_GetWarrant.WarrantData.delta', index=40,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='effectiveLeverage', full_name='Qot_GetWarrant.WarrantData.effectiveLeverage', index=41,
      number=43, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upperStrikePrice', full_name='Qot_GetWarrant.WarrantData.upperStrikePrice', index=42,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lowerStrikePrice', full_name='Qot_GetWarrant.WarrantData.lowerStrikePrice', index=43,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inLinePriceStatus', full_name='Qot_GetWarrant.WarrantData.inLinePriceStatus', index=44,
      number=46, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=811,
  serialized_end=1776,
)


_S2C = _descriptor.Descriptor(
  name='S2C',
  full_name='Qot_GetWarrant.S2C',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lastPage', full_name='Qot_GetWarrant.S2C.lastPage', index=0,
      number=1, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allCount', full_name='Qot_GetWarrant.S2C.allCount', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warrantDataList', full_name='Qot_GetWarrant.S2C.warrantDataList', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1778,
  serialized_end=1873,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='Qot_GetWarrant.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='c2s', full_name='Qot_GetWarrant.Request.c2s', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1875,
  serialized_end=1918,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Qot_GetWarrant.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='retType', full_name='Qot_GetWarrant.Response.retType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=True, default_value=-400,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retMsg', full_name='Qot_GetWarrant.Response.retMsg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errCode', full_name='Qot_GetWarrant.Response.errCode', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s2c', full_name='Qot_GetWarrant.Response.s2c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1920,
  serialized_end=2020,
)

_C2S.fields_by_name['owner'].message_type = Qot__Common__pb2._SECURITY
_WARRANTDATA.fields_by_name['stock'].message_type = Qot__Common__pb2._SECURITY
_WARRANTDATA.fields_by_name['owner'].message_type = Qot__Common__pb2._SECURITY
_S2C.fields_by_name['warrantDataList'].message_type = _WARRANTDATA
_REQUEST.fields_by_name['c2s'].message_type = _C2S
_RESPONSE.fields_by_name['s2c'].message_type = _S2C
DESCRIPTOR.message_types_by_name['C2S'] = _C2S
DESCRIPTOR.message_types_by_name['WarrantData'] = _WARRANTDATA
DESCRIPTOR.message_types_by_name['S2C'] = _S2C
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

C2S = _reflection.GeneratedProtocolMessageType('C2S', (_message.Message,), dict(
  DESCRIPTOR = _C2S,
  __module__ = 'Qot_GetWarrant_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetWarrant.C2S)
  ))
_sym_db.RegisterMessage(C2S)

WarrantData = _reflection.GeneratedProtocolMessageType('WarrantData', (_message.Message,), dict(
  DESCRIPTOR = _WARRANTDATA,
  __module__ = 'Qot_GetWarrant_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetWarrant.WarrantData)
  ))
_sym_db.RegisterMessage(WarrantData)

S2C = _reflection.GeneratedProtocolMessageType('S2C', (_message.Message,), dict(
  DESCRIPTOR = _S2C,
  __module__ = 'Qot_GetWarrant_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetWarrant.S2C)
  ))
_sym_db.RegisterMessage(S2C)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'Qot_GetWarrant_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetWarrant.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'Qot_GetWarrant_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetWarrant.Response)
  ))
_sym_db.RegisterMessage(Response)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ-github.com/futuopen/ftapi4go/pb/qotgetwarrant'))
# @@protoc_insertion_point(module_scope)
