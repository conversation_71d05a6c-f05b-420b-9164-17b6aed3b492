syntax = "proto2";
package Qot_UpdateBasicQot;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotupdatebasicqot";

import "Common.proto";
import "Qot_Common.proto";

message S2C
{
	repeated Qot_Common.BasicQot basicQotList = 1; //股票基本行情
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
