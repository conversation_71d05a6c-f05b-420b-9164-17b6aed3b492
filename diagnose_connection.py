#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
連接診斷工具
幫助排查Web監控系統的連接問題
"""

import socket
import urllib.request
import urllib.error
import json
import subprocess
import sys
import os

def check_port_available(port):
    """檢查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', port))
            return result != 0  # 0表示端口被占用
    except Exception:
        return True

def check_port_listening(port):
    """檢查端口是否在監聽"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', port))
            return result == 0  # 0表示連接成功
    except Exception:
        return False

def test_api_endpoint(url):
    """測試API端點"""
    try:
        req = urllib.request.Request(url)
        req.add_header('User-Agent', 'Mozilla/5.0')
        
        with urllib.request.urlopen(req, timeout=5) as response:
            data = response.read().decode('utf-8')
            return True, data
    except urllib.error.HTTPError as e:
        return False, f"HTTP錯誤 {e.code}: {e.reason}"
    except urllib.error.URLError as e:
        return False, f"URL錯誤: {e.reason}"
    except Exception as e:
        return False, f"其他錯誤: {str(e)}"

def check_futu_opend():
    """檢查FUTU OpenD連接"""
    try:
        from futu import OpenQuoteContext, RET_OK
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        quote_ctx.close()
        return True, "FUTU OpenD連接正常"
    except ImportError:
        return False, "未安裝futu-api"
    except Exception as e:
        return False, f"FUTU OpenD連接失敗: {str(e)}"

def check_files_exist():
    """檢查必要文件是否存在"""
    files = [
        'hsi_web_monitor.html',
        'hsi_web_server.py',
        'simple_web_server.py'
    ]
    
    results = {}
    for file in files:
        results[file] = os.path.exists(file)
    
    return results

def main():
    """主診斷函數"""
    print("🔍 Web監控系統連接診斷工具")
    print("=" * 50)
    
    # 1. 檢查文件
    print("\n📁 檢查必要文件...")
    files_status = check_files_exist()
    for file, exists in files_status.items():
        status = "✅" if exists else "❌"
        print(f"   {status} {file}")
    
    # 2. 檢查端口
    print("\n🔌 檢查端口狀態...")
    port_8080_available = check_port_available(8080)
    port_8080_listening = check_port_listening(8080)
    port_11111_listening = check_port_listening(11111)
    
    print(f"   端口8080可用: {'✅' if port_8080_available else '❌ (被占用)'}")
    print(f"   端口8080監聽: {'✅' if port_8080_listening else '❌ (無服務)'}")
    print(f"   端口11111監聽: {'✅' if port_11111_listening else '❌ (OpenD未啟動)'}")
    
    # 3. 檢查FUTU OpenD
    print("\n🔗 檢查FUTU OpenD...")
    futu_ok, futu_msg = check_futu_opend()
    print(f"   {'✅' if futu_ok else '❌'} {futu_msg}")
    
    # 4. 測試API端點
    print("\n🌐 測試API端點...")
    if port_8080_listening:
        endpoints = [
            'http://localhost:8080/api/products',
            'http://localhost:8080/api/connect',
            'http://localhost:8080/api/quote?product=HK.HSImain'
        ]
        
        for endpoint in endpoints:
            success, result = test_api_endpoint(endpoint)
            print(f"   {'✅' if success else '❌'} {endpoint}")
            if not success:
                print(f"      錯誤: {result}")
    else:
        print("   ⚠️ 服務器未運行，跳過API測試")
    
    # 5. 提供解決建議
    print("\n💡 解決建議:")
    
    if not port_11111_listening:
        print("   🔧 FUTU OpenD問題:")
        print("      1. 啟動FUTU OpenD程式")
        print("      2. 使用牛牛號22188140登錄")
        print("      3. 確認期貨LV1權限")
    
    if not port_8080_listening:
        print("   🔧 後端服務器問題:")
        print("      1. 運行: python simple_web_server.py (測試版)")
        print("      2. 或運行: python hsi_web_server.py (完整版)")
        print("      3. 確認沒有防火牆阻擋")
    
    if not port_8080_available and port_8080_listening:
        print("   🔧 端口占用問題:")
        print("      1. 關閉其他使用8080端口的程式")
        print("      2. 或修改服務器端口設置")
    
    # 6. 快速測試選項
    print("\n🚀 快速測試選項:")
    print("   1. 啟動簡化服務器: python simple_web_server.py")
    print("   2. 啟動完整服務器: python hsi_web_server.py")
    print("   3. 一鍵啟動: python start_web_monitor.py")
    
    # 7. 瀏覽器測試
    print("\n🌐 瀏覽器測試:")
    if port_8080_listening:
        print("   在瀏覽器中訪問以下URL測試:")
        print("   http://localhost:8080/api/products")
        print("   應該看到JSON格式的產品列表")
    
    print("\n" + "=" * 50)
    
    # 互動式測試
    while True:
        print("\n選擇操作:")
        print("1. 啟動簡化測試服務器")
        print("2. 測試API連接")
        print("3. 重新診斷")
        print("4. 退出")
        
        choice = input("\n請輸入選項 (1-4): ").strip()
        
        if choice == "1":
            print("\n🚀 啟動簡化測試服務器...")
            try:
                subprocess.run([sys.executable, 'simple_web_server.py'])
            except KeyboardInterrupt:
                print("\n⏹️ 服務器已停止")
            except Exception as e:
                print(f"❌ 啟動失敗: {e}")
                
        elif choice == "2":
            print("\n🧪 測試API連接...")
            success, result = test_api_endpoint('http://localhost:8080/api/products')
            if success:
                print("✅ API連接正常")
                try:
                    data = json.loads(result)
                    print(f"   響應數據: {data}")
                except:
                    print(f"   響應內容: {result[:200]}...")
            else:
                print(f"❌ API連接失敗: {result}")
                
        elif choice == "3":
            main()
            break
            
        elif choice == "4":
            print("👋 診斷結束")
            break
            
        else:
            print("❌ 無效選項")

if __name__ == "__main__":
    main()
