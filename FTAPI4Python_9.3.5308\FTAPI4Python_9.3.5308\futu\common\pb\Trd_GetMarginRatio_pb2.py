# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Trd_GetMarginRatio.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Trd_Common_pb2 as Trd__Common__pb2
import Qot_Common_pb2 as Qot__Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Trd_GetMarginRatio.proto',
  package='Trd_GetMarginRatio',
  syntax='proto2',
  serialized_pb=_b('\n\x18Trd_GetMarginRatio.proto\x12\x12Trd_GetMarginRatio\x1a\x10Trd_Common.proto\x1a\x10Qot_Common.proto\"\xc9\x02\n\x0fMarginRatioInfo\x12&\n\x08security\x18\x01 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x14\n\x0cisLongPermit\x18\x02 \x01(\x08\x12\x15\n\risShortPermit\x18\x03 \x01(\x08\x12\x17\n\x0fshortPoolRemain\x18\x04 \x01(\x01\x12\x14\n\x0cshortFeeRate\x18\x05 \x01(\x01\x12\x16\n\x0e\x61lertLongRatio\x18\x06 \x01(\x01\x12\x17\n\x0f\x61lertShortRatio\x18\x07 \x01(\x01\x12\x13\n\x0bimLongRatio\x18\x08 \x01(\x01\x12\x14\n\x0cimShortRatio\x18\t \x01(\x01\x12\x14\n\x0cmcmLongRatio\x18\n \x01(\x01\x12\x15\n\rmcmShortRatio\x18\x0b \x01(\x01\x12\x13\n\x0bmmLongRatio\x18\x0c \x01(\x01\x12\x14\n\x0cmmShortRatio\x18\r \x01(\x01\"X\n\x03\x43\x32S\x12%\n\x06header\x18\x01 \x02(\x0b\x32\x15.Trd_Common.TrdHeader\x12*\n\x0csecurityList\x18\x02 \x03(\x0b\x32\x14.Qot_Common.Security\"n\n\x03S2C\x12%\n\x06header\x18\x01 \x02(\x0b\x32\x15.Trd_Common.TrdHeader\x12@\n\x13marginRatioInfoList\x18\x02 \x03(\x0b\x32#.Trd_GetMarginRatio.MarginRatioInfo\"/\n\x07Request\x12$\n\x03\x63\x32s\x18\x01 \x02(\x0b\x32\x17.Trd_GetMarginRatio.C2S\"h\n\x08Response\x12\x15\n\x07retType\x18\x01 \x02(\x05:\x04-400\x12\x0e\n\x06retMsg\x18\x02 \x01(\t\x12\x0f\n\x07\x65rrCode\x18\x03 \x01(\x05\x12$\n\x03s2c\x18\x04 \x01(\x0b\x32\x17.Trd_GetMarginRatio.S2CBH\n\x13\x63om.futu.openapi.pbZ1github.com/futuopen/ftapi4go/pb/trdgetmarginratio')
  ,
  dependencies=[Trd__Common__pb2.DESCRIPTOR,Qot__Common__pb2.DESCRIPTOR,])




_MARGINRATIOINFO = _descriptor.Descriptor(
  name='MarginRatioInfo',
  full_name='Trd_GetMarginRatio.MarginRatioInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='security', full_name='Trd_GetMarginRatio.MarginRatioInfo.security', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isLongPermit', full_name='Trd_GetMarginRatio.MarginRatioInfo.isLongPermit', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isShortPermit', full_name='Trd_GetMarginRatio.MarginRatioInfo.isShortPermit', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shortPoolRemain', full_name='Trd_GetMarginRatio.MarginRatioInfo.shortPoolRemain', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shortFeeRate', full_name='Trd_GetMarginRatio.MarginRatioInfo.shortFeeRate', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='alertLongRatio', full_name='Trd_GetMarginRatio.MarginRatioInfo.alertLongRatio', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='alertShortRatio', full_name='Trd_GetMarginRatio.MarginRatioInfo.alertShortRatio', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imLongRatio', full_name='Trd_GetMarginRatio.MarginRatioInfo.imLongRatio', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imShortRatio', full_name='Trd_GetMarginRatio.MarginRatioInfo.imShortRatio', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mcmLongRatio', full_name='Trd_GetMarginRatio.MarginRatioInfo.mcmLongRatio', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mcmShortRatio', full_name='Trd_GetMarginRatio.MarginRatioInfo.mcmShortRatio', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mmLongRatio', full_name='Trd_GetMarginRatio.MarginRatioInfo.mmLongRatio', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mmShortRatio', full_name='Trd_GetMarginRatio.MarginRatioInfo.mmShortRatio', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=85,
  serialized_end=414,
)


_C2S = _descriptor.Descriptor(
  name='C2S',
  full_name='Trd_GetMarginRatio.C2S',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='Trd_GetMarginRatio.C2S.header', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='securityList', full_name='Trd_GetMarginRatio.C2S.securityList', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=416,
  serialized_end=504,
)


_S2C = _descriptor.Descriptor(
  name='S2C',
  full_name='Trd_GetMarginRatio.S2C',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='Trd_GetMarginRatio.S2C.header', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='marginRatioInfoList', full_name='Trd_GetMarginRatio.S2C.marginRatioInfoList', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=506,
  serialized_end=616,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='Trd_GetMarginRatio.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='c2s', full_name='Trd_GetMarginRatio.Request.c2s', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=618,
  serialized_end=665,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Trd_GetMarginRatio.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='retType', full_name='Trd_GetMarginRatio.Response.retType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=True, default_value=-400,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retMsg', full_name='Trd_GetMarginRatio.Response.retMsg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errCode', full_name='Trd_GetMarginRatio.Response.errCode', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s2c', full_name='Trd_GetMarginRatio.Response.s2c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=667,
  serialized_end=771,
)

_MARGINRATIOINFO.fields_by_name['security'].message_type = Qot__Common__pb2._SECURITY
_C2S.fields_by_name['header'].message_type = Trd__Common__pb2._TRDHEADER
_C2S.fields_by_name['securityList'].message_type = Qot__Common__pb2._SECURITY
_S2C.fields_by_name['header'].message_type = Trd__Common__pb2._TRDHEADER
_S2C.fields_by_name['marginRatioInfoList'].message_type = _MARGINRATIOINFO
_REQUEST.fields_by_name['c2s'].message_type = _C2S
_RESPONSE.fields_by_name['s2c'].message_type = _S2C
DESCRIPTOR.message_types_by_name['MarginRatioInfo'] = _MARGINRATIOINFO
DESCRIPTOR.message_types_by_name['C2S'] = _C2S
DESCRIPTOR.message_types_by_name['S2C'] = _S2C
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

MarginRatioInfo = _reflection.GeneratedProtocolMessageType('MarginRatioInfo', (_message.Message,), dict(
  DESCRIPTOR = _MARGINRATIOINFO,
  __module__ = 'Trd_GetMarginRatio_pb2'
  # @@protoc_insertion_point(class_scope:Trd_GetMarginRatio.MarginRatioInfo)
  ))
_sym_db.RegisterMessage(MarginRatioInfo)

C2S = _reflection.GeneratedProtocolMessageType('C2S', (_message.Message,), dict(
  DESCRIPTOR = _C2S,
  __module__ = 'Trd_GetMarginRatio_pb2'
  # @@protoc_insertion_point(class_scope:Trd_GetMarginRatio.C2S)
  ))
_sym_db.RegisterMessage(C2S)

S2C = _reflection.GeneratedProtocolMessageType('S2C', (_message.Message,), dict(
  DESCRIPTOR = _S2C,
  __module__ = 'Trd_GetMarginRatio_pb2'
  # @@protoc_insertion_point(class_scope:Trd_GetMarginRatio.S2C)
  ))
_sym_db.RegisterMessage(S2C)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'Trd_GetMarginRatio_pb2'
  # @@protoc_insertion_point(class_scope:Trd_GetMarginRatio.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'Trd_GetMarginRatio_pb2'
  # @@protoc_insertion_point(class_scope:Trd_GetMarginRatio.Response)
  ))
_sym_db.RegisterMessage(Response)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ1github.com/futuopen/ftapi4go/pb/trdgetmarginratio'))
# @@protoc_insertion_point(module_scope)
