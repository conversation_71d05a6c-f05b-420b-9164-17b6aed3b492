syntax = "proto2";
package Qot_GetReference;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetreference";

import "Common.proto";
import "Qot_Common.proto";

enum ReferenceType
{
	ReferenceType_Unknow = 0; 
	ReferenceType_Warrant = 1; //正股相关的窝轮
	ReferenceType_Future = 2; //期货主连的相关合约
}

message C2S
{
	required Qot_Common.Security security = 1; //股票
	required int32 referenceType = 2; // ReferenceType, 相关类型
}

message S2C
{
	repeated Qot_Common.SecurityStaticInfo staticInfoList = 2; //相关股票列表
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
