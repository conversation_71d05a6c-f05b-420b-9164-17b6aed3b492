# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Trd_Common.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Common_pb2 as Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Trd_Common.proto',
  package='Trd_Common',
  syntax='proto2',
  serialized_pb=_b('\n\x10Trd_Common.proto\x12\nTrd_Common\x1a\x0c\x43ommon.proto\"]\n\x0b\x41\x63\x63\x43\x61shInfo\x12\x10\n\x08\x63urrency\x18\x01 \x01(\x05\x12\x0c\n\x04\x63\x61sh\x18\x02 \x01(\x01\x12\x18\n\x10\x61vailableBalance\x18\x03 \x01(\x01\x12\x14\n\x0cnetCashPower\x18\x04 \x01(\x01\"2\n\rAccMarketInfo\x12\x11\n\ttrdMarket\x18\x01 \x01(\x05\x12\x0e\n\x06\x61ssets\x18\x02 \x01(\x01\"=\n\tTrdHeader\x12\x0e\n\x06trdEnv\x18\x01 \x02(\x05\x12\r\n\x05\x61\x63\x63ID\x18\x02 \x02(\x04\x12\x11\n\ttrdMarket\x18\x03 \x02(\x05\"\xb5\x01\n\x06TrdAcc\x12\x0e\n\x06trdEnv\x18\x01 \x02(\x05\x12\r\n\x05\x61\x63\x63ID\x18\x02 \x02(\x04\x12\x19\n\x11trdMarketAuthList\x18\x03 \x03(\x05\x12\x0f\n\x07\x61\x63\x63Type\x18\x04 \x01(\x05\x12\x0f\n\x07\x63\x61rdNum\x18\x05 \x01(\t\x12\x14\n\x0csecurityFirm\x18\x06 \x01(\x05\x12\x12\n\nsimAccType\x18\x07 \x01(\x05\x12\x12\n\nuniCardNum\x18\x08 \x01(\t\x12\x11\n\taccStatus\x18\t \x01(\x05\"\xe8\x05\n\x05\x46unds\x12\r\n\x05power\x18\x01 \x02(\x01\x12\x13\n\x0btotalAssets\x18\x02 \x02(\x01\x12\x0c\n\x04\x63\x61sh\x18\x03 \x02(\x01\x12\x11\n\tmarketVal\x18\x04 \x02(\x01\x12\x12\n\nfrozenCash\x18\x05 \x02(\x01\x12\x10\n\x08\x64\x65\x62tCash\x18\x06 \x02(\x01\x12\x19\n\x11\x61vlWithdrawalCash\x18\x07 \x02(\x01\x12\x10\n\x08\x63urrency\x18\x08 \x01(\x05\x12\x16\n\x0e\x61vailableFunds\x18\t \x01(\x01\x12\x14\n\x0cunrealizedPL\x18\n \x01(\x01\x12\x12\n\nrealizedPL\x18\x0b \x01(\x01\x12\x11\n\triskLevel\x18\x0c \x01(\x05\x12\x15\n\rinitialMargin\x18\r \x01(\x01\x12\x19\n\x11maintenanceMargin\x18\x0e \x01(\x01\x12-\n\x0c\x63\x61shInfoList\x18\x0f \x03(\x0b\x32\x17.Trd_Common.AccCashInfo\x12\x15\n\rmaxPowerShort\x18\x10 \x01(\x01\x12\x14\n\x0cnetCashPower\x18\x11 \x01(\x01\x12\x0e\n\x06longMv\x18\x12 \x01(\x01\x12\x0f\n\x07shortMv\x18\x13 \x01(\x01\x12\x14\n\x0cpendingAsset\x18\x14 \x01(\x01\x12\x15\n\rmaxWithdrawal\x18\x15 \x01(\x01\x12\x12\n\nriskStatus\x18\x16 \x01(\x05\x12\x18\n\x10marginCallMargin\x18\x17 \x01(\x01\x12\r\n\x05isPdt\x18\x18 \x01(\x08\x12\x0e\n\x06pdtSeq\x18\x19 \x01(\t\x12\x15\n\rbeginningDTBP\x18\x1a \x01(\x01\x12\x15\n\rremainingDTBP\x18\x1b \x01(\x01\x12\x14\n\x0c\x64tCallAmount\x18\x1c \x01(\x01\x12\x10\n\x08\x64tStatus\x18\x1d \x01(\x05\x12\x18\n\x10securitiesAssets\x18\x1e \x01(\x01\x12\x12\n\nfundAssets\x18\x1f \x01(\x01\x12\x12\n\nbondAssets\x18  \x01(\x01\x12\x31\n\x0emarketInfoList\x18! \x03(\x0b\x32\x19.Trd_Common.AccMarketInfo\"\xe1\x03\n\x08Position\x12\x12\n\npositionID\x18\x01 \x02(\x04\x12\x14\n\x0cpositionSide\x18\x02 \x02(\x05\x12\x0c\n\x04\x63ode\x18\x03 \x02(\t\x12\x0c\n\x04name\x18\x04 \x02(\t\x12\x0b\n\x03qty\x18\x05 \x02(\x01\x12\x12\n\ncanSellQty\x18\x06 \x02(\x01\x12\r\n\x05price\x18\x07 \x02(\x01\x12\x11\n\tcostPrice\x18\x08 \x01(\x01\x12\x0b\n\x03val\x18\t \x02(\x01\x12\r\n\x05plVal\x18\n \x02(\x01\x12\x0f\n\x07plRatio\x18\x0b \x01(\x01\x12\x11\n\tsecMarket\x18\x0c \x01(\x05\x12\x10\n\x08td_plVal\x18\x15 \x01(\x01\x12\x11\n\ttd_trdVal\x18\x16 \x01(\x01\x12\x11\n\ttd_buyVal\x18\x17 \x01(\x01\x12\x11\n\ttd_buyQty\x18\x18 \x01(\x01\x12\x12\n\ntd_sellVal\x18\x19 \x01(\x01\x12\x12\n\ntd_sellQty\x18\x1a \x01(\x01\x12\x14\n\x0cunrealizedPL\x18\x1c \x01(\x01\x12\x12\n\nrealizedPL\x18\x1d \x01(\x01\x12\x10\n\x08\x63urrency\x18\x1e \x01(\x05\x12\x11\n\ttrdMarket\x18\x1f \x01(\x05\x12\x18\n\x10\x64ilutedCostPrice\x18  \x01(\x01\x12\x18\n\x10\x61verageCostPrice\x18! \x01(\x01\x12\x16\n\x0e\x61veragePlRatio\x18\" \x01(\x01\"\x85\x04\n\x05Order\x12\x0f\n\x07trdSide\x18\x01 \x02(\x05\x12\x11\n\torderType\x18\x02 \x02(\x05\x12\x13\n\x0borderStatus\x18\x03 \x02(\x05\x12\x0f\n\x07orderID\x18\x04 \x02(\x04\x12\x11\n\torderIDEx\x18\x05 \x02(\t\x12\x0c\n\x04\x63ode\x18\x06 \x02(\t\x12\x0c\n\x04name\x18\x07 \x02(\t\x12\x0b\n\x03qty\x18\x08 \x02(\x01\x12\r\n\x05price\x18\t \x01(\x01\x12\x12\n\ncreateTime\x18\n \x02(\t\x12\x12\n\nupdateTime\x18\x0b \x02(\t\x12\x0f\n\x07\x66illQty\x18\x0c \x01(\x01\x12\x14\n\x0c\x66illAvgPrice\x18\r \x01(\x01\x12\x12\n\nlastErrMsg\x18\x0e \x01(\t\x12\x11\n\tsecMarket\x18\x0f \x01(\x05\x12\x17\n\x0f\x63reateTimestamp\x18\x10 \x01(\x01\x12\x17\n\x0fupdateTimestamp\x18\x11 \x01(\x01\x12\x0e\n\x06remark\x18\x12 \x01(\t\x12\x13\n\x0btimeInForce\x18\x13 \x01(\x05\x12\x16\n\x0e\x66illOutsideRTH\x18\x14 \x01(\x08\x12\x10\n\x08\x61uxPrice\x18\x15 \x01(\x01\x12\x11\n\ttrailType\x18\x16 \x01(\x05\x12\x12\n\ntrailValue\x18\x17 \x01(\x01\x12\x13\n\x0btrailSpread\x18\x18 \x01(\x01\x12\x10\n\x08\x63urrency\x18\x19 \x01(\x05\x12\x11\n\ttrdMarket\x18\x1a \x01(\x05\x12\x0f\n\x07session\x18\x1b \x01(\x05\",\n\x0cOrderFeeItem\x12\r\n\x05title\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\"[\n\x08OrderFee\x12\x11\n\torderIDEx\x18\x01 \x02(\t\x12\x11\n\tfeeAmount\x18\x02 \x01(\x01\x12)\n\x07\x66\x65\x65List\x18\x03 \x03(\x0b\x32\x18.Trd_Common.OrderFeeItem\"\xca\x02\n\tOrderFill\x12\x0f\n\x07trdSide\x18\x01 \x02(\x05\x12\x0e\n\x06\x66illID\x18\x02 \x02(\x04\x12\x10\n\x08\x66illIDEx\x18\x03 \x02(\t\x12\x0f\n\x07orderID\x18\x04 \x01(\x04\x12\x11\n\torderIDEx\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x02(\t\x12\x0c\n\x04name\x18\x07 \x02(\t\x12\x0b\n\x03qty\x18\x08 \x02(\x01\x12\r\n\x05price\x18\t \x02(\x01\x12\x12\n\ncreateTime\x18\n \x02(\t\x12\x17\n\x0f\x63ounterBrokerID\x18\x0b \x01(\x05\x12\x19\n\x11\x63ounterBrokerName\x18\x0c \x01(\t\x12\x11\n\tsecMarket\x18\r \x01(\x05\x12\x17\n\x0f\x63reateTimestamp\x18\x0e \x01(\x01\x12\x17\n\x0fupdateTimestamp\x18\x0f \x01(\x01\x12\x0e\n\x06status\x18\x10 \x01(\x05\x12\x11\n\ttrdMarket\x18\x11 \x01(\x05\"\xb1\x01\n\nMaxTrdQtys\x12\x12\n\nmaxCashBuy\x18\x01 \x02(\x01\x12\x1b\n\x13maxCashAndMarginBuy\x18\x02 \x01(\x01\x12\x17\n\x0fmaxPositionSell\x18\x03 \x02(\x01\x12\x14\n\x0cmaxSellShort\x18\x04 \x01(\x01\x12\x12\n\nmaxBuyBack\x18\x05 \x01(\x01\x12\x16\n\x0elongRequiredIM\x18\x06 \x01(\x01\x12\x17\n\x0fshortRequiredIM\x18\x07 \x01(\x01\"\x88\x01\n\x13TrdFilterConditions\x12\x10\n\x08\x63odeList\x18\x01 \x03(\t\x12\x0e\n\x06idList\x18\x02 \x03(\x04\x12\x11\n\tbeginTime\x18\x03 \x01(\t\x12\x0f\n\x07\x65ndTime\x18\x04 \x01(\t\x12\x15\n\rorderIDExList\x18\x05 \x03(\t\x12\x14\n\x0c\x66ilterMarket\x18\x06 \x01(\x05*.\n\x06TrdEnv\x12\x13\n\x0fTrdEnv_Simulate\x10\x00\x12\x0f\n\x0bTrdEnv_Real\x10\x01*X\n\x0bTrdCategory\x12\x17\n\x13TrdCategory_Unknown\x10\x00\x12\x18\n\x14TrdCategory_Security\x10\x01\x12\x16\n\x12TrdCategory_Future\x10\x02*\x97\x03\n\tTrdMarket\x12\x15\n\x11TrdMarket_Unknown\x10\x00\x12\x10\n\x0cTrdMarket_HK\x10\x01\x12\x10\n\x0cTrdMarket_US\x10\x02\x12\x10\n\x0cTrdMarket_CN\x10\x03\x12\x12\n\x0eTrdMarket_HKCC\x10\x04\x12\x15\n\x11TrdMarket_Futures\x10\x05\x12\x10\n\x0cTrdMarket_SG\x10\x06\x12\x10\n\x0cTrdMarket_AU\x10\x08\x12!\n\x1dTrdMarket_Futures_Simulate_HK\x10\n\x12!\n\x1dTrdMarket_Futures_Simulate_US\x10\x0b\x12!\n\x1dTrdMarket_Futures_Simulate_SG\x10\x0c\x12!\n\x1dTrdMarket_Futures_Simulate_JP\x10\r\x12\x10\n\x0cTrdMarket_JP\x10\x0f\x12\x10\n\x0cTrdMarket_MY\x10o\x12\x10\n\x0cTrdMarket_CA\x10p\x12\x15\n\x11TrdMarket_HK_Fund\x10q\x12\x15\n\x11TrdMarket_US_Fund\x10{*\x80\x02\n\x0cTrdSecMarket\x12\x18\n\x14TrdSecMarket_Unknown\x10\x00\x12\x13\n\x0fTrdSecMarket_HK\x10\x01\x12\x13\n\x0fTrdSecMarket_US\x10\x02\x12\x16\n\x12TrdSecMarket_CN_SH\x10\x1f\x12\x16\n\x12TrdSecMarket_CN_SZ\x10 \x12\x13\n\x0fTrdSecMarket_SG\x10)\x12\x13\n\x0fTrdSecMarket_JP\x10\x33\x12\x13\n\x0fTrdSecMarket_AU\x10=\x12\x13\n\x0fTrdSecMarket_MY\x10G\x12\x13\n\x0fTrdSecMarket_CA\x10Q\x12\x13\n\x0fTrdSecMarket_FX\x10[*m\n\x07TrdSide\x12\x13\n\x0fTrdSide_Unknown\x10\x00\x12\x0f\n\x0bTrdSide_Buy\x10\x01\x12\x10\n\x0cTrdSide_Sell\x10\x02\x12\x15\n\x11TrdSide_SellShort\x10\x03\x12\x13\n\x0fTrdSide_BuyBack\x10\x04*\xeb\x03\n\tOrderType\x12\x15\n\x11OrderType_Unknown\x10\x00\x12\x14\n\x10OrderType_Normal\x10\x01\x12\x14\n\x10OrderType_Market\x10\x02\x12\x1b\n\x17OrderType_AbsoluteLimit\x10\x05\x12\x15\n\x11OrderType_Auction\x10\x06\x12\x1a\n\x16OrderType_AuctionLimit\x10\x07\x12\x1a\n\x16OrderType_SpecialLimit\x10\x08\x12\x1e\n\x1aOrderType_SpecialLimit_All\x10\t\x12\x12\n\x0eOrderType_Stop\x10\n\x12\x17\n\x13OrderType_StopLimit\x10\x0b\x12\x1d\n\x19OrderType_MarketifTouched\x10\x0c\x12\x1c\n\x18OrderType_LimitifTouched\x10\r\x12\x1a\n\x16OrderType_TrailingStop\x10\x0e\x12\x1f\n\x1bOrderType_TrailingStopLimit\x10\x0f\x12\x19\n\x15OrderType_TWAP_MARKET\x10\x10\x12\x18\n\x14OrderType_TWAP_LIMIT\x10\x11\x12\x19\n\x15OrderType_VWAP_MARKET\x10\x12\x12\x18\n\x14OrderType_VWAP_LIMIT\x10\x13*M\n\tTrailType\x12\x15\n\x11TrailType_Unknown\x10\x00\x12\x13\n\x0fTrailType_Ratio\x10\x01\x12\x14\n\x10TrailType_Amount\x10\x02*\xfc\x03\n\x0bOrderStatus\x12\x1b\n\x17OrderStatus_Unsubmitted\x10\x00\x12 \n\x13OrderStatus_Unknown\x10\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\x12\x1d\n\x19OrderStatus_WaitingSubmit\x10\x01\x12\x1a\n\x16OrderStatus_Submitting\x10\x02\x12\x1c\n\x18OrderStatus_SubmitFailed\x10\x03\x12\x17\n\x13OrderStatus_TimeOut\x10\x04\x12\x19\n\x15OrderStatus_Submitted\x10\x05\x12\x1b\n\x17OrderStatus_Filled_Part\x10\n\x12\x1a\n\x16OrderStatus_Filled_All\x10\x0b\x12\x1f\n\x1bOrderStatus_Cancelling_Part\x10\x0c\x12\x1e\n\x1aOrderStatus_Cancelling_All\x10\r\x12\x1e\n\x1aOrderStatus_Cancelled_Part\x10\x0e\x12\x1d\n\x19OrderStatus_Cancelled_All\x10\x0f\x12\x16\n\x12OrderStatus_Failed\x10\x15\x12\x18\n\x14OrderStatus_Disabled\x10\x16\x12\x17\n\x13OrderStatus_Deleted\x10\x17\x12\x1d\n\x19OrderStatus_FillCancelled\x10\x18*e\n\x0fOrderFillStatus\x12\x16\n\x12OrderFillStatus_OK\x10\x00\x12\x1d\n\x19OrderFillStatus_Cancelled\x10\x01\x12\x1b\n\x17OrderFillStatus_Changed\x10\x02*`\n\x0cPositionSide\x12\x15\n\x11PositionSide_Long\x10\x00\x12!\n\x14PositionSide_Unknown\x10\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\x12\x16\n\x12PositionSide_Short\x10\x01*\xad\x01\n\rModifyOrderOp\x12\x19\n\x15ModifyOrderOp_Unknown\x10\x00\x12\x18\n\x14ModifyOrderOp_Normal\x10\x01\x12\x18\n\x14ModifyOrderOp_Cancel\x10\x02\x12\x19\n\x15ModifyOrderOp_Disable\x10\x03\x12\x18\n\x14ModifyOrderOp_Enable\x10\x04\x12\x18\n\x14ModifyOrderOp_Delete\x10\x05*P\n\nTrdAccType\x12\x16\n\x12TrdAccType_Unknown\x10\x00\x12\x13\n\x0fTrdAccType_Cash\x10\x01\x12\x15\n\x11TrdAccType_Margin\x10\x02*B\n\x0cTrdAccStatus\x12\x17\n\x13TrdAccStatus_Active\x10\x00\x12\x19\n\x15TrdAccStatus_Disabled\x10\x01*\xb0\x01\n\x08\x43urrency\x12\x14\n\x10\x43urrency_Unknown\x10\x00\x12\x10\n\x0c\x43urrency_HKD\x10\x01\x12\x10\n\x0c\x43urrency_USD\x10\x02\x12\x10\n\x0c\x43urrency_CNH\x10\x03\x12\x10\n\x0c\x43urrency_JPY\x10\x04\x12\x10\n\x0c\x43urrency_SGD\x10\x05\x12\x10\n\x0c\x43urrency_AUD\x10\x06\x12\x10\n\x0c\x43urrency_CAD\x10\x07\x12\x10\n\x0c\x43urrency_MYR\x10\x08*\xb6\x01\n\x0c\x43ltRiskLevel\x12!\n\x14\x43ltRiskLevel_Unknown\x10\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\x12\x15\n\x11\x43ltRiskLevel_Safe\x10\x00\x12\x18\n\x14\x43ltRiskLevel_Warning\x10\x01\x12\x17\n\x13\x43ltRiskLevel_Danger\x10\x02\x12\x1d\n\x19\x43ltRiskLevel_AbsoluteSafe\x10\x03\x12\x1a\n\x16\x43ltRiskLevel_OptDanger\x10\x04*7\n\x0bTimeInForce\x12\x13\n\x0fTimeInForce_DAY\x10\x00\x12\x13\n\x0fTimeInForce_GTC\x10\x01*\x95\x01\n\x0cSecurityFirm\x12\x18\n\x14SecurityFirm_Unknown\x10\x00\x12\x1f\n\x1bSecurityFirm_FutuSecurities\x10\x01\x12\x18\n\x14SecurityFirm_FutuInc\x10\x02\x12\x17\n\x13SecurityFirm_FutuSG\x10\x03\x12\x17\n\x13SecurityFirm_FutuAU\x10\x04*i\n\nSimAccType\x12\x16\n\x12SimAccType_Unknown\x10\x00\x12\x14\n\x10SimAccType_Stock\x10\x01\x12\x15\n\x11SimAccType_Option\x10\x02\x12\x16\n\x12SimAccType_Futures\x10\x03*\x94\x02\n\rCltRiskStatus\x12\x19\n\x15\x43ltRiskStatus_Unknown\x10\x00\x12\x18\n\x14\x43ltRiskStatus_Level1\x10\x01\x12\x18\n\x14\x43ltRiskStatus_Level2\x10\x02\x12\x18\n\x14\x43ltRiskStatus_Level3\x10\x03\x12\x18\n\x14\x43ltRiskStatus_Level4\x10\x04\x12\x18\n\x14\x43ltRiskStatus_Level5\x10\x05\x12\x18\n\x14\x43ltRiskStatus_Level6\x10\x06\x12\x18\n\x14\x43ltRiskStatus_Level7\x10\x07\x12\x18\n\x14\x43ltRiskStatus_Level8\x10\x08\x12\x18\n\x14\x43ltRiskStatus_Level9\x10\t*b\n\x08\x44TStatus\x12\x14\n\x10\x44TStatus_Unknown\x10\x00\x12\x16\n\x12\x44TStatus_Unlimited\x10\x01\x12\x13\n\x0f\x44TStatus_EMCall\x10\x02\x12\x13\n\x0f\x44TStatus_DTCall\x10\x03\x42@\n\x13\x63om.futu.openapi.pbZ)github.com/futuopen/ftapi4go/pb/trdcommon')
  ,
  dependencies=[Common__pb2.DESCRIPTOR,])

_TRDENV = _descriptor.EnumDescriptor(
  name='TrdEnv',
  full_name='Trd_Common.TrdEnv',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TrdEnv_Simulate', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdEnv_Real', index=1, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2982,
  serialized_end=3028,
)
_sym_db.RegisterEnumDescriptor(_TRDENV)

TrdEnv = enum_type_wrapper.EnumTypeWrapper(_TRDENV)
_TRDCATEGORY = _descriptor.EnumDescriptor(
  name='TrdCategory',
  full_name='Trd_Common.TrdCategory',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TrdCategory_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdCategory_Security', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdCategory_Future', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=3030,
  serialized_end=3118,
)
_sym_db.RegisterEnumDescriptor(_TRDCATEGORY)

TrdCategory = enum_type_wrapper.EnumTypeWrapper(_TRDCATEGORY)
_TRDMARKET = _descriptor.EnumDescriptor(
  name='TrdMarket',
  full_name='Trd_Common.TrdMarket',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_HK', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_US', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_CN', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_HKCC', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_Futures', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_SG', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_AU', index=7, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_Futures_Simulate_HK', index=8, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_Futures_Simulate_US', index=9, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_Futures_Simulate_SG', index=10, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_Futures_Simulate_JP', index=11, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_JP', index=12, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_MY', index=13, number=111,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_CA', index=14, number=112,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_HK_Fund', index=15, number=113,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdMarket_US_Fund', index=16, number=123,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=3121,
  serialized_end=3528,
)
_sym_db.RegisterEnumDescriptor(_TRDMARKET)

TrdMarket = enum_type_wrapper.EnumTypeWrapper(_TRDMARKET)
_TRDSECMARKET = _descriptor.EnumDescriptor(
  name='TrdSecMarket',
  full_name='Trd_Common.TrdSecMarket',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_HK', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_US', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_CN_SH', index=3, number=31,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_CN_SZ', index=4, number=32,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_SG', index=5, number=41,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_JP', index=6, number=51,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_AU', index=7, number=61,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_MY', index=8, number=71,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_CA', index=9, number=81,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSecMarket_FX', index=10, number=91,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=3531,
  serialized_end=3787,
)
_sym_db.RegisterEnumDescriptor(_TRDSECMARKET)

TrdSecMarket = enum_type_wrapper.EnumTypeWrapper(_TRDSECMARKET)
_TRDSIDE = _descriptor.EnumDescriptor(
  name='TrdSide',
  full_name='Trd_Common.TrdSide',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TrdSide_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSide_Buy', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSide_Sell', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSide_SellShort', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdSide_BuyBack', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=3789,
  serialized_end=3898,
)
_sym_db.RegisterEnumDescriptor(_TRDSIDE)

TrdSide = enum_type_wrapper.EnumTypeWrapper(_TRDSIDE)
_ORDERTYPE = _descriptor.EnumDescriptor(
  name='OrderType',
  full_name='Trd_Common.OrderType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OrderType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_Normal', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_Market', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_AbsoluteLimit', index=3, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_Auction', index=4, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_AuctionLimit', index=5, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_SpecialLimit', index=6, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_SpecialLimit_All', index=7, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_Stop', index=8, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_StopLimit', index=9, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_MarketifTouched', index=10, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_LimitifTouched', index=11, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_TrailingStop', index=12, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_TrailingStopLimit', index=13, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_TWAP_MARKET', index=14, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_TWAP_LIMIT', index=15, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_VWAP_MARKET', index=16, number=18,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderType_VWAP_LIMIT', index=17, number=19,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=3901,
  serialized_end=4392,
)
_sym_db.RegisterEnumDescriptor(_ORDERTYPE)

OrderType = enum_type_wrapper.EnumTypeWrapper(_ORDERTYPE)
_TRAILTYPE = _descriptor.EnumDescriptor(
  name='TrailType',
  full_name='Trd_Common.TrailType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TrailType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrailType_Ratio', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrailType_Amount', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4394,
  serialized_end=4471,
)
_sym_db.RegisterEnumDescriptor(_TRAILTYPE)

TrailType = enum_type_wrapper.EnumTypeWrapper(_TRAILTYPE)
_ORDERSTATUS = _descriptor.EnumDescriptor(
  name='OrderStatus',
  full_name='Trd_Common.OrderStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Unsubmitted', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Unknown', index=1, number=-1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_WaitingSubmit', index=2, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Submitting', index=3, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_SubmitFailed', index=4, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_TimeOut', index=5, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Submitted', index=6, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Filled_Part', index=7, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Filled_All', index=8, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Cancelling_Part', index=9, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Cancelling_All', index=10, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Cancelled_Part', index=11, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Cancelled_All', index=12, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Failed', index=13, number=21,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Disabled', index=14, number=22,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_Deleted', index=15, number=23,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderStatus_FillCancelled', index=16, number=24,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4474,
  serialized_end=4982,
)
_sym_db.RegisterEnumDescriptor(_ORDERSTATUS)

OrderStatus = enum_type_wrapper.EnumTypeWrapper(_ORDERSTATUS)
_ORDERFILLSTATUS = _descriptor.EnumDescriptor(
  name='OrderFillStatus',
  full_name='Trd_Common.OrderFillStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OrderFillStatus_OK', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderFillStatus_Cancelled', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderFillStatus_Changed', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4984,
  serialized_end=5085,
)
_sym_db.RegisterEnumDescriptor(_ORDERFILLSTATUS)

OrderFillStatus = enum_type_wrapper.EnumTypeWrapper(_ORDERFILLSTATUS)
_POSITIONSIDE = _descriptor.EnumDescriptor(
  name='PositionSide',
  full_name='Trd_Common.PositionSide',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PositionSide_Long', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PositionSide_Unknown', index=1, number=-1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PositionSide_Short', index=2, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5087,
  serialized_end=5183,
)
_sym_db.RegisterEnumDescriptor(_POSITIONSIDE)

PositionSide = enum_type_wrapper.EnumTypeWrapper(_POSITIONSIDE)
_MODIFYORDEROP = _descriptor.EnumDescriptor(
  name='ModifyOrderOp',
  full_name='Trd_Common.ModifyOrderOp',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ModifyOrderOp_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ModifyOrderOp_Normal', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ModifyOrderOp_Cancel', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ModifyOrderOp_Disable', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ModifyOrderOp_Enable', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ModifyOrderOp_Delete', index=5, number=5,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5186,
  serialized_end=5359,
)
_sym_db.RegisterEnumDescriptor(_MODIFYORDEROP)

ModifyOrderOp = enum_type_wrapper.EnumTypeWrapper(_MODIFYORDEROP)
_TRDACCTYPE = _descriptor.EnumDescriptor(
  name='TrdAccType',
  full_name='Trd_Common.TrdAccType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TrdAccType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdAccType_Cash', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdAccType_Margin', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5361,
  serialized_end=5441,
)
_sym_db.RegisterEnumDescriptor(_TRDACCTYPE)

TrdAccType = enum_type_wrapper.EnumTypeWrapper(_TRDACCTYPE)
_TRDACCSTATUS = _descriptor.EnumDescriptor(
  name='TrdAccStatus',
  full_name='Trd_Common.TrdAccStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TrdAccStatus_Active', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdAccStatus_Disabled', index=1, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5443,
  serialized_end=5509,
)
_sym_db.RegisterEnumDescriptor(_TRDACCSTATUS)

TrdAccStatus = enum_type_wrapper.EnumTypeWrapper(_TRDACCSTATUS)
_CURRENCY = _descriptor.EnumDescriptor(
  name='Currency',
  full_name='Trd_Common.Currency',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='Currency_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Currency_HKD', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Currency_USD', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Currency_CNH', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Currency_JPY', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Currency_SGD', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Currency_AUD', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Currency_CAD', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Currency_MYR', index=8, number=8,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5512,
  serialized_end=5688,
)
_sym_db.RegisterEnumDescriptor(_CURRENCY)

Currency = enum_type_wrapper.EnumTypeWrapper(_CURRENCY)
_CLTRISKLEVEL = _descriptor.EnumDescriptor(
  name='CltRiskLevel',
  full_name='Trd_Common.CltRiskLevel',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CltRiskLevel_Unknown', index=0, number=-1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskLevel_Safe', index=1, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskLevel_Warning', index=2, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskLevel_Danger', index=3, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskLevel_AbsoluteSafe', index=4, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskLevel_OptDanger', index=5, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5691,
  serialized_end=5873,
)
_sym_db.RegisterEnumDescriptor(_CLTRISKLEVEL)

CltRiskLevel = enum_type_wrapper.EnumTypeWrapper(_CLTRISKLEVEL)
_TIMEINFORCE = _descriptor.EnumDescriptor(
  name='TimeInForce',
  full_name='Trd_Common.TimeInForce',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TimeInForce_DAY', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TimeInForce_GTC', index=1, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5875,
  serialized_end=5930,
)
_sym_db.RegisterEnumDescriptor(_TIMEINFORCE)

TimeInForce = enum_type_wrapper.EnumTypeWrapper(_TIMEINFORCE)
_SECURITYFIRM = _descriptor.EnumDescriptor(
  name='SecurityFirm',
  full_name='Trd_Common.SecurityFirm',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SecurityFirm_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityFirm_FutuSecurities', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityFirm_FutuInc', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityFirm_FutuSG', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityFirm_FutuAU', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5933,
  serialized_end=6082,
)
_sym_db.RegisterEnumDescriptor(_SECURITYFIRM)

SecurityFirm = enum_type_wrapper.EnumTypeWrapper(_SECURITYFIRM)
_SIMACCTYPE = _descriptor.EnumDescriptor(
  name='SimAccType',
  full_name='Trd_Common.SimAccType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SimAccType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SimAccType_Stock', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SimAccType_Option', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SimAccType_Futures', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=6084,
  serialized_end=6189,
)
_sym_db.RegisterEnumDescriptor(_SIMACCTYPE)

SimAccType = enum_type_wrapper.EnumTypeWrapper(_SIMACCTYPE)
_CLTRISKSTATUS = _descriptor.EnumDescriptor(
  name='CltRiskStatus',
  full_name='Trd_Common.CltRiskStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CltRiskStatus_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskStatus_Level1', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskStatus_Level2', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskStatus_Level3', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskStatus_Level4', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskStatus_Level5', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskStatus_Level6', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskStatus_Level7', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskStatus_Level8', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CltRiskStatus_Level9', index=9, number=9,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=6192,
  serialized_end=6468,
)
_sym_db.RegisterEnumDescriptor(_CLTRISKSTATUS)

CltRiskStatus = enum_type_wrapper.EnumTypeWrapper(_CLTRISKSTATUS)
_DTSTATUS = _descriptor.EnumDescriptor(
  name='DTStatus',
  full_name='Trd_Common.DTStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DTStatus_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DTStatus_Unlimited', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DTStatus_EMCall', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DTStatus_DTCall', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=6470,
  serialized_end=6568,
)
_sym_db.RegisterEnumDescriptor(_DTSTATUS)

DTStatus = enum_type_wrapper.EnumTypeWrapper(_DTSTATUS)
TrdEnv_Simulate = 0
TrdEnv_Real = 1
TrdCategory_Unknown = 0
TrdCategory_Security = 1
TrdCategory_Future = 2
TrdMarket_Unknown = 0
TrdMarket_HK = 1
TrdMarket_US = 2
TrdMarket_CN = 3
TrdMarket_HKCC = 4
TrdMarket_Futures = 5
TrdMarket_SG = 6
TrdMarket_AU = 8
TrdMarket_Futures_Simulate_HK = 10
TrdMarket_Futures_Simulate_US = 11
TrdMarket_Futures_Simulate_SG = 12
TrdMarket_Futures_Simulate_JP = 13
TrdMarket_JP = 15
TrdMarket_MY = 111
TrdMarket_CA = 112
TrdMarket_HK_Fund = 113
TrdMarket_US_Fund = 123
TrdSecMarket_Unknown = 0
TrdSecMarket_HK = 1
TrdSecMarket_US = 2
TrdSecMarket_CN_SH = 31
TrdSecMarket_CN_SZ = 32
TrdSecMarket_SG = 41
TrdSecMarket_JP = 51
TrdSecMarket_AU = 61
TrdSecMarket_MY = 71
TrdSecMarket_CA = 81
TrdSecMarket_FX = 91
TrdSide_Unknown = 0
TrdSide_Buy = 1
TrdSide_Sell = 2
TrdSide_SellShort = 3
TrdSide_BuyBack = 4
OrderType_Unknown = 0
OrderType_Normal = 1
OrderType_Market = 2
OrderType_AbsoluteLimit = 5
OrderType_Auction = 6
OrderType_AuctionLimit = 7
OrderType_SpecialLimit = 8
OrderType_SpecialLimit_All = 9
OrderType_Stop = 10
OrderType_StopLimit = 11
OrderType_MarketifTouched = 12
OrderType_LimitifTouched = 13
OrderType_TrailingStop = 14
OrderType_TrailingStopLimit = 15
OrderType_TWAP_MARKET = 16
OrderType_TWAP_LIMIT = 17
OrderType_VWAP_MARKET = 18
OrderType_VWAP_LIMIT = 19
TrailType_Unknown = 0
TrailType_Ratio = 1
TrailType_Amount = 2
OrderStatus_Unsubmitted = 0
OrderStatus_Unknown = -1
OrderStatus_WaitingSubmit = 1
OrderStatus_Submitting = 2
OrderStatus_SubmitFailed = 3
OrderStatus_TimeOut = 4
OrderStatus_Submitted = 5
OrderStatus_Filled_Part = 10
OrderStatus_Filled_All = 11
OrderStatus_Cancelling_Part = 12
OrderStatus_Cancelling_All = 13
OrderStatus_Cancelled_Part = 14
OrderStatus_Cancelled_All = 15
OrderStatus_Failed = 21
OrderStatus_Disabled = 22
OrderStatus_Deleted = 23
OrderStatus_FillCancelled = 24
OrderFillStatus_OK = 0
OrderFillStatus_Cancelled = 1
OrderFillStatus_Changed = 2
PositionSide_Long = 0
PositionSide_Unknown = -1
PositionSide_Short = 1
ModifyOrderOp_Unknown = 0
ModifyOrderOp_Normal = 1
ModifyOrderOp_Cancel = 2
ModifyOrderOp_Disable = 3
ModifyOrderOp_Enable = 4
ModifyOrderOp_Delete = 5
TrdAccType_Unknown = 0
TrdAccType_Cash = 1
TrdAccType_Margin = 2
TrdAccStatus_Active = 0
TrdAccStatus_Disabled = 1
Currency_Unknown = 0
Currency_HKD = 1
Currency_USD = 2
Currency_CNH = 3
Currency_JPY = 4
Currency_SGD = 5
Currency_AUD = 6
Currency_CAD = 7
Currency_MYR = 8
CltRiskLevel_Unknown = -1
CltRiskLevel_Safe = 0
CltRiskLevel_Warning = 1
CltRiskLevel_Danger = 2
CltRiskLevel_AbsoluteSafe = 3
CltRiskLevel_OptDanger = 4
TimeInForce_DAY = 0
TimeInForce_GTC = 1
SecurityFirm_Unknown = 0
SecurityFirm_FutuSecurities = 1
SecurityFirm_FutuInc = 2
SecurityFirm_FutuSG = 3
SecurityFirm_FutuAU = 4
SimAccType_Unknown = 0
SimAccType_Stock = 1
SimAccType_Option = 2
SimAccType_Futures = 3
CltRiskStatus_Unknown = 0
CltRiskStatus_Level1 = 1
CltRiskStatus_Level2 = 2
CltRiskStatus_Level3 = 3
CltRiskStatus_Level4 = 4
CltRiskStatus_Level5 = 5
CltRiskStatus_Level6 = 6
CltRiskStatus_Level7 = 7
CltRiskStatus_Level8 = 8
CltRiskStatus_Level9 = 9
DTStatus_Unknown = 0
DTStatus_Unlimited = 1
DTStatus_EMCall = 2
DTStatus_DTCall = 3



_ACCCASHINFO = _descriptor.Descriptor(
  name='AccCashInfo',
  full_name='Trd_Common.AccCashInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='currency', full_name='Trd_Common.AccCashInfo.currency', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cash', full_name='Trd_Common.AccCashInfo.cash', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='availableBalance', full_name='Trd_Common.AccCashInfo.availableBalance', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='netCashPower', full_name='Trd_Common.AccCashInfo.netCashPower', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=46,
  serialized_end=139,
)


_ACCMARKETINFO = _descriptor.Descriptor(
  name='AccMarketInfo',
  full_name='Trd_Common.AccMarketInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trdMarket', full_name='Trd_Common.AccMarketInfo.trdMarket', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='assets', full_name='Trd_Common.AccMarketInfo.assets', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=141,
  serialized_end=191,
)


_TRDHEADER = _descriptor.Descriptor(
  name='TrdHeader',
  full_name='Trd_Common.TrdHeader',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trdEnv', full_name='Trd_Common.TrdHeader.trdEnv', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accID', full_name='Trd_Common.TrdHeader.accID', index=1,
      number=2, type=4, cpp_type=4, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trdMarket', full_name='Trd_Common.TrdHeader.trdMarket', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=193,
  serialized_end=254,
)


_TRDACC = _descriptor.Descriptor(
  name='TrdAcc',
  full_name='Trd_Common.TrdAcc',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trdEnv', full_name='Trd_Common.TrdAcc.trdEnv', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accID', full_name='Trd_Common.TrdAcc.accID', index=1,
      number=2, type=4, cpp_type=4, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trdMarketAuthList', full_name='Trd_Common.TrdAcc.trdMarketAuthList', index=2,
      number=3, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accType', full_name='Trd_Common.TrdAcc.accType', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cardNum', full_name='Trd_Common.TrdAcc.cardNum', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='securityFirm', full_name='Trd_Common.TrdAcc.securityFirm', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='simAccType', full_name='Trd_Common.TrdAcc.simAccType', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uniCardNum', full_name='Trd_Common.TrdAcc.uniCardNum', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accStatus', full_name='Trd_Common.TrdAcc.accStatus', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=257,
  serialized_end=438,
)


_FUNDS = _descriptor.Descriptor(
  name='Funds',
  full_name='Trd_Common.Funds',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='power', full_name='Trd_Common.Funds.power', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='totalAssets', full_name='Trd_Common.Funds.totalAssets', index=1,
      number=2, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cash', full_name='Trd_Common.Funds.cash', index=2,
      number=3, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='marketVal', full_name='Trd_Common.Funds.marketVal', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='frozenCash', full_name='Trd_Common.Funds.frozenCash', index=4,
      number=5, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='debtCash', full_name='Trd_Common.Funds.debtCash', index=5,
      number=6, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='avlWithdrawalCash', full_name='Trd_Common.Funds.avlWithdrawalCash', index=6,
      number=7, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='Trd_Common.Funds.currency', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='availableFunds', full_name='Trd_Common.Funds.availableFunds', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unrealizedPL', full_name='Trd_Common.Funds.unrealizedPL', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='realizedPL', full_name='Trd_Common.Funds.realizedPL', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='riskLevel', full_name='Trd_Common.Funds.riskLevel', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='initialMargin', full_name='Trd_Common.Funds.initialMargin', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maintenanceMargin', full_name='Trd_Common.Funds.maintenanceMargin', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cashInfoList', full_name='Trd_Common.Funds.cashInfoList', index=14,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maxPowerShort', full_name='Trd_Common.Funds.maxPowerShort', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='netCashPower', full_name='Trd_Common.Funds.netCashPower', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='longMv', full_name='Trd_Common.Funds.longMv', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shortMv', full_name='Trd_Common.Funds.shortMv', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pendingAsset', full_name='Trd_Common.Funds.pendingAsset', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maxWithdrawal', full_name='Trd_Common.Funds.maxWithdrawal', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='riskStatus', full_name='Trd_Common.Funds.riskStatus', index=21,
      number=22, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='marginCallMargin', full_name='Trd_Common.Funds.marginCallMargin', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isPdt', full_name='Trd_Common.Funds.isPdt', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pdtSeq', full_name='Trd_Common.Funds.pdtSeq', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='beginningDTBP', full_name='Trd_Common.Funds.beginningDTBP', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remainingDTBP', full_name='Trd_Common.Funds.remainingDTBP', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dtCallAmount', full_name='Trd_Common.Funds.dtCallAmount', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dtStatus', full_name='Trd_Common.Funds.dtStatus', index=28,
      number=29, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='securitiesAssets', full_name='Trd_Common.Funds.securitiesAssets', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fundAssets', full_name='Trd_Common.Funds.fundAssets', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bondAssets', full_name='Trd_Common.Funds.bondAssets', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='marketInfoList', full_name='Trd_Common.Funds.marketInfoList', index=32,
      number=33, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=441,
  serialized_end=1185,
)


_POSITION = _descriptor.Descriptor(
  name='Position',
  full_name='Trd_Common.Position',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='positionID', full_name='Trd_Common.Position.positionID', index=0,
      number=1, type=4, cpp_type=4, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='positionSide', full_name='Trd_Common.Position.positionSide', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='Trd_Common.Position.code', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Trd_Common.Position.name', index=3,
      number=4, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='Trd_Common.Position.qty', index=4,
      number=5, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='canSellQty', full_name='Trd_Common.Position.canSellQty', index=5,
      number=6, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='Trd_Common.Position.price', index=6,
      number=7, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='costPrice', full_name='Trd_Common.Position.costPrice', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='Trd_Common.Position.val', index=8,
      number=9, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plVal', full_name='Trd_Common.Position.plVal', index=9,
      number=10, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plRatio', full_name='Trd_Common.Position.plRatio', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='secMarket', full_name='Trd_Common.Position.secMarket', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='td_plVal', full_name='Trd_Common.Position.td_plVal', index=12,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='td_trdVal', full_name='Trd_Common.Position.td_trdVal', index=13,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='td_buyVal', full_name='Trd_Common.Position.td_buyVal', index=14,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='td_buyQty', full_name='Trd_Common.Position.td_buyQty', index=15,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='td_sellVal', full_name='Trd_Common.Position.td_sellVal', index=16,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='td_sellQty', full_name='Trd_Common.Position.td_sellQty', index=17,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unrealizedPL', full_name='Trd_Common.Position.unrealizedPL', index=18,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='realizedPL', full_name='Trd_Common.Position.realizedPL', index=19,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='Trd_Common.Position.currency', index=20,
      number=30, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trdMarket', full_name='Trd_Common.Position.trdMarket', index=21,
      number=31, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dilutedCostPrice', full_name='Trd_Common.Position.dilutedCostPrice', index=22,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='averageCostPrice', full_name='Trd_Common.Position.averageCostPrice', index=23,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='averagePlRatio', full_name='Trd_Common.Position.averagePlRatio', index=24,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1188,
  serialized_end=1669,
)


_ORDER = _descriptor.Descriptor(
  name='Order',
  full_name='Trd_Common.Order',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trdSide', full_name='Trd_Common.Order.trdSide', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderType', full_name='Trd_Common.Order.orderType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderStatus', full_name='Trd_Common.Order.orderStatus', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderID', full_name='Trd_Common.Order.orderID', index=3,
      number=4, type=4, cpp_type=4, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderIDEx', full_name='Trd_Common.Order.orderIDEx', index=4,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='Trd_Common.Order.code', index=5,
      number=6, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Trd_Common.Order.name', index=6,
      number=7, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='Trd_Common.Order.qty', index=7,
      number=8, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='Trd_Common.Order.price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='createTime', full_name='Trd_Common.Order.createTime', index=9,
      number=10, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updateTime', full_name='Trd_Common.Order.updateTime', index=10,
      number=11, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fillQty', full_name='Trd_Common.Order.fillQty', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fillAvgPrice', full_name='Trd_Common.Order.fillAvgPrice', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastErrMsg', full_name='Trd_Common.Order.lastErrMsg', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='secMarket', full_name='Trd_Common.Order.secMarket', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='createTimestamp', full_name='Trd_Common.Order.createTimestamp', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updateTimestamp', full_name='Trd_Common.Order.updateTimestamp', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='Trd_Common.Order.remark', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timeInForce', full_name='Trd_Common.Order.timeInForce', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fillOutsideRTH', full_name='Trd_Common.Order.fillOutsideRTH', index=19,
      number=20, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auxPrice', full_name='Trd_Common.Order.auxPrice', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trailType', full_name='Trd_Common.Order.trailType', index=21,
      number=22, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trailValue', full_name='Trd_Common.Order.trailValue', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trailSpread', full_name='Trd_Common.Order.trailSpread', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='Trd_Common.Order.currency', index=24,
      number=25, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trdMarket', full_name='Trd_Common.Order.trdMarket', index=25,
      number=26, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='session', full_name='Trd_Common.Order.session', index=26,
      number=27, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1672,
  serialized_end=2189,
)


_ORDERFEEITEM = _descriptor.Descriptor(
  name='OrderFeeItem',
  full_name='Trd_Common.OrderFeeItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='title', full_name='Trd_Common.OrderFeeItem.title', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='Trd_Common.OrderFeeItem.value', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2191,
  serialized_end=2235,
)


_ORDERFEE = _descriptor.Descriptor(
  name='OrderFee',
  full_name='Trd_Common.OrderFee',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='orderIDEx', full_name='Trd_Common.OrderFee.orderIDEx', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='feeAmount', full_name='Trd_Common.OrderFee.feeAmount', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='feeList', full_name='Trd_Common.OrderFee.feeList', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2237,
  serialized_end=2328,
)


_ORDERFILL = _descriptor.Descriptor(
  name='OrderFill',
  full_name='Trd_Common.OrderFill',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trdSide', full_name='Trd_Common.OrderFill.trdSide', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fillID', full_name='Trd_Common.OrderFill.fillID', index=1,
      number=2, type=4, cpp_type=4, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fillIDEx', full_name='Trd_Common.OrderFill.fillIDEx', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderID', full_name='Trd_Common.OrderFill.orderID', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderIDEx', full_name='Trd_Common.OrderFill.orderIDEx', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='Trd_Common.OrderFill.code', index=5,
      number=6, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Trd_Common.OrderFill.name', index=6,
      number=7, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='Trd_Common.OrderFill.qty', index=7,
      number=8, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='Trd_Common.OrderFill.price', index=8,
      number=9, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='createTime', full_name='Trd_Common.OrderFill.createTime', index=9,
      number=10, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='counterBrokerID', full_name='Trd_Common.OrderFill.counterBrokerID', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='counterBrokerName', full_name='Trd_Common.OrderFill.counterBrokerName', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='secMarket', full_name='Trd_Common.OrderFill.secMarket', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='createTimestamp', full_name='Trd_Common.OrderFill.createTimestamp', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updateTimestamp', full_name='Trd_Common.OrderFill.updateTimestamp', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='Trd_Common.OrderFill.status', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trdMarket', full_name='Trd_Common.OrderFill.trdMarket', index=16,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2331,
  serialized_end=2661,
)


_MAXTRDQTYS = _descriptor.Descriptor(
  name='MaxTrdQtys',
  full_name='Trd_Common.MaxTrdQtys',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='maxCashBuy', full_name='Trd_Common.MaxTrdQtys.maxCashBuy', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maxCashAndMarginBuy', full_name='Trd_Common.MaxTrdQtys.maxCashAndMarginBuy', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maxPositionSell', full_name='Trd_Common.MaxTrdQtys.maxPositionSell', index=2,
      number=3, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maxSellShort', full_name='Trd_Common.MaxTrdQtys.maxSellShort', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maxBuyBack', full_name='Trd_Common.MaxTrdQtys.maxBuyBack', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='longRequiredIM', full_name='Trd_Common.MaxTrdQtys.longRequiredIM', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shortRequiredIM', full_name='Trd_Common.MaxTrdQtys.shortRequiredIM', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2664,
  serialized_end=2841,
)


_TRDFILTERCONDITIONS = _descriptor.Descriptor(
  name='TrdFilterConditions',
  full_name='Trd_Common.TrdFilterConditions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='codeList', full_name='Trd_Common.TrdFilterConditions.codeList', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='idList', full_name='Trd_Common.TrdFilterConditions.idList', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='beginTime', full_name='Trd_Common.TrdFilterConditions.beginTime', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endTime', full_name='Trd_Common.TrdFilterConditions.endTime', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderIDExList', full_name='Trd_Common.TrdFilterConditions.orderIDExList', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filterMarket', full_name='Trd_Common.TrdFilterConditions.filterMarket', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2844,
  serialized_end=2980,
)

_FUNDS.fields_by_name['cashInfoList'].message_type = _ACCCASHINFO
_FUNDS.fields_by_name['marketInfoList'].message_type = _ACCMARKETINFO
_ORDERFEE.fields_by_name['feeList'].message_type = _ORDERFEEITEM
DESCRIPTOR.message_types_by_name['AccCashInfo'] = _ACCCASHINFO
DESCRIPTOR.message_types_by_name['AccMarketInfo'] = _ACCMARKETINFO
DESCRIPTOR.message_types_by_name['TrdHeader'] = _TRDHEADER
DESCRIPTOR.message_types_by_name['TrdAcc'] = _TRDACC
DESCRIPTOR.message_types_by_name['Funds'] = _FUNDS
DESCRIPTOR.message_types_by_name['Position'] = _POSITION
DESCRIPTOR.message_types_by_name['Order'] = _ORDER
DESCRIPTOR.message_types_by_name['OrderFeeItem'] = _ORDERFEEITEM
DESCRIPTOR.message_types_by_name['OrderFee'] = _ORDERFEE
DESCRIPTOR.message_types_by_name['OrderFill'] = _ORDERFILL
DESCRIPTOR.message_types_by_name['MaxTrdQtys'] = _MAXTRDQTYS
DESCRIPTOR.message_types_by_name['TrdFilterConditions'] = _TRDFILTERCONDITIONS
DESCRIPTOR.enum_types_by_name['TrdEnv'] = _TRDENV
DESCRIPTOR.enum_types_by_name['TrdCategory'] = _TRDCATEGORY
DESCRIPTOR.enum_types_by_name['TrdMarket'] = _TRDMARKET
DESCRIPTOR.enum_types_by_name['TrdSecMarket'] = _TRDSECMARKET
DESCRIPTOR.enum_types_by_name['TrdSide'] = _TRDSIDE
DESCRIPTOR.enum_types_by_name['OrderType'] = _ORDERTYPE
DESCRIPTOR.enum_types_by_name['TrailType'] = _TRAILTYPE
DESCRIPTOR.enum_types_by_name['OrderStatus'] = _ORDERSTATUS
DESCRIPTOR.enum_types_by_name['OrderFillStatus'] = _ORDERFILLSTATUS
DESCRIPTOR.enum_types_by_name['PositionSide'] = _POSITIONSIDE
DESCRIPTOR.enum_types_by_name['ModifyOrderOp'] = _MODIFYORDEROP
DESCRIPTOR.enum_types_by_name['TrdAccType'] = _TRDACCTYPE
DESCRIPTOR.enum_types_by_name['TrdAccStatus'] = _TRDACCSTATUS
DESCRIPTOR.enum_types_by_name['Currency'] = _CURRENCY
DESCRIPTOR.enum_types_by_name['CltRiskLevel'] = _CLTRISKLEVEL
DESCRIPTOR.enum_types_by_name['TimeInForce'] = _TIMEINFORCE
DESCRIPTOR.enum_types_by_name['SecurityFirm'] = _SECURITYFIRM
DESCRIPTOR.enum_types_by_name['SimAccType'] = _SIMACCTYPE
DESCRIPTOR.enum_types_by_name['CltRiskStatus'] = _CLTRISKSTATUS
DESCRIPTOR.enum_types_by_name['DTStatus'] = _DTSTATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AccCashInfo = _reflection.GeneratedProtocolMessageType('AccCashInfo', (_message.Message,), dict(
  DESCRIPTOR = _ACCCASHINFO,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.AccCashInfo)
  ))
_sym_db.RegisterMessage(AccCashInfo)

AccMarketInfo = _reflection.GeneratedProtocolMessageType('AccMarketInfo', (_message.Message,), dict(
  DESCRIPTOR = _ACCMARKETINFO,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.AccMarketInfo)
  ))
_sym_db.RegisterMessage(AccMarketInfo)

TrdHeader = _reflection.GeneratedProtocolMessageType('TrdHeader', (_message.Message,), dict(
  DESCRIPTOR = _TRDHEADER,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.TrdHeader)
  ))
_sym_db.RegisterMessage(TrdHeader)

TrdAcc = _reflection.GeneratedProtocolMessageType('TrdAcc', (_message.Message,), dict(
  DESCRIPTOR = _TRDACC,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.TrdAcc)
  ))
_sym_db.RegisterMessage(TrdAcc)

Funds = _reflection.GeneratedProtocolMessageType('Funds', (_message.Message,), dict(
  DESCRIPTOR = _FUNDS,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.Funds)
  ))
_sym_db.RegisterMessage(Funds)

Position = _reflection.GeneratedProtocolMessageType('Position', (_message.Message,), dict(
  DESCRIPTOR = _POSITION,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.Position)
  ))
_sym_db.RegisterMessage(Position)

Order = _reflection.GeneratedProtocolMessageType('Order', (_message.Message,), dict(
  DESCRIPTOR = _ORDER,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.Order)
  ))
_sym_db.RegisterMessage(Order)

OrderFeeItem = _reflection.GeneratedProtocolMessageType('OrderFeeItem', (_message.Message,), dict(
  DESCRIPTOR = _ORDERFEEITEM,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.OrderFeeItem)
  ))
_sym_db.RegisterMessage(OrderFeeItem)

OrderFee = _reflection.GeneratedProtocolMessageType('OrderFee', (_message.Message,), dict(
  DESCRIPTOR = _ORDERFEE,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.OrderFee)
  ))
_sym_db.RegisterMessage(OrderFee)

OrderFill = _reflection.GeneratedProtocolMessageType('OrderFill', (_message.Message,), dict(
  DESCRIPTOR = _ORDERFILL,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.OrderFill)
  ))
_sym_db.RegisterMessage(OrderFill)

MaxTrdQtys = _reflection.GeneratedProtocolMessageType('MaxTrdQtys', (_message.Message,), dict(
  DESCRIPTOR = _MAXTRDQTYS,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.MaxTrdQtys)
  ))
_sym_db.RegisterMessage(MaxTrdQtys)

TrdFilterConditions = _reflection.GeneratedProtocolMessageType('TrdFilterConditions', (_message.Message,), dict(
  DESCRIPTOR = _TRDFILTERCONDITIONS,
  __module__ = 'Trd_Common_pb2'
  # @@protoc_insertion_point(class_scope:Trd_Common.TrdFilterConditions)
  ))
_sym_db.RegisterMessage(TrdFilterConditions)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ)github.com/futuopen/ftapi4go/pb/trdcommon'))
# @@protoc_insertion_point(module_scope)
