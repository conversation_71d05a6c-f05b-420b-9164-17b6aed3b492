#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
進階恆生指數期貨實時監控程式
支持實時推送、歷史數據記錄和多種顯示模式

作者: AI Assistant
日期: 2025-01-14
"""

import time
import datetime
import json
import os
from futu import *
from config import *

class AdvancedHSIMonitor:
    """進階恆生指數期貨監控器"""
    
    def __init__(self):
        self.quote_ctx = None
        self.is_connected = False
        self.is_monitoring = False
        self.price_history = []
        self.last_price = None
        
    def connect_to_opend(self):
        """連接到 OpenD"""
        try:
            self.quote_ctx = OpenQuoteContext(
                host=OPEND_CONFIG["host"], 
                port=OPEND_CONFIG["port"]
            )
            
            # 設置推送處理器
            self.quote_ctx.set_handler(HSIQuoteHandler(self))
            
            self.is_connected = True
            print(f"✅ 成功連接到 OpenD ({OPEND_CONFIG['host']}:{OPEND_CONFIG['port']})")
            return True
            
        except Exception as e:
            print(f"❌ 連接失敗: {e}")
            return False
    
    def subscribe_hsi_futures(self):
        """訂閱恆生指數期貨"""
        try:
            hsi_code = FUTURES_CODES["HSI_MAIN"]
            
            ret_sub, err_message = self.quote_ctx.subscribe(
                [hsi_code], 
                [SubType.QUOTE], 
                subscribe_push=MONITOR_CONFIG["enable_push"]
            )
            
            if ret_sub == RET_OK:
                print(f"✅ 成功訂閱 {hsi_code} 實時行情")
                return True
            else:
                print(f"❌ 訂閱失敗: {err_message}")
                return False
                
        except Exception as e:
            print(f"❌ 訂閱過程發生錯誤: {e}")
            return False
    
    def get_current_quote(self):
        """獲取當前報價"""
        try:
            hsi_code = FUTURES_CODES["HSI_MAIN"]
            ret, data = self.quote_ctx.get_stock_quote([hsi_code])
            
            if ret == RET_OK and not data.empty:
                quote_data = data.iloc[0].to_dict()
                quote_data['timestamp'] = datetime.datetime.now()
                return quote_data
            else:
                print(f"❌ 獲取報價失敗: {data}")
                return None
                
        except Exception as e:
            print(f"❌ 獲取報價時發生錯誤: {e}")
            return None
    
    def display_quote_table(self, quote_data):
        """以表格形式顯示報價"""
        if not quote_data:
            return
            
        current_time = quote_data['timestamp'].strftime("%Y-%m-%d %H:%M:%S")
        
        # 計算漲跌
        change = quote_data['last_price'] - quote_data['prev_close_price']
        change_percent = (change / quote_data['prev_close_price']) * 100
        
        # 選擇顯示符號
        if change > 0:
            trend_symbol = "📈"
            color_code = "\033[92m"  # 綠色
        elif change < 0:
            trend_symbol = "📉"
            color_code = "\033[91m"  # 紅色
        else:
            trend_symbol = "➡️"
            color_code = "\033[93m"  # 黃色
        
        reset_color = "\033[0m"
        
        print("\n" + "="*70)
        print(f"📊 恆生指數期貨實時行情 - {current_time}")
        print("="*70)
        print(f"│ 代碼     │ {quote_data['code']:<20} │")
        print(f"│ 名稱     │ {quote_data['name']:<20} │")
        print(f"│ 最新價   │ {color_code}{quote_data['last_price']:>8.1f}{reset_color}            │")
        print(f"│ 開盤價   │ {quote_data['open_price']:>8.1f}            │")
        print(f"│ 最高價   │ {quote_data['high_price']:>8.1f}            │")
        print(f"│ 最低價   │ {quote_data['low_price']:>8.1f}            │")
        print(f"│ 昨收價   │ {quote_data['prev_close_price']:>8.1f}            │")
        print(f"│ 漲跌額   │ {color_code}{trend_symbol} {change:>+7.1f}{reset_color}            │")
        print(f"│ 漲跌幅   │ {color_code}{trend_symbol} {change_percent:>+6.2f}%{reset_color}           │")
        print(f"│ 成交量   │ {quote_data['volume']:>12,}      │")
        print(f"│ 成交額   │ {quote_data['turnover']:>12,.0f}      │")
        print(f"│ 數據時間 │ {quote_data['data_time']:<20} │")
        print("="*70)
    
    def display_quote_simple(self, quote_data):
        """簡單格式顯示報價"""
        if not quote_data:
            return
            
        current_time = quote_data['timestamp'].strftime("%H:%M:%S")
        change = quote_data['last_price'] - quote_data['prev_close_price']
        change_percent = (change / quote_data['prev_close_price']) * 100
        
        trend_symbol = "📈" if change > 0 else "📉" if change < 0 else "➡️"
        
        print(f"[{current_time}] 恆指期貨: {quote_data['last_price']:.1f} "
              f"({trend_symbol}{change:+.1f}, {change_percent:+.2f}%) "
              f"成交量: {quote_data['volume']:,}")
    
    def save_price_history(self, quote_data):
        """保存價格歷史"""
        if not quote_data:
            return
            
        history_entry = {
            'timestamp': quote_data['timestamp'].isoformat(),
            'price': quote_data['last_price'],
            'volume': quote_data['volume'],
            'change': quote_data['last_price'] - quote_data['prev_close_price']
        }
        
        self.price_history.append(history_entry)
        
        # 保持最近100條記錄
        if len(self.price_history) > 100:
            self.price_history = self.price_history[-100:]
    
    def export_history_to_file(self):
        """導出歷史數據到文件"""
        if not self.price_history:
            print("📝 沒有歷史數據可導出")
            return
            
        filename = f"hsi_history_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.price_history, f, ensure_ascii=False, indent=2)
            print(f"📁 歷史數據已導出到: {filename}")
        except Exception as e:
            print(f"❌ 導出失敗: {e}")
    
    def start_monitoring(self):
        """開始監控"""
        if not self.connect_to_opend():
            return
            
        if not self.subscribe_hsi_futures():
            return
            
        self.is_monitoring = True
        refresh_interval = MONITOR_CONFIG["refresh_interval"]
        display_format = MONITOR_CONFIG["display_format"]
        
        print(f"\n🚀 開始監控恆生指數期貨")
        print(f"📱 刷新間隔: {refresh_interval} 秒")
        print(f"🎨 顯示格式: {display_format}")
        print(f"📡 實時推送: {'啟用' if MONITOR_CONFIG['enable_push'] else '禁用'}")
        print("⏹️  按 Ctrl+C 停止監控\n")
        
        try:
            while self.is_monitoring:
                quote_data = self.get_current_quote()
                
                if quote_data:
                    # 顯示報價
                    if display_format == "table":
                        self.display_quote_table(quote_data)
                    else:
                        self.display_quote_simple(quote_data)
                    
                    # 保存歷史
                    self.save_price_history(quote_data)
                    self.last_price = quote_data['last_price']
                
                time.sleep(refresh_interval)
                
        except KeyboardInterrupt:
            print("\n\n⏹️  用戶中斷，停止監控...")
        except Exception as e:
            print(f"\n❌ 監控過程中發生錯誤: {e}")
        finally:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止監控"""
        self.is_monitoring = False
        
        if self.quote_ctx and self.is_connected:
            try:
                # 取消訂閱
                hsi_code = FUTURES_CODES["HSI_MAIN"]
                self.quote_ctx.unsubscribe([hsi_code], [SubType.QUOTE])
                self.quote_ctx.close()
                print("✅ 已斷開連接並清理資源")
            except Exception as e:
                print(f"⚠️  清理資源時發生錯誤: {e}")
        
        # 詢問是否導出歷史數據
        if self.price_history:
            try:
                export = input("\n📊 是否導出價格歷史數據? (y/n): ").strip().lower()
                if export == 'y':
                    self.export_history_to_file()
            except:
                pass


class HSIQuoteHandler(StockQuoteHandlerBase):
    """恆指期貨報價推送處理器"""
    
    def __init__(self, monitor):
        super().__init__()
        self.monitor = monitor
    
    def on_recv_rsp(self, rsp_pb):
        """接收實時報價推送"""
        ret_code, data = super().on_recv_rsp(rsp_pb)
        
        if ret_code != RET_OK:
            print(f"❌ 推送數據錯誤: {data}")
            return RET_ERROR, data
        
        # 處理推送數據
        for index, row in data.iterrows():
            if row['code'] == FUTURES_CODES["HSI_MAIN"]:
                current_time = datetime.datetime.now().strftime("%H:%M:%S")
                
                # 計算價格變化
                if self.monitor.last_price:
                    price_change = row['last_price'] - self.monitor.last_price
                    if abs(price_change) >= 1:  # 只在價格變化超過1點時顯示推送
                        change_symbol = "📈" if price_change > 0 else "📉"
                        print(f"\n🔔 [{current_time}] 價格更新: {row['last_price']:.1f} "
                              f"({change_symbol}{price_change:+.1f})")
                
                self.monitor.last_price = row['last_price']
        
        return RET_OK, data


def main():
    """主函數"""
    print("🎯 進階恆生指數期貨實時監控程式")
    print("=" * 60)
    print("📋 功能特色:")
    print("   ✅ 實時價格監控")
    print("   ✅ 實時推送通知")
    print("   ✅ 價格歷史記錄")
    print("   ✅ 多種顯示格式")
    print("   ✅ 數據導出功能")
    print("=" * 60)
    print(f"🔧 當前配置:")
    print(f"   - 牛牛號: {FUTU_ACCOUNT['user_id']}")
    print(f"   - OpenD: {OPEND_CONFIG['host']}:{OPEND_CONFIG['port']}")
    print(f"   - 監控標的: {FUTURES_CODES['HSI_MAIN']}")
    print(f"   - 刷新間隔: {MONITOR_CONFIG['refresh_interval']} 秒")
    print("=" * 60)
    
    input("\n按 Enter 鍵開始監控...")
    
    # 創建監控器並開始監控
    monitor = AdvancedHSIMonitor()
    monitor.start_monitoring()


if __name__ == "__main__":
    main()
