# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Qot_GetFutureInfo.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Common_pb2 as Common__pb2
import Qot_Common_pb2 as Qot__Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Qot_GetFutureInfo.proto',
  package='Qot_GetFutureInfo',
  syntax='proto2',
  serialized_pb=_b('\n\x17Qot_GetFutureInfo.proto\x12\x11Qot_GetFutureInfo\x1a\x0c\x43ommon.proto\x1a\x10Qot_Common.proto\"\'\n\tTradeTime\x12\r\n\x05\x62\x65gin\x18\x01 \x01(\x01\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x01\"\xd8\x03\n\nFutureInfo\x12\x0c\n\x04name\x18\x01 \x02(\t\x12&\n\x08security\x18\x02 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x15\n\rlastTradeTime\x18\x03 \x02(\t\x12\x1a\n\x12lastTradeTimestamp\x18\x04 \x01(\x01\x12#\n\x05owner\x18\x05 \x01(\x0b\x32\x14.Qot_Common.Security\x12\x12\n\nownerOther\x18\x06 \x02(\t\x12\x10\n\x08\x65xchange\x18\x07 \x02(\t\x12\x14\n\x0c\x63ontractType\x18\x08 \x02(\t\x12\x14\n\x0c\x63ontractSize\x18\t \x02(\x01\x12\x18\n\x10\x63ontractSizeUnit\x18\n \x02(\t\x12\x15\n\rquoteCurrency\x18\x0b \x02(\t\x12\x0e\n\x06minVar\x18\x0c \x02(\x01\x12\x12\n\nminVarUnit\x18\r \x02(\t\x12\x11\n\tquoteUnit\x18\x0e \x01(\t\x12/\n\ttradeTime\x18\x0f \x03(\x0b\x32\x1c.Qot_GetFutureInfo.TradeTime\x12\x10\n\x08timeZone\x18\x10 \x02(\t\x12\x19\n\x11\x65xchangeFormatUrl\x18\x11 \x02(\t\x12$\n\x06origin\x18\x12 \x01(\x0b\x32\x14.Qot_Common.Security\"1\n\x03\x43\x32S\x12*\n\x0csecurityList\x18\x01 \x03(\x0b\x32\x14.Qot_Common.Security\"<\n\x03S2C\x12\x35\n\x0e\x66utureInfoList\x18\x01 \x03(\x0b\x32\x1d.Qot_GetFutureInfo.FutureInfo\".\n\x07Request\x12#\n\x03\x63\x32s\x18\x01 \x02(\x0b\x32\x16.Qot_GetFutureInfo.C2S\"g\n\x08Response\x12\x15\n\x07retType\x18\x01 \x02(\x05:\x04-400\x12\x0e\n\x06retMsg\x18\x02 \x01(\t\x12\x0f\n\x07\x65rrCode\x18\x03 \x01(\x05\x12#\n\x03s2c\x18\x04 \x01(\x0b\x32\x16.Qot_GetFutureInfo.S2CBG\n\x13\x63om.futu.openapi.pbZ0github.com/futuopen/ftapi4go/pb/qotgetfutureinfo')
  ,
  dependencies=[Common__pb2.DESCRIPTOR,Qot__Common__pb2.DESCRIPTOR,])




_TRADETIME = _descriptor.Descriptor(
  name='TradeTime',
  full_name='Qot_GetFutureInfo.TradeTime',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='begin', full_name='Qot_GetFutureInfo.TradeTime.begin', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='Qot_GetFutureInfo.TradeTime.end', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=78,
  serialized_end=117,
)


_FUTUREINFO = _descriptor.Descriptor(
  name='FutureInfo',
  full_name='Qot_GetFutureInfo.FutureInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='Qot_GetFutureInfo.FutureInfo.name', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='security', full_name='Qot_GetFutureInfo.FutureInfo.security', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastTradeTime', full_name='Qot_GetFutureInfo.FutureInfo.lastTradeTime', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastTradeTimestamp', full_name='Qot_GetFutureInfo.FutureInfo.lastTradeTimestamp', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='owner', full_name='Qot_GetFutureInfo.FutureInfo.owner', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ownerOther', full_name='Qot_GetFutureInfo.FutureInfo.ownerOther', index=5,
      number=6, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exchange', full_name='Qot_GetFutureInfo.FutureInfo.exchange', index=6,
      number=7, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractType', full_name='Qot_GetFutureInfo.FutureInfo.contractType', index=7,
      number=8, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractSize', full_name='Qot_GetFutureInfo.FutureInfo.contractSize', index=8,
      number=9, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractSizeUnit', full_name='Qot_GetFutureInfo.FutureInfo.contractSizeUnit', index=9,
      number=10, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quoteCurrency', full_name='Qot_GetFutureInfo.FutureInfo.quoteCurrency', index=10,
      number=11, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='minVar', full_name='Qot_GetFutureInfo.FutureInfo.minVar', index=11,
      number=12, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='minVarUnit', full_name='Qot_GetFutureInfo.FutureInfo.minVarUnit', index=12,
      number=13, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quoteUnit', full_name='Qot_GetFutureInfo.FutureInfo.quoteUnit', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tradeTime', full_name='Qot_GetFutureInfo.FutureInfo.tradeTime', index=14,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timeZone', full_name='Qot_GetFutureInfo.FutureInfo.timeZone', index=15,
      number=16, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exchangeFormatUrl', full_name='Qot_GetFutureInfo.FutureInfo.exchangeFormatUrl', index=16,
      number=17, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin', full_name='Qot_GetFutureInfo.FutureInfo.origin', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=120,
  serialized_end=592,
)


_C2S = _descriptor.Descriptor(
  name='C2S',
  full_name='Qot_GetFutureInfo.C2S',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='securityList', full_name='Qot_GetFutureInfo.C2S.securityList', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=594,
  serialized_end=643,
)


_S2C = _descriptor.Descriptor(
  name='S2C',
  full_name='Qot_GetFutureInfo.S2C',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='futureInfoList', full_name='Qot_GetFutureInfo.S2C.futureInfoList', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=645,
  serialized_end=705,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='Qot_GetFutureInfo.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='c2s', full_name='Qot_GetFutureInfo.Request.c2s', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=707,
  serialized_end=753,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Qot_GetFutureInfo.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='retType', full_name='Qot_GetFutureInfo.Response.retType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=True, default_value=-400,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retMsg', full_name='Qot_GetFutureInfo.Response.retMsg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errCode', full_name='Qot_GetFutureInfo.Response.errCode', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s2c', full_name='Qot_GetFutureInfo.Response.s2c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=755,
  serialized_end=858,
)

_FUTUREINFO.fields_by_name['security'].message_type = Qot__Common__pb2._SECURITY
_FUTUREINFO.fields_by_name['owner'].message_type = Qot__Common__pb2._SECURITY
_FUTUREINFO.fields_by_name['tradeTime'].message_type = _TRADETIME
_FUTUREINFO.fields_by_name['origin'].message_type = Qot__Common__pb2._SECURITY
_C2S.fields_by_name['securityList'].message_type = Qot__Common__pb2._SECURITY
_S2C.fields_by_name['futureInfoList'].message_type = _FUTUREINFO
_REQUEST.fields_by_name['c2s'].message_type = _C2S
_RESPONSE.fields_by_name['s2c'].message_type = _S2C
DESCRIPTOR.message_types_by_name['TradeTime'] = _TRADETIME
DESCRIPTOR.message_types_by_name['FutureInfo'] = _FUTUREINFO
DESCRIPTOR.message_types_by_name['C2S'] = _C2S
DESCRIPTOR.message_types_by_name['S2C'] = _S2C
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TradeTime = _reflection.GeneratedProtocolMessageType('TradeTime', (_message.Message,), dict(
  DESCRIPTOR = _TRADETIME,
  __module__ = 'Qot_GetFutureInfo_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetFutureInfo.TradeTime)
  ))
_sym_db.RegisterMessage(TradeTime)

FutureInfo = _reflection.GeneratedProtocolMessageType('FutureInfo', (_message.Message,), dict(
  DESCRIPTOR = _FUTUREINFO,
  __module__ = 'Qot_GetFutureInfo_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetFutureInfo.FutureInfo)
  ))
_sym_db.RegisterMessage(FutureInfo)

C2S = _reflection.GeneratedProtocolMessageType('C2S', (_message.Message,), dict(
  DESCRIPTOR = _C2S,
  __module__ = 'Qot_GetFutureInfo_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetFutureInfo.C2S)
  ))
_sym_db.RegisterMessage(C2S)

S2C = _reflection.GeneratedProtocolMessageType('S2C', (_message.Message,), dict(
  DESCRIPTOR = _S2C,
  __module__ = 'Qot_GetFutureInfo_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetFutureInfo.S2C)
  ))
_sym_db.RegisterMessage(S2C)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'Qot_GetFutureInfo_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetFutureInfo.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'Qot_GetFutureInfo_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetFutureInfo.Response)
  ))
_sym_db.RegisterMessage(Response)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ0github.com/futuopen/ftapi4go/pb/qotgetfutureinfo'))
# @@protoc_insertion_point(module_scope)
