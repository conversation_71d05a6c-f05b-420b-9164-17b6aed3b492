syntax = "proto2";
package Trd_SubAccPush;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/trdsubaccpush";

import "Common.proto";

message C2S
{
	repeated uint64 accIDList = 1; //要接收推送数据的业务账号列表，全量非增量，即使用者请每次传需要接收推送数据的所有业务账号
}

message S2C
{
	
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	//以下3个字段每条协议都有，注释说明在InitConnect.proto中
	required int32 retType = 1 [default = -400];
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
