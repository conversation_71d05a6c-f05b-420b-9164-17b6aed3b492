# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Trd_FlowSummary.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Trd_Common_pb2 as Trd__Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Trd_FlowSummary.proto',
  package='Trd_FlowSummary',
  syntax='proto2',
  serialized_pb=_b('\n\x15Trd_FlowSummary.proto\x12\x0fTrd_FlowSummary\x1a\x10Trd_Common.proto\"\xc6\x01\n\x0f\x46lowSummaryInfo\x12\x14\n\x0c\x63learingDate\x18\x01 \x01(\t\x12\x16\n\x0esettlementDate\x18\x02 \x01(\t\x12\x10\n\x08\x63urrency\x18\x03 \x01(\x05\x12\x14\n\x0c\x63\x61shFlowType\x18\x04 \x01(\t\x12\x19\n\x11\x63\x61shFlowDirection\x18\x05 \x01(\x05\x12\x16\n\x0e\x63\x61shFlowAmount\x18\x06 \x01(\x01\x12\x16\n\x0e\x63\x61shFlowRemark\x18\x07 \x01(\t\x12\x12\n\ncashFlowID\x18\x08 \x01(\x04\"]\n\x03\x43\x32S\x12%\n\x06header\x18\x01 \x02(\x0b\x32\x15.Trd_Common.TrdHeader\x12\x14\n\x0c\x63learingDate\x18\x02 \x02(\t\x12\x19\n\x11\x63\x61shFlowDirection\x18\x03 \x01(\x05\"k\n\x03S2C\x12%\n\x06header\x18\x01 \x02(\x0b\x32\x15.Trd_Common.TrdHeader\x12=\n\x13\x66lowSummaryInfoList\x18\x02 \x03(\x0b\x32 .Trd_FlowSummary.FlowSummaryInfo\",\n\x07Request\x12!\n\x03\x63\x32s\x18\x01 \x02(\x0b\x32\x14.Trd_FlowSummary.C2S\"e\n\x08Response\x12\x15\n\x07retType\x18\x01 \x02(\x05:\x04-400\x12\x0e\n\x06retMsg\x18\x02 \x01(\t\x12\x0f\n\x07\x65rrCode\x18\x03 \x01(\x05\x12!\n\x03s2c\x18\x04 \x01(\x0b\x32\x14.Trd_FlowSummary.S2C*s\n\x14TrdCashFlowDirection\x12 \n\x1cTrdCashFlowDirection_Unknown\x10\x00\x12\x1b\n\x17TrdCashFlowDirection_In\x10\x01\x12\x1c\n\x18TrdCashFlowDirection_Out\x10\x02\x42\x45\n\x13\x63om.futu.openapi.pbZ.github.com/futuopen/ftapi4go/pb/trdflowsummary')
  ,
  dependencies=[Trd__Common__pb2.DESCRIPTOR,])

_TRDCASHFLOWDIRECTION = _descriptor.EnumDescriptor(
  name='TrdCashFlowDirection',
  full_name='Trd_FlowSummary.TrdCashFlowDirection',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TrdCashFlowDirection_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdCashFlowDirection_In', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TrdCashFlowDirection_Out', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=614,
  serialized_end=729,
)
_sym_db.RegisterEnumDescriptor(_TRDCASHFLOWDIRECTION)

TrdCashFlowDirection = enum_type_wrapper.EnumTypeWrapper(_TRDCASHFLOWDIRECTION)
TrdCashFlowDirection_Unknown = 0
TrdCashFlowDirection_In = 1
TrdCashFlowDirection_Out = 2



_FLOWSUMMARYINFO = _descriptor.Descriptor(
  name='FlowSummaryInfo',
  full_name='Trd_FlowSummary.FlowSummaryInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='clearingDate', full_name='Trd_FlowSummary.FlowSummaryInfo.clearingDate', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='settlementDate', full_name='Trd_FlowSummary.FlowSummaryInfo.settlementDate', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='Trd_FlowSummary.FlowSummaryInfo.currency', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cashFlowType', full_name='Trd_FlowSummary.FlowSummaryInfo.cashFlowType', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cashFlowDirection', full_name='Trd_FlowSummary.FlowSummaryInfo.cashFlowDirection', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cashFlowAmount', full_name='Trd_FlowSummary.FlowSummaryInfo.cashFlowAmount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cashFlowRemark', full_name='Trd_FlowSummary.FlowSummaryInfo.cashFlowRemark', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cashFlowID', full_name='Trd_FlowSummary.FlowSummaryInfo.cashFlowID', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=61,
  serialized_end=259,
)


_C2S = _descriptor.Descriptor(
  name='C2S',
  full_name='Trd_FlowSummary.C2S',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='Trd_FlowSummary.C2S.header', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='clearingDate', full_name='Trd_FlowSummary.C2S.clearingDate', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cashFlowDirection', full_name='Trd_FlowSummary.C2S.cashFlowDirection', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=261,
  serialized_end=354,
)


_S2C = _descriptor.Descriptor(
  name='S2C',
  full_name='Trd_FlowSummary.S2C',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='Trd_FlowSummary.S2C.header', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='flowSummaryInfoList', full_name='Trd_FlowSummary.S2C.flowSummaryInfoList', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=356,
  serialized_end=463,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='Trd_FlowSummary.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='c2s', full_name='Trd_FlowSummary.Request.c2s', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=465,
  serialized_end=509,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Trd_FlowSummary.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='retType', full_name='Trd_FlowSummary.Response.retType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=True, default_value=-400,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retMsg', full_name='Trd_FlowSummary.Response.retMsg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errCode', full_name='Trd_FlowSummary.Response.errCode', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s2c', full_name='Trd_FlowSummary.Response.s2c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=511,
  serialized_end=612,
)

_C2S.fields_by_name['header'].message_type = Trd__Common__pb2._TRDHEADER
_S2C.fields_by_name['header'].message_type = Trd__Common__pb2._TRDHEADER
_S2C.fields_by_name['flowSummaryInfoList'].message_type = _FLOWSUMMARYINFO
_REQUEST.fields_by_name['c2s'].message_type = _C2S
_RESPONSE.fields_by_name['s2c'].message_type = _S2C
DESCRIPTOR.message_types_by_name['FlowSummaryInfo'] = _FLOWSUMMARYINFO
DESCRIPTOR.message_types_by_name['C2S'] = _C2S
DESCRIPTOR.message_types_by_name['S2C'] = _S2C
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.enum_types_by_name['TrdCashFlowDirection'] = _TRDCASHFLOWDIRECTION
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

FlowSummaryInfo = _reflection.GeneratedProtocolMessageType('FlowSummaryInfo', (_message.Message,), dict(
  DESCRIPTOR = _FLOWSUMMARYINFO,
  __module__ = 'Trd_FlowSummary_pb2'
  # @@protoc_insertion_point(class_scope:Trd_FlowSummary.FlowSummaryInfo)
  ))
_sym_db.RegisterMessage(FlowSummaryInfo)

C2S = _reflection.GeneratedProtocolMessageType('C2S', (_message.Message,), dict(
  DESCRIPTOR = _C2S,
  __module__ = 'Trd_FlowSummary_pb2'
  # @@protoc_insertion_point(class_scope:Trd_FlowSummary.C2S)
  ))
_sym_db.RegisterMessage(C2S)

S2C = _reflection.GeneratedProtocolMessageType('S2C', (_message.Message,), dict(
  DESCRIPTOR = _S2C,
  __module__ = 'Trd_FlowSummary_pb2'
  # @@protoc_insertion_point(class_scope:Trd_FlowSummary.S2C)
  ))
_sym_db.RegisterMessage(S2C)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'Trd_FlowSummary_pb2'
  # @@protoc_insertion_point(class_scope:Trd_FlowSummary.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'Trd_FlowSummary_pb2'
  # @@protoc_insertion_point(class_scope:Trd_FlowSummary.Response)
  ))
_sym_db.RegisterMessage(Response)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ.github.com/futuopen/ftapi4go/pb/trdflowsummary'))
# @@protoc_insertion_point(module_scope)
