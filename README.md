# 恆生指數期貨實時監控程式

這是一個使用 FUTU API 獲取恆生指數期貨實時價格的 Python 程式集合。

## 📋 功能特色

- ✅ 實時獲取恆生指數期貨價格
- ✅ 支持實時推送通知
- ✅ 多種顯示格式（表格/簡潔）
- ✅ 價格歷史記錄
- ✅ 數據導出功能
- ✅ 完善的錯誤處理

## 🔧 環境要求

### 軟體要求
- Python 3.6 或以上版本
- FUTU OpenD 程式
- futu-api Python 套件

### 帳戶要求
- 富途牛牛帳號：22188140
- 期貨 LV1 權限
- FUTU OpenD WebSocket密鑰：3e8229abd3ccdfdc

## 📦 安裝步驟

### 1. 安裝 Python 依賴
```bash
pip install futu-api
```

### 2. 下載並啟動 FUTU OpenD
1. 從富途官網下載 OpenD 程式
2. 啟動 OpenD 並使用您的帳號登錄
3. 確保 OpenD 運行在 127.0.0.1:11111

### 3. 驗證權限
確保您的帳號具有：
- 期貨 LV1 權限
- 可以訂閱恆生指數期貨數據

## 🚀 使用方法

### 程式文件說明

1. **simple_hsi_monitor.py** - 簡易版本
   - 基本的價格獲取功能
   - 適合初學者使用
   - 支持單次查詢和持續監控

2. **hsi_futures_realtime.py** - 標準版本
   - 完整的實時監控功能
   - 支持實時推送
   - 面向對象設計

3. **advanced_hsi_monitor.py** - 進階版本
   - 最完整的功能集
   - 支持歷史數據記錄
   - 多種顯示格式
   - 數據導出功能

4. **config.py** - 配置文件
   - 包含帳戶信息
   - 連接設置
   - 監控參數

### 運行程式

#### 簡易版本（推薦新手）
```bash
python simple_hsi_monitor.py
```

#### 進階版本（推薦日常使用）
```bash
python advanced_hsi_monitor.py
```

## 📊 程式輸出示例

### 表格格式顯示
```
======================================================================
📊 恆生指數期貨實時行情 - 2025-01-14 15:30:25
======================================================================
│ 代碼     │ HK.HSImain           │
│ 名稱     │ 恒指期货主连         │
│ 最新價   │  19850.0            │
│ 開盤價   │  19820.0            │
│ 最高價   │  19880.0            │
│ 最低價   │  19800.0            │
│ 昨收價   │  19830.0            │
│ 漲跌額   │ 📈   +20.0            │
│ 漲跌幅   │ 📈  +0.10%           │
│ 成交量   │      125,430      │
│ 成交額   │ 2,487,650,000      │
│ 數據時間 │ 2025-01-14 15:30:20  │
======================================================================
```

### 實時推送通知
```
🔔 [15:30:25] 價格更新: 19852.0 (📈+2.0)
🔔 [15:30:28] 價格更新: 19848.0 (📉-4.0)
```

## ⚙️ 配置說明

### 修改監控參數
編輯 `config.py` 文件：

```python
# 監控設置
MONITOR_CONFIG = {
    "refresh_interval": 5,     # 刷新間隔（秒）
    "enable_push": True,       # 是否啟用推送
    "display_format": "table"  # 顯示格式: table, simple
}
```

### 更改期貨合約
```python
# 期貨代碼配置
FUTURES_CODES = {
    "HSI_MAIN": "HK.HSImain",      # 恆生指數期貨主連
    "HSI_CURRENT": "HK.HSI2501",   # 恆生指數期貨當月合約
    "MHI_MAIN": "HK.MHImain",      # 小型恆生指數期貨主連
}
```

## 🔍 故障排除

### 常見問題

1. **連接失敗**
   - 確保 OpenD 已啟動並登錄
   - 檢查防火牆設置
   - 確認端口 11111 未被占用

2. **訂閱失敗**
   - 確認帳號有期貨 LV1 權限
   - 檢查網絡連接
   - 確認期貨代碼正確

3. **無數據推送**
   - 確認市場開盤時間
   - 檢查訂閱狀態
   - 確認推送設置已啟用

### 錯誤代碼說明
- `RET_OK`: 操作成功
- `RET_ERROR`: 一般錯誤
- 其他錯誤代碼請參考 FUTU API 文檔

## 📈 市場時間

恆生指數期貨交易時間（香港時間）：
- 日盤：09:15 - 12:00, 13:00 - 16:30
- 夜盤：17:15 - 次日 03:00

## ⚠️ 注意事項

1. **數據使用**
   - 本程式僅供學習和個人使用
   - 請勿用於商業用途
   - 投資有風險，請謹慎決策

2. **API 限制**
   - 遵守 FUTU API 使用條款
   - 注意請求頻率限制
   - 避免過度頻繁的數據請求

3. **安全提醒**
   - 保護好您的帳號信息
   - 定期更改密碼
   - 不要在公共場所運行程式

## 📞 技術支持

如遇到技術問題，請：
1. 檢查 FUTU OpenD 日誌
2. 確認網絡連接狀態
3. 查看程式錯誤輸出
4. 參考 FUTU API 官方文檔

## 📄 許可證

本程式僅供學習和研究使用，請遵守相關法律法規和 FUTU API 使用條款。
