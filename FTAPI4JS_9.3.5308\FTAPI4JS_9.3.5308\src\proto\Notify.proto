syntax = "proto2";
package Notify;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/notify";

import "Common.proto";

enum NotifyType
{
	NotifyType_None = 0; //无
	NotifyType_GtwEvent = 1; //OpenD运行事件通知
	NotifyType_ProgramStatus = 2; //程序状态
	NotifyType_ConnStatus = 3; //连接状态
	NotifyType_QotRight = 4; //行情权限
	NotifyType_APILevel = 5; //用户等级，已在2.10版本之后废弃
	NotifyType_APIQuota = 6; //API额度
	NotifyType_UsedQuota = 7; // 已使用额度
}

enum GtwEventType
{
	GtwEventType_None = 0; //正常无错
	GtwEventType_LocalCfgLoadFailed	= 1; //加载本地配置失败
	GtwEventType_APISvrRunFailed = 2; //服务器启动失败
	GtwEventType_ForceUpdate = 3; //客户端版本过低
	GtwEventType_LoginFailed = 4; //登录失败
	GtwEventType_UnAgreeDisclaimer = 5; //未同意免责声明
	GtwEventType_NetCfgMissing = 6; //缺少必要网络配置信息;例如控制订阅额度 //已优化，不会再出现该情况
	GtwEventType_KickedOut = 7; //牛牛帐号在别处登录
	GtwEventType_LoginPwdChanged = 8; //登录密码被修改
	GtwEventType_BanLogin = 9; //用户被禁止登录
	GtwEventType_NeedPicVerifyCode = 10; //需要图形验证码
	GtwEventType_NeedPhoneVerifyCode = 11; //需要手机验证码
	GtwEventType_AppDataNotExist = 12; //程序自带数据不存在
	GtwEventType_NessaryDataMissing = 13; //缺少必要数据
	GtwEventType_TradePwdChanged = 14; //交易密码被修改
	GtwEventType_EnableDeviceLock = 15; //启用设备锁
}

message GtwEvent
{
	required int32 eventType = 1; //GtwEventType,事件类型
	required string desc = 2; //事件描述
}

message ProgramStatus
{
	required Common.ProgramStatus programStatus = 1; //当前程序状态
}

message ConnectStatus
{
	required bool qotLogined = 1; //是否登陆行情服务器
	required bool trdLogined = 2; //是否登陆交易服务器
}

message QotRight
{
	required int32 hkQotRight = 4; //港股行情权限, Qot_Common.QotRight
	required int32 usQotRight = 5; //美股行情权限, Qot_Common.QotRight
	required int32 cnQotRight = 6; //A股行情权限, Qot_Common.QotRight // (已废弃)，使用shQotRight和szQotRight
	optional int32 hkOptionQotRight = 7; //港股期权行情权限, Qot_Common.QotRight
	optional bool hasUSOptionQotRight = 8; //是否有美股期权行情权限
	optional int32 hkFutureQotRight = 9; //港股期货行情权限, Qot_Common.QotRight
	optional int32 usFutureQotRight = 10; //美股期货行情权限, Qot_Common.QotRight(已废弃)
	optional int32 usOptionQotRight = 11; //美股期权行情权限, Qot_Common.QotRight
	optional int32 usIndexQotRight = 12; //美股指数行情权限, Qot_Common.QotRight
	optional int32 usOtcQotRight = 13; //美股OTC市场行情权限, Qot_Common.QotRight
	optional int32 sgFutureQotRight = 14; //新加坡市场期货行情权限, Qot_Common.QotRight
	optional int32 jpFutureQotRight = 15; //日本市场期货行情权限, Qot_Common.QotRight
	optional int32 usCMEFutureQotRight = 16; //美股CME期货行情权限, Qot_Common.QotRight
	optional int32 usCBOTFutureQotRight = 17; //美股CBOT期货行情权限, Qot_Common.QotRight
	optional int32 usNYMEXFutureQotRight = 18; //美股NYMEX期货行情权限, Qot_Common.QotRight
	optional int32 usCOMEXFutureQotRight = 19; //美股COMEX期货行情权限, Qot_Common.QotRight
	optional int32 usCBOEFutureQotRight = 20; //美股CBOE期货行情权限, Qot_Common.QotRight
	optional int32 shQotRight = 21; //上海A股行情权限, Qot_Common.QotRight
	optional int32 szQotRight = 22; //深圳A股行情权限, Qot_Common.QotRight
}

message APILevel
{
	required string apiLevel = 3; //api用户等级描述，已在2.10版本之后废弃
}

message APIQuota
{
	required int32 subQuota = 1; //订阅额度
	required int32 historyKLQuota = 2; //历史K线额度
}

message UsedQuota
{
	optional int32 usedSubQuota = 1; // 已使用订阅额度
	optional int32 usedKLineQuota = 2; // 已使用历史 K 线额度
}

message S2C
{
	required int32 type = 1; //通知类型
	optional GtwEvent event = 2; //事件通息
	optional ProgramStatus programStatus = 3; //程序状态
	optional ConnectStatus connectStatus = 4; //连接状态
	optional QotRight qotRight = 5; //行情权限
	optional APILevel apiLevel = 6; //用户等级，已在2.10版本之后废弃
	optional APIQuota apiQuota = 7; //API额度
	optional UsedQuota usedQuota = 8; // 已使用额度
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
