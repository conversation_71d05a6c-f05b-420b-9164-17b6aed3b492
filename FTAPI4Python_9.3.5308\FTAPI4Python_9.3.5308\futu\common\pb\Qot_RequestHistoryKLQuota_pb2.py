# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Qot_RequestHistoryKLQuota.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Common_pb2 as Common__pb2
import Qot_Common_pb2 as Qot__Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Qot_RequestHistoryKLQuota.proto',
  package='Qot_RequestHistoryKLQuota',
  syntax='proto2',
  serialized_pb=_b('\n\x1fQot_RequestHistoryKLQuota.proto\x12\x19Qot_RequestHistoryKLQuota\x1a\x0c\x43ommon.proto\x1a\x10Qot_Common.proto\"q\n\nDetailItem\x12&\n\x08security\x18\x01 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x13\n\x0brequestTime\x18\x02 \x02(\t\x12\x18\n\x10requestTimeStamp\x18\x03 \x01(\x03\"\x19\n\x03\x43\x32S\x12\x12\n\nbGetDetail\x18\x02 \x01(\x08\"h\n\x03S2C\x12\x11\n\tusedQuota\x18\x01 \x02(\x05\x12\x13\n\x0bremainQuota\x18\x02 \x02(\x05\x12\x39\n\ndetailList\x18\x03 \x03(\x0b\x32%.Qot_RequestHistoryKLQuota.DetailItem\"6\n\x07Request\x12+\n\x03\x63\x32s\x18\x01 \x02(\x0b\x32\x1e.Qot_RequestHistoryKLQuota.C2S\"o\n\x08Response\x12\x15\n\x07retType\x18\x01 \x02(\x05:\x04-400\x12\x0e\n\x06retMsg\x18\x02 \x01(\t\x12\x0f\n\x07\x65rrCode\x18\x03 \x01(\x05\x12+\n\x03s2c\x18\x04 \x01(\x0b\x32\x1e.Qot_RequestHistoryKLQuota.S2CBO\n\x13\x63om.futu.openapi.pbZ8github.com/futuopen/ftapi4go/pb/qotrequesthistoryklquota')
  ,
  dependencies=[Common__pb2.DESCRIPTOR,Qot__Common__pb2.DESCRIPTOR,])




_DETAILITEM = _descriptor.Descriptor(
  name='DetailItem',
  full_name='Qot_RequestHistoryKLQuota.DetailItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='security', full_name='Qot_RequestHistoryKLQuota.DetailItem.security', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Qot_RequestHistoryKLQuota.DetailItem.name', index=1,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='requestTime', full_name='Qot_RequestHistoryKLQuota.DetailItem.requestTime', index=2,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='requestTimeStamp', full_name='Qot_RequestHistoryKLQuota.DetailItem.requestTimeStamp', index=3,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=94,
  serialized_end=207,
)


_C2S = _descriptor.Descriptor(
  name='C2S',
  full_name='Qot_RequestHistoryKLQuota.C2S',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bGetDetail', full_name='Qot_RequestHistoryKLQuota.C2S.bGetDetail', index=0,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=209,
  serialized_end=234,
)


_S2C = _descriptor.Descriptor(
  name='S2C',
  full_name='Qot_RequestHistoryKLQuota.S2C',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='usedQuota', full_name='Qot_RequestHistoryKLQuota.S2C.usedQuota', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remainQuota', full_name='Qot_RequestHistoryKLQuota.S2C.remainQuota', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detailList', full_name='Qot_RequestHistoryKLQuota.S2C.detailList', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=236,
  serialized_end=340,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='Qot_RequestHistoryKLQuota.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='c2s', full_name='Qot_RequestHistoryKLQuota.Request.c2s', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=342,
  serialized_end=396,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Qot_RequestHistoryKLQuota.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='retType', full_name='Qot_RequestHistoryKLQuota.Response.retType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=True, default_value=-400,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retMsg', full_name='Qot_RequestHistoryKLQuota.Response.retMsg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errCode', full_name='Qot_RequestHistoryKLQuota.Response.errCode', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s2c', full_name='Qot_RequestHistoryKLQuota.Response.s2c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=398,
  serialized_end=509,
)

_DETAILITEM.fields_by_name['security'].message_type = Qot__Common__pb2._SECURITY
_S2C.fields_by_name['detailList'].message_type = _DETAILITEM
_REQUEST.fields_by_name['c2s'].message_type = _C2S
_RESPONSE.fields_by_name['s2c'].message_type = _S2C
DESCRIPTOR.message_types_by_name['DetailItem'] = _DETAILITEM
DESCRIPTOR.message_types_by_name['C2S'] = _C2S
DESCRIPTOR.message_types_by_name['S2C'] = _S2C
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DetailItem = _reflection.GeneratedProtocolMessageType('DetailItem', (_message.Message,), dict(
  DESCRIPTOR = _DETAILITEM,
  __module__ = 'Qot_RequestHistoryKLQuota_pb2'
  # @@protoc_insertion_point(class_scope:Qot_RequestHistoryKLQuota.DetailItem)
  ))
_sym_db.RegisterMessage(DetailItem)

C2S = _reflection.GeneratedProtocolMessageType('C2S', (_message.Message,), dict(
  DESCRIPTOR = _C2S,
  __module__ = 'Qot_RequestHistoryKLQuota_pb2'
  # @@protoc_insertion_point(class_scope:Qot_RequestHistoryKLQuota.C2S)
  ))
_sym_db.RegisterMessage(C2S)

S2C = _reflection.GeneratedProtocolMessageType('S2C', (_message.Message,), dict(
  DESCRIPTOR = _S2C,
  __module__ = 'Qot_RequestHistoryKLQuota_pb2'
  # @@protoc_insertion_point(class_scope:Qot_RequestHistoryKLQuota.S2C)
  ))
_sym_db.RegisterMessage(S2C)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'Qot_RequestHistoryKLQuota_pb2'
  # @@protoc_insertion_point(class_scope:Qot_RequestHistoryKLQuota.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'Qot_RequestHistoryKLQuota_pb2'
  # @@protoc_insertion_point(class_scope:Qot_RequestHistoryKLQuota.Response)
  ))
_sym_db.RegisterMessage(Response)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ8github.com/futuopen/ftapi4go/pb/qotrequesthistoryklquota'))
# @@protoc_insertion_point(module_scope)
