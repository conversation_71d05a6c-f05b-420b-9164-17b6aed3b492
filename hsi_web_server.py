#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恆生指數期貨Web監控後端服務器
提供實時數據API給HTML前端

作者: AI Assistant
日期: 2025-01-14
"""

import json
import time
import datetime
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import socketserver
from futu import *

class HSIDataProvider:
    """恆指數據提供器"""
    
    def __init__(self):
        self.quote_ctx = None
        self.is_connected = False
        self.current_data = {}
        self.kline_data = {}
        self.products = {
            'HK.HSImain': '恆指期貨主連',
            'HK.MHImain': '小型恆指期貨主連'
        }
        
    def connect_to_futu(self):
        """連接到FUTU OpenD"""
        try:
            self.quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
            self.is_connected = True
            print("✅ 成功連接到 FUTU OpenD")
            return True
        except Exception as e:
            print(f"❌ 連接失敗: {e}")
            self.is_connected = False
            return False
    
    def subscribe_product(self, product_code):
        """訂閱產品"""
        if not self.is_connected:
            return False
            
        try:
            ret_sub, err_message = self.quote_ctx.subscribe(
                [product_code], 
                [SubType.QUOTE, SubType.K_5M], 
                subscribe_push=False
            )
            
            if ret_sub == RET_OK:
                print(f"✅ 成功訂閱 {product_code}")
                return True
            else:
                print(f"❌ 訂閱失敗: {err_message}")
                return False
                
        except Exception as e:
            print(f"❌ 訂閱錯誤: {e}")
            return False
    
    def get_realtime_quote(self, product_code):
        """獲取實時報價"""
        if not self.is_connected:
            return None
            
        try:
            ret, data = self.quote_ctx.get_stock_quote([product_code])
            
            if ret == RET_OK and not data.empty:
                quote_data = data.iloc[0]
                
                result = {
                    'code': quote_data['code'],
                    'name': quote_data['name'],
                    'last_price': float(quote_data['last_price']),
                    'open_price': float(quote_data['open_price']),
                    'high_price': float(quote_data['high_price']),
                    'low_price': float(quote_data['low_price']),
                    'prev_close_price': float(quote_data['prev_close_price']),
                    'volume': int(quote_data['volume']),
                    'turnover': float(quote_data['turnover']),
                    'data_time': quote_data['data_time'],
                    'timestamp': datetime.datetime.now().isoformat()
                }
                
                self.current_data[product_code] = result
                return result
                
            else:
                print(f"❌ 獲取報價失敗: {data}")
                return None
                
        except Exception as e:
            print(f"❌ 獲取報價錯誤: {e}")
            return None
    
    def get_kline_data(self, product_code, num=50):
        """獲取K線數據"""
        if not self.is_connected:
            return None
            
        try:
            ret, data = self.quote_ctx.get_cur_kline(
                code=product_code, 
                num=num, 
                ktype=KLType.K_5M
            )
            
            if ret == RET_OK and not data.empty:
                kline_list = []
                
                for index, row in data.iterrows():
                    kline_item = {
                        'time': row['time_key'],
                        'open': float(row['open']),
                        'high': float(row['high']),
                        'low': float(row['low']),
                        'close': float(row['close']),
                        'volume': int(row['volume']),
                        'turnover': float(row['turnover'])
                    }
                    kline_list.append(kline_item)
                
                # 計算移動平均線
                closes = [item['close'] for item in kline_list]
                sma10 = self.calculate_sma(closes, 10)
                sma20 = self.calculate_sma(closes, 20)
                
                for i, item in enumerate(kline_list):
                    item['sma10'] = sma10[i] if sma10[i] is not None else None
                    item['sma20'] = sma20[i] if sma20[i] is not None else None
                
                self.kline_data[product_code] = kline_list
                return kline_list
                
            else:
                print(f"❌ 獲取K線失敗: {data}")
                return None
                
        except Exception as e:
            print(f"❌ 獲取K線錯誤: {e}")
            return None
    
    def calculate_sma(self, data, period):
        """計算簡單移動平均線"""
        sma = []
        for i in range(len(data)):
            if i < period - 1:
                sma.append(None)
            else:
                avg = sum(data[i-period+1:i+1]) / period
                sma.append(round(avg, 2))
        return sma
    
    def disconnect(self):
        """斷開連接"""
        if self.quote_ctx:
            try:
                self.quote_ctx.close()
                self.is_connected = False
                print("✅ 已斷開FUTU連接")
            except Exception as e:
                print(f"⚠️ 斷開連接錯誤: {e}")


class HSIWebHandler(BaseHTTPRequestHandler):
    """Web請求處理器"""
    
    def __init__(self, *args, data_provider=None, **kwargs):
        self.data_provider = data_provider
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """處理GET請求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query_params = parse_qs(parsed_path.query)
        
        # 設置CORS頭
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        try:
            if path == '/api/connect':
                # 連接API
                success = self.data_provider.connect_to_futu()
                response = {'success': success, 'message': '連接成功' if success else '連接失敗'}
                
            elif path == '/api/disconnect':
                # 斷開連接API
                self.data_provider.disconnect()
                response = {'success': True, 'message': '已斷開連接'}
                
            elif path == '/api/quote':
                # 獲取實時報價
                product = query_params.get('product', ['HK.HSImain'])[0]
                
                if not self.data_provider.is_connected:
                    response = {'success': False, 'message': '未連接到FUTU'}
                else:
                    # 訂閱產品
                    self.data_provider.subscribe_product(product)
                    
                    # 獲取報價
                    quote_data = self.data_provider.get_realtime_quote(product)
                    
                    if quote_data:
                        response = {'success': True, 'data': quote_data}
                    else:
                        response = {'success': False, 'message': '獲取報價失敗'}
                        
            elif path == '/api/kline':
                # 獲取K線數據
                product = query_params.get('product', ['HK.HSImain'])[0]
                num = int(query_params.get('num', ['50'])[0])
                
                if not self.data_provider.is_connected:
                    response = {'success': False, 'message': '未連接到FUTU'}
                else:
                    kline_data = self.data_provider.get_kline_data(product, num)
                    
                    if kline_data:
                        response = {'success': True, 'data': kline_data}
                    else:
                        response = {'success': False, 'message': '獲取K線失敗'}
                        
            elif path == '/api/products':
                # 獲取產品列表
                response = {
                    'success': True, 
                    'data': self.data_provider.products
                }
                
            else:
                response = {'success': False, 'message': '未知的API端點'}
                
        except Exception as e:
            response = {'success': False, 'message': f'服務器錯誤: {str(e)}'}
        
        # 發送響應
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def do_OPTIONS(self):
        """處理OPTIONS請求（CORS預檢）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定義日誌格式"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {format % args}")


def create_handler(data_provider):
    """創建請求處理器工廠"""
    def handler(*args, **kwargs):
        return HSIWebHandler(*args, data_provider=data_provider, **kwargs)
    return handler


def main():
    """主函數"""
    print("🎯 恆生指數期貨Web監控服務器")
    print("=" * 50)
    
    # 創建數據提供器
    data_provider = HSIDataProvider()
    
    # 創建HTTP服務器
    server_port = 8080
    handler = create_handler(data_provider)
    
    try:
        with HTTPServer(('localhost', server_port), handler) as httpd:
            print(f"🚀 服務器已啟動: http://localhost:{server_port}")
            print("📋 可用的API端點:")
            print("   - GET /api/connect - 連接到FUTU")
            print("   - GET /api/disconnect - 斷開FUTU連接")
            print("   - GET /api/quote?product=HK.HSImain - 獲取實時報價")
            print("   - GET /api/kline?product=HK.HSImain&num=50 - 獲取K線數據")
            print("   - GET /api/products - 獲取產品列表")
            print("=" * 50)
            print("⏹️ 按 Ctrl+C 停止服務器")
            
            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                print("\n\n⏹️ 服務器已停止")
                
    except Exception as e:
        print(f"❌ 服務器啟動失敗: {e}")
    
    finally:
        # 清理資源
        data_provider.disconnect()


if __name__ == "__main__":
    main()
