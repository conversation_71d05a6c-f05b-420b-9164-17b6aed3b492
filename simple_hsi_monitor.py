#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡易恆生指數期貨實時價格監控
使用 FUTU API 獲取實時行情

配置信息:
- 牛牛號: 22188140
- OpenD WebSocket密鑰: 3e8229abd3ccdfdc
- 期貨權限: LV1
"""

import time
import datetime
from futu import *

def get_hsi_futures_price():
    """獲取恆生指數期貨當前價格"""
    
    # 連接到 OpenD
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        # 恆生指數期貨主連代碼
        hsi_code = "HK.HSImain"
        
        print(f"正在連接 FUTU OpenD...")
        
        # 訂閱恆生指數期貨
        ret_sub, err_message = quote_ctx.subscribe([hsi_code], [SubType.QUOTE])
        
        if ret_sub == RET_OK:
            print(f"✅ 成功訂閱 {hsi_code}")
            
            # 獲取實時報價
            ret, data = quote_ctx.get_stock_quote([hsi_code])
            
            if ret == RET_OK:
                quote_data = data.iloc[0]
                
                # 顯示價格信息
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                print("\n" + "="*50)
                print(f"📊 恆生指數期貨實時行情")
                print(f"⏰ 更新時間: {current_time}")
                print("="*50)
                print(f"代碼: {quote_data['code']}")
                print(f"名稱: {quote_data['name']}")
                print(f"最新價: {quote_data['last_price']:.1f}")
                print(f"開盤價: {quote_data['open_price']:.1f}")
                print(f"最高價: {quote_data['high_price']:.1f}")
                print(f"最低價: {quote_data['low_price']:.1f}")
                print(f"昨收價: {quote_data['prev_close_price']:.1f}")
                
                # 計算漲跌
                change = quote_data['last_price'] - quote_data['prev_close_price']
                change_percent = (change / quote_data['prev_close_price']) * 100
                
                if change > 0:
                    print(f"漲跌額: 📈 +{change:.1f}")
                    print(f"漲跌幅: 📈 +{change_percent:.2f}%")
                elif change < 0:
                    print(f"漲跌額: 📉 {change:.1f}")
                    print(f"漲跌幅: 📉 {change_percent:.2f}%")
                else:
                    print(f"漲跌額: ➡️ {change:.1f}")
                    print(f"漲跌幅: ➡️ {change_percent:.2f}%")
                
                print(f"成交量: {quote_data['volume']:,}")
                print(f"成交額: {quote_data['turnover']:,.0f}")
                print(f"數據時間: {quote_data['data_time']}")
                print("="*50)
                
                return quote_data
                
            else:
                print(f"❌ 獲取報價失敗: {data}")
                return None
                
        else:
            print(f"❌ 訂閱失敗: {err_message}")
            return None
            
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")
        return None
        
    finally:
        # 關閉連接
        quote_ctx.close()


def continuous_monitor(interval=10):
    """持續監控模式"""
    
    print("🚀 開始持續監控恆生指數期貨")
    print(f"📱 每 {interval} 秒更新一次")
    print("⏹️  按 Ctrl+C 停止監控\n")
    
    try:
        while True:
            get_hsi_futures_price()
            print(f"\n⏳ 等待 {interval} 秒後下次更新...")
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n\n⏹️  監控已停止")


def main():
    """主函數"""
    print("🎯 恆生指數期貨實時價格監控程式")
    print("=" * 60)
    print("📋 使用說明:")
    print("   1. 確保 FUTU OpenD 已啟動並登錄")
    print("   2. 確保已有期貨 LV1 權限")
    print("   3. 選擇運行模式")
    print("=" * 60)
    
    while True:
        print("\n📌 請選擇運行模式:")
        print("1. 獲取一次當前價格")
        print("2. 持續監控模式 (每10秒更新)")
        print("3. 持續監控模式 (每5秒更新)")
        print("4. 退出程式")
        
        choice = input("\n請輸入選項 (1-4): ").strip()
        
        if choice == "1":
            print("\n📊 正在獲取當前價格...")
            get_hsi_futures_price()
            
        elif choice == "2":
            continuous_monitor(interval=10)
            
        elif choice == "3":
            continuous_monitor(interval=5)
            
        elif choice == "4":
            print("👋 程式已退出")
            break
            
        else:
            print("❌ 無效選項，請重新選擇")


if __name__ == "__main__":
    main()
