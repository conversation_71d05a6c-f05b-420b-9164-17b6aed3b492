# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Notify.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Common_pb2 as Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Notify.proto',
  package='Notify',
  syntax='proto2',
  serialized_pb=_b('\n\x0cNotify.proto\x12\x06Notify\x1a\x0c\x43ommon.proto\"+\n\x08GtwEvent\x12\x11\n\teventType\x18\x01 \x02(\x05\x12\x0c\n\x04\x64\x65sc\x18\x02 \x02(\t\"=\n\rProgramStatus\x12,\n\rprogramStatus\x18\x01 \x02(\x0b\x32\x15.Common.ProgramStatus\"7\n\rConnectStatus\x12\x12\n\nqotLogined\x18\x01 \x02(\x08\x12\x12\n\ntrdLogined\x18\x02 \x02(\x08\"\xee\x03\n\x08QotRight\x12\x12\n\nhkQotRight\x18\x04 \x02(\x05\x12\x12\n\nusQotRight\x18\x05 \x02(\x05\x12\x12\n\ncnQotRight\x18\x06 \x02(\x05\x12\x18\n\x10hkOptionQotRight\x18\x07 \x01(\x05\x12\x1b\n\x13hasUSOptionQotRight\x18\x08 \x01(\x08\x12\x18\n\x10hkFutureQotRight\x18\t \x01(\x05\x12\x18\n\x10usFutureQotRight\x18\n \x01(\x05\x12\x18\n\x10usOptionQotRight\x18\x0b \x01(\x05\x12\x17\n\x0fusIndexQotRight\x18\x0c \x01(\x05\x12\x15\n\rusOtcQotRight\x18\r \x01(\x05\x12\x18\n\x10sgFutureQotRight\x18\x0e \x01(\x05\x12\x18\n\x10jpFutureQotRight\x18\x0f \x01(\x05\x12\x1b\n\x13usCMEFutureQotRight\x18\x10 \x01(\x05\x12\x1c\n\x14usCBOTFutureQotRight\x18\x11 \x01(\x05\x12\x1d\n\x15usNYMEXFutureQotRight\x18\x12 \x01(\x05\x12\x1d\n\x15usCOMEXFutureQotRight\x18\x13 \x01(\x05\x12\x1c\n\x14usCBOEFutureQotRight\x18\x14 \x01(\x05\x12\x12\n\nshQotRight\x18\x15 \x01(\x05\x12\x12\n\nszQotRight\x18\x16 \x01(\x05\"\x1c\n\x08\x41PILevel\x12\x10\n\x08\x61piLevel\x18\x03 \x02(\t\"4\n\x08\x41PIQuota\x12\x10\n\x08subQuota\x18\x01 \x02(\x05\x12\x16\n\x0ehistoryKLQuota\x18\x02 \x02(\x05\"9\n\tUsedQuota\x12\x14\n\x0cusedSubQuota\x18\x01 \x01(\x05\x12\x16\n\x0eusedKLineQuota\x18\x02 \x01(\x05\"\xa2\x02\n\x03S2C\x12\x0c\n\x04type\x18\x01 \x02(\x05\x12\x1f\n\x05\x65vent\x18\x02 \x01(\x0b\x32\x10.Notify.GtwEvent\x12,\n\rprogramStatus\x18\x03 \x01(\x0b\x32\x15.Notify.ProgramStatus\x12,\n\rconnectStatus\x18\x04 \x01(\x0b\x32\x15.Notify.ConnectStatus\x12\"\n\x08qotRight\x18\x05 \x01(\x0b\x32\x10.Notify.QotRight\x12\"\n\x08\x61piLevel\x18\x06 \x01(\x0b\x32\x10.Notify.APILevel\x12\"\n\x08\x61piQuota\x18\x07 \x01(\x0b\x32\x10.Notify.APIQuota\x12$\n\tusedQuota\x18\x08 \x01(\x0b\x32\x11.Notify.UsedQuota\"\\\n\x08Response\x12\x15\n\x07retType\x18\x01 \x02(\x05:\x04-400\x12\x0e\n\x06retMsg\x18\x02 \x01(\t\x12\x0f\n\x07\x65rrCode\x18\x03 \x01(\x05\x12\x18\n\x03s2c\x18\x04 \x01(\x0b\x32\x0b.Notify.S2C*\xd8\x01\n\nNotifyType\x12\x13\n\x0fNotifyType_None\x10\x00\x12\x17\n\x13NotifyType_GtwEvent\x10\x01\x12\x1c\n\x18NotifyType_ProgramStatus\x10\x02\x12\x19\n\x15NotifyType_ConnStatus\x10\x03\x12\x17\n\x13NotifyType_QotRight\x10\x04\x12\x17\n\x13NotifyType_APILevel\x10\x05\x12\x17\n\x13NotifyType_APIQuota\x10\x06\x12\x18\n\x14NotifyType_UsedQuota\x10\x07*\x9b\x04\n\x0cGtwEventType\x12\x15\n\x11GtwEventType_None\x10\x00\x12#\n\x1fGtwEventType_LocalCfgLoadFailed\x10\x01\x12 \n\x1cGtwEventType_APISvrRunFailed\x10\x02\x12\x1c\n\x18GtwEventType_ForceUpdate\x10\x03\x12\x1c\n\x18GtwEventType_LoginFailed\x10\x04\x12\"\n\x1eGtwEventType_UnAgreeDisclaimer\x10\x05\x12\x1e\n\x1aGtwEventType_NetCfgMissing\x10\x06\x12\x1a\n\x16GtwEventType_KickedOut\x10\x07\x12 \n\x1cGtwEventType_LoginPwdChanged\x10\x08\x12\x19\n\x15GtwEventType_BanLogin\x10\t\x12\"\n\x1eGtwEventType_NeedPicVerifyCode\x10\n\x12$\n GtwEventType_NeedPhoneVerifyCode\x10\x0b\x12 \n\x1cGtwEventType_AppDataNotExist\x10\x0c\x12#\n\x1fGtwEventType_NessaryDataMissing\x10\r\x12 \n\x1cGtwEventType_TradePwdChanged\x10\x0e\x12!\n\x1dGtwEventType_EnableDeviceLock\x10\x0f\x42=\n\x13\x63om.futu.openapi.pbZ&github.com/futuopen/ftapi4go/pb/notify')
  ,
  dependencies=[Common__pb2.DESCRIPTOR,])

_NOTIFYTYPE = _descriptor.EnumDescriptor(
  name='NotifyType',
  full_name='Notify.NotifyType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NotifyType_None', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NotifyType_GtwEvent', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NotifyType_ProgramStatus', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NotifyType_ConnStatus', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NotifyType_QotRight', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NotifyType_APILevel', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NotifyType_APIQuota', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NotifyType_UsedQuota', index=7, number=7,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1231,
  serialized_end=1447,
)
_sym_db.RegisterEnumDescriptor(_NOTIFYTYPE)

NotifyType = enum_type_wrapper.EnumTypeWrapper(_NOTIFYTYPE)
_GTWEVENTTYPE = _descriptor.EnumDescriptor(
  name='GtwEventType',
  full_name='Notify.GtwEventType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_None', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_LocalCfgLoadFailed', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_APISvrRunFailed', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_ForceUpdate', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_LoginFailed', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_UnAgreeDisclaimer', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_NetCfgMissing', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_KickedOut', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_LoginPwdChanged', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_BanLogin', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_NeedPicVerifyCode', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_NeedPhoneVerifyCode', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_AppDataNotExist', index=12, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_NessaryDataMissing', index=13, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_TradePwdChanged', index=14, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GtwEventType_EnableDeviceLock', index=15, number=15,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1450,
  serialized_end=1989,
)
_sym_db.RegisterEnumDescriptor(_GTWEVENTTYPE)

GtwEventType = enum_type_wrapper.EnumTypeWrapper(_GTWEVENTTYPE)
NotifyType_None = 0
NotifyType_GtwEvent = 1
NotifyType_ProgramStatus = 2
NotifyType_ConnStatus = 3
NotifyType_QotRight = 4
NotifyType_APILevel = 5
NotifyType_APIQuota = 6
NotifyType_UsedQuota = 7
GtwEventType_None = 0
GtwEventType_LocalCfgLoadFailed = 1
GtwEventType_APISvrRunFailed = 2
GtwEventType_ForceUpdate = 3
GtwEventType_LoginFailed = 4
GtwEventType_UnAgreeDisclaimer = 5
GtwEventType_NetCfgMissing = 6
GtwEventType_KickedOut = 7
GtwEventType_LoginPwdChanged = 8
GtwEventType_BanLogin = 9
GtwEventType_NeedPicVerifyCode = 10
GtwEventType_NeedPhoneVerifyCode = 11
GtwEventType_AppDataNotExist = 12
GtwEventType_NessaryDataMissing = 13
GtwEventType_TradePwdChanged = 14
GtwEventType_EnableDeviceLock = 15



_GTWEVENT = _descriptor.Descriptor(
  name='GtwEvent',
  full_name='Notify.GtwEvent',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='eventType', full_name='Notify.GtwEvent.eventType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='desc', full_name='Notify.GtwEvent.desc', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=38,
  serialized_end=81,
)


_PROGRAMSTATUS = _descriptor.Descriptor(
  name='ProgramStatus',
  full_name='Notify.ProgramStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='programStatus', full_name='Notify.ProgramStatus.programStatus', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=83,
  serialized_end=144,
)


_CONNECTSTATUS = _descriptor.Descriptor(
  name='ConnectStatus',
  full_name='Notify.ConnectStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='qotLogined', full_name='Notify.ConnectStatus.qotLogined', index=0,
      number=1, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trdLogined', full_name='Notify.ConnectStatus.trdLogined', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=146,
  serialized_end=201,
)


_QOTRIGHT = _descriptor.Descriptor(
  name='QotRight',
  full_name='Notify.QotRight',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hkQotRight', full_name='Notify.QotRight.hkQotRight', index=0,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usQotRight', full_name='Notify.QotRight.usQotRight', index=1,
      number=5, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cnQotRight', full_name='Notify.QotRight.cnQotRight', index=2,
      number=6, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hkOptionQotRight', full_name='Notify.QotRight.hkOptionQotRight', index=3,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hasUSOptionQotRight', full_name='Notify.QotRight.hasUSOptionQotRight', index=4,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hkFutureQotRight', full_name='Notify.QotRight.hkFutureQotRight', index=5,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usFutureQotRight', full_name='Notify.QotRight.usFutureQotRight', index=6,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usOptionQotRight', full_name='Notify.QotRight.usOptionQotRight', index=7,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usIndexQotRight', full_name='Notify.QotRight.usIndexQotRight', index=8,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usOtcQotRight', full_name='Notify.QotRight.usOtcQotRight', index=9,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sgFutureQotRight', full_name='Notify.QotRight.sgFutureQotRight', index=10,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jpFutureQotRight', full_name='Notify.QotRight.jpFutureQotRight', index=11,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usCMEFutureQotRight', full_name='Notify.QotRight.usCMEFutureQotRight', index=12,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usCBOTFutureQotRight', full_name='Notify.QotRight.usCBOTFutureQotRight', index=13,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usNYMEXFutureQotRight', full_name='Notify.QotRight.usNYMEXFutureQotRight', index=14,
      number=18, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usCOMEXFutureQotRight', full_name='Notify.QotRight.usCOMEXFutureQotRight', index=15,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usCBOEFutureQotRight', full_name='Notify.QotRight.usCBOEFutureQotRight', index=16,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shQotRight', full_name='Notify.QotRight.shQotRight', index=17,
      number=21, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='szQotRight', full_name='Notify.QotRight.szQotRight', index=18,
      number=22, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=204,
  serialized_end=698,
)


_APILEVEL = _descriptor.Descriptor(
  name='APILevel',
  full_name='Notify.APILevel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='apiLevel', full_name='Notify.APILevel.apiLevel', index=0,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=700,
  serialized_end=728,
)


_APIQUOTA = _descriptor.Descriptor(
  name='APIQuota',
  full_name='Notify.APIQuota',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='subQuota', full_name='Notify.APIQuota.subQuota', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='historyKLQuota', full_name='Notify.APIQuota.historyKLQuota', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=730,
  serialized_end=782,
)


_USEDQUOTA = _descriptor.Descriptor(
  name='UsedQuota',
  full_name='Notify.UsedQuota',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='usedSubQuota', full_name='Notify.UsedQuota.usedSubQuota', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usedKLineQuota', full_name='Notify.UsedQuota.usedKLineQuota', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=784,
  serialized_end=841,
)


_S2C = _descriptor.Descriptor(
  name='S2C',
  full_name='Notify.S2C',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='Notify.S2C.type', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='event', full_name='Notify.S2C.event', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='programStatus', full_name='Notify.S2C.programStatus', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='connectStatus', full_name='Notify.S2C.connectStatus', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qotRight', full_name='Notify.S2C.qotRight', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='apiLevel', full_name='Notify.S2C.apiLevel', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='apiQuota', full_name='Notify.S2C.apiQuota', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usedQuota', full_name='Notify.S2C.usedQuota', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=844,
  serialized_end=1134,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Notify.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='retType', full_name='Notify.Response.retType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=True, default_value=-400,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retMsg', full_name='Notify.Response.retMsg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errCode', full_name='Notify.Response.errCode', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s2c', full_name='Notify.Response.s2c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1136,
  serialized_end=1228,
)

_PROGRAMSTATUS.fields_by_name['programStatus'].message_type = Common__pb2._PROGRAMSTATUS
_S2C.fields_by_name['event'].message_type = _GTWEVENT
_S2C.fields_by_name['programStatus'].message_type = _PROGRAMSTATUS
_S2C.fields_by_name['connectStatus'].message_type = _CONNECTSTATUS
_S2C.fields_by_name['qotRight'].message_type = _QOTRIGHT
_S2C.fields_by_name['apiLevel'].message_type = _APILEVEL
_S2C.fields_by_name['apiQuota'].message_type = _APIQUOTA
_S2C.fields_by_name['usedQuota'].message_type = _USEDQUOTA
_RESPONSE.fields_by_name['s2c'].message_type = _S2C
DESCRIPTOR.message_types_by_name['GtwEvent'] = _GTWEVENT
DESCRIPTOR.message_types_by_name['ProgramStatus'] = _PROGRAMSTATUS
DESCRIPTOR.message_types_by_name['ConnectStatus'] = _CONNECTSTATUS
DESCRIPTOR.message_types_by_name['QotRight'] = _QOTRIGHT
DESCRIPTOR.message_types_by_name['APILevel'] = _APILEVEL
DESCRIPTOR.message_types_by_name['APIQuota'] = _APIQUOTA
DESCRIPTOR.message_types_by_name['UsedQuota'] = _USEDQUOTA
DESCRIPTOR.message_types_by_name['S2C'] = _S2C
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.enum_types_by_name['NotifyType'] = _NOTIFYTYPE
DESCRIPTOR.enum_types_by_name['GtwEventType'] = _GTWEVENTTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GtwEvent = _reflection.GeneratedProtocolMessageType('GtwEvent', (_message.Message,), dict(
  DESCRIPTOR = _GTWEVENT,
  __module__ = 'Notify_pb2'
  # @@protoc_insertion_point(class_scope:Notify.GtwEvent)
  ))
_sym_db.RegisterMessage(GtwEvent)

ProgramStatus = _reflection.GeneratedProtocolMessageType('ProgramStatus', (_message.Message,), dict(
  DESCRIPTOR = _PROGRAMSTATUS,
  __module__ = 'Notify_pb2'
  # @@protoc_insertion_point(class_scope:Notify.ProgramStatus)
  ))
_sym_db.RegisterMessage(ProgramStatus)

ConnectStatus = _reflection.GeneratedProtocolMessageType('ConnectStatus', (_message.Message,), dict(
  DESCRIPTOR = _CONNECTSTATUS,
  __module__ = 'Notify_pb2'
  # @@protoc_insertion_point(class_scope:Notify.ConnectStatus)
  ))
_sym_db.RegisterMessage(ConnectStatus)

QotRight = _reflection.GeneratedProtocolMessageType('QotRight', (_message.Message,), dict(
  DESCRIPTOR = _QOTRIGHT,
  __module__ = 'Notify_pb2'
  # @@protoc_insertion_point(class_scope:Notify.QotRight)
  ))
_sym_db.RegisterMessage(QotRight)

APILevel = _reflection.GeneratedProtocolMessageType('APILevel', (_message.Message,), dict(
  DESCRIPTOR = _APILEVEL,
  __module__ = 'Notify_pb2'
  # @@protoc_insertion_point(class_scope:Notify.APILevel)
  ))
_sym_db.RegisterMessage(APILevel)

APIQuota = _reflection.GeneratedProtocolMessageType('APIQuota', (_message.Message,), dict(
  DESCRIPTOR = _APIQUOTA,
  __module__ = 'Notify_pb2'
  # @@protoc_insertion_point(class_scope:Notify.APIQuota)
  ))
_sym_db.RegisterMessage(APIQuota)

UsedQuota = _reflection.GeneratedProtocolMessageType('UsedQuota', (_message.Message,), dict(
  DESCRIPTOR = _USEDQUOTA,
  __module__ = 'Notify_pb2'
  # @@protoc_insertion_point(class_scope:Notify.UsedQuota)
  ))
_sym_db.RegisterMessage(UsedQuota)

S2C = _reflection.GeneratedProtocolMessageType('S2C', (_message.Message,), dict(
  DESCRIPTOR = _S2C,
  __module__ = 'Notify_pb2'
  # @@protoc_insertion_point(class_scope:Notify.S2C)
  ))
_sym_db.RegisterMessage(S2C)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'Notify_pb2'
  # @@protoc_insertion_point(class_scope:Notify.Response)
  ))
_sym_db.RegisterMessage(Response)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ&github.com/futuopen/ftapi4go/pb/notify'))
# @@protoc_insertion_point(module_scope)
