# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Qot_StockFilter.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Common_pb2 as Common__pb2
import Qot_Common_pb2 as Qot__Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Qot_StockFilter.proto',
  package='Qot_StockFilter',
  syntax='proto2',
  serialized_pb=_b('\n\x15Qot_StockFilter.proto\x12\x0fQot_StockFilter\x1a\x0c\x43ommon.proto\x1a\x10Qot_Common.proto\"j\n\nBaseFilter\x12\x11\n\tfieldName\x18\x01 \x02(\x05\x12\x11\n\tfilterMin\x18\x02 \x01(\x01\x12\x11\n\tfilterMax\x18\x03 \x01(\x01\x12\x12\n\nisNoFilter\x18\x04 \x01(\x08\x12\x0f\n\x07sortDir\x18\x05 \x01(\x05\"~\n\x10\x41\x63\x63umulateFilter\x12\x11\n\tfieldName\x18\x01 \x02(\x05\x12\x11\n\tfilterMin\x18\x02 \x01(\x01\x12\x11\n\tfilterMax\x18\x03 \x01(\x01\x12\x12\n\nisNoFilter\x18\x04 \x01(\x08\x12\x0f\n\x07sortDir\x18\x05 \x01(\x05\x12\x0c\n\x04\x64\x61ys\x18\x06 \x02(\x05\"\x80\x01\n\x0f\x46inancialFilter\x12\x11\n\tfieldName\x18\x01 \x02(\x05\x12\x11\n\tfilterMin\x18\x02 \x01(\x01\x12\x11\n\tfilterMax\x18\x03 \x01(\x01\x12\x12\n\nisNoFilter\x18\x04 \x01(\x08\x12\x0f\n\x07sortDir\x18\x05 \x01(\x05\x12\x0f\n\x07quarter\x18\x06 \x02(\x05\"a\n\rPatternFilter\x12\x11\n\tfieldName\x18\x01 \x02(\x05\x12\x0e\n\x06klType\x18\x02 \x02(\x05\x12\x12\n\nisNoFilter\x18\x03 \x01(\x08\x12\x19\n\x11\x63onsecutivePeriod\x18\x04 \x01(\x05\"\xee\x01\n\x15\x43ustomIndicatorFilter\x12\x16\n\x0e\x66irstFieldName\x18\x01 \x02(\x05\x12\x17\n\x0fsecondFieldName\x18\x02 \x02(\x05\x12\x18\n\x10relativePosition\x18\x03 \x02(\x05\x12\x12\n\nfieldValue\x18\x04 \x01(\x01\x12\x0e\n\x06klType\x18\x05 \x02(\x05\x12\x12\n\nisNoFilter\x18\x06 \x01(\x08\x12\x1a\n\x12\x66irstFieldParaList\x18\x07 \x03(\x05\x12\x1b\n\x13secondFieldParaList\x18\x08 \x03(\x05\x12\x19\n\x11\x63onsecutivePeriod\x18\t \x01(\x05\",\n\x08\x42\x61seData\x12\x11\n\tfieldName\x18\x01 \x02(\x05\x12\r\n\x05value\x18\x02 \x02(\x01\"@\n\x0e\x41\x63\x63umulateData\x12\x11\n\tfieldName\x18\x01 \x02(\x05\x12\r\n\x05value\x18\x02 \x02(\x01\x12\x0c\n\x04\x64\x61ys\x18\x03 \x02(\x05\"B\n\rFinancialData\x12\x11\n\tfieldName\x18\x01 \x02(\x05\x12\r\n\x05value\x18\x02 \x02(\x01\x12\x0f\n\x07quarter\x18\x03 \x02(\x05\"^\n\x13\x43ustomIndicatorData\x12\x11\n\tfieldName\x18\x01 \x02(\x05\x12\r\n\x05value\x18\x02 \x02(\x01\x12\x0e\n\x06klType\x18\x03 \x02(\x05\x12\x15\n\rfieldParaList\x18\x04 \x03(\x05\"\xb1\x02\n\tStockData\x12&\n\x08security\x18\x01 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x0c\n\x04name\x18\x02 \x02(\t\x12/\n\x0c\x62\x61seDataList\x18\x03 \x03(\x0b\x32\x19.Qot_StockFilter.BaseData\x12;\n\x12\x61\x63\x63umulateDataList\x18\x04 \x03(\x0b\x32\x1f.Qot_StockFilter.AccumulateData\x12\x39\n\x11\x66inancialDataList\x18\x05 \x03(\x0b\x32\x1e.Qot_StockFilter.FinancialData\x12\x45\n\x17\x63ustomIndicatorDataList\x18\x06 \x03(\x0b\x32$.Qot_StockFilter.CustomIndicatorData\"\x91\x03\n\x03\x43\x32S\x12\r\n\x05\x62\x65gin\x18\x01 \x02(\x05\x12\x0b\n\x03num\x18\x02 \x02(\x05\x12\x0e\n\x06market\x18\x03 \x02(\x05\x12#\n\x05plate\x18\x04 \x01(\x0b\x32\x14.Qot_Common.Security\x12\x33\n\x0e\x62\x61seFilterList\x18\x05 \x03(\x0b\x32\x1b.Qot_StockFilter.BaseFilter\x12?\n\x14\x61\x63\x63umulateFilterList\x18\x06 \x03(\x0b\x32!.Qot_StockFilter.AccumulateFilter\x12=\n\x13\x66inancialFilterList\x18\x07 \x03(\x0b\x32 .Qot_StockFilter.FinancialFilter\x12\x39\n\x11patternFilterList\x18\x08 \x03(\x0b\x32\x1e.Qot_StockFilter.PatternFilter\x12I\n\x19\x63ustomIndicatorFilterList\x18\t \x03(\x0b\x32&.Qot_StockFilter.CustomIndicatorFilter\"W\n\x03S2C\x12\x10\n\x08lastPage\x18\x01 \x02(\x08\x12\x10\n\x08\x61llCount\x18\x02 \x02(\x05\x12,\n\x08\x64\x61taList\x18\x03 \x03(\x0b\x32\x1a.Qot_StockFilter.StockData\",\n\x07Request\x12!\n\x03\x63\x32s\x18\x01 \x02(\x0b\x32\x14.Qot_StockFilter.C2S\"e\n\x08Response\x12\x15\n\x07retType\x18\x01 \x02(\x05:\x04-400\x12\x0e\n\x06retMsg\x18\x02 \x01(\t\x12\x0f\n\x07\x65rrCode\x18\x03 \x01(\x05\x12!\n\x03s2c\x18\x04 \x01(\x0b\x32\x14.Qot_StockFilter.S2C*\x9e\x05\n\nStockField\x12\x16\n\x12StockField_Unknown\x10\x00\x12\x18\n\x14StockField_StockCode\x10\x01\x12\x18\n\x14StockField_StockName\x10\x02\x12\x17\n\x13StockField_CurPrice\x10\x03\x12,\n(StockField_CurPriceToHighest52WeeksRatio\x10\x04\x12+\n\'StockField_CurPriceToLowest52WeeksRatio\x10\x05\x12-\n)StockField_HighPriceToHighest52WeeksRatio\x10\x06\x12+\n\'StockField_LowPriceToLowest52WeeksRatio\x10\x07\x12\x1a\n\x16StockField_VolumeRatio\x10\x08\x12\x1a\n\x16StockField_BidAskRatio\x10\t\x12\x17\n\x13StockField_LotPrice\x10\n\x12\x18\n\x14StockField_MarketVal\x10\x0b\x12\x17\n\x13StockField_PeAnnual\x10\x0c\x12\x14\n\x10StockField_PeTTM\x10\r\x12\x15\n\x11StockField_PbRate\x10\x0e\x12\x1d\n\x19StockField_ChangeRate5min\x10\x0f\x12\"\n\x1eStockField_ChangeRateBeginYear\x10\x10\x12\x14\n\x10StockField_PSTTM\x10\x11\x12\x15\n\x11StockField_PCFTTM\x10\x12\x12\x19\n\x15StockField_TotalShare\x10\x13\x12\x19\n\x15StockField_FloatShare\x10\x14\x12\x1d\n\x19StockField_FloatMarketVal\x10\x15*\xc9\x01\n\x0f\x41\x63\x63umulateField\x12\x1b\n\x17\x41\x63\x63umulateField_Unknown\x10\x00\x12\x1e\n\x1a\x41\x63\x63umulateField_ChangeRate\x10\x01\x12\x1d\n\x19\x41\x63\x63umulateField_Amplitude\x10\x02\x12\x1a\n\x16\x41\x63\x63umulateField_Volume\x10\x03\x12\x1c\n\x18\x41\x63\x63umulateField_Turnover\x10\x04\x12 \n\x1c\x41\x63\x63umulateField_TurnoverRate\x10\x05*\xcb\r\n\x0e\x46inancialField\x12\x1a\n\x16\x46inancialField_Unknown\x10\x00\x12\x1c\n\x18\x46inancialField_NetProfit\x10\x01\x12\"\n\x1e\x46inancialField_NetProfitGrowth\x10\x02\x12 \n\x1c\x46inancialField_SumOfBusiness\x10\x03\x12&\n\"FinancialField_SumOfBusinessGrowth\x10\x04\x12 \n\x1c\x46inancialField_NetProfitRate\x10\x05\x12\"\n\x1e\x46inancialField_GrossProfitRate\x10\x06\x12!\n\x1d\x46inancialField_DebtAssetsRate\x10\x07\x12%\n!FinancialField_ReturnOnEquityRate\x10\x08\x12\x17\n\x13\x46inancialField_ROIC\x10\t\x12\x19\n\x15\x46inancialField_ROATTM\x10\n\x12\x1a\n\x16\x46inancialField_EBITTTM\x10\x0b\x12\x19\n\x15\x46inancialField_EBITDA\x10\x0c\x12%\n!FinancialField_OperatingMarginTTM\x10\r\x12\x1d\n\x19\x46inancialField_EBITMargin\x10\x0e\x12\x1f\n\x1b\x46inancialField_EBITDAMargin\x10\x0f\x12$\n FinancialField_FinancialCostRate\x10\x10\x12%\n!FinancialField_OperatingProfitTTM\x10\x11\x12*\n&FinancialField_ShareholderNetProfitTTM\x10\x12\x12(\n$FinancialField_NetProfitCashCoverTTM\x10\x13\x12\x1f\n\x1b\x46inancialField_CurrentRatio\x10\x14\x12\x1d\n\x19\x46inancialField_QuickRatio\x10\x15\x12$\n FinancialField_CurrentAssetRatio\x10\x16\x12#\n\x1f\x46inancialField_CurrentDebtRatio\x10\x17\x12#\n\x1f\x46inancialField_EquityMultiplier\x10\x18\x12 \n\x1c\x46inancialField_PropertyRatio\x10\x19\x12)\n%FinancialField_CashAndCashEquivalents\x10\x1a\x12%\n!FinancialField_TotalAssetTurnover\x10\x1b\x12%\n!FinancialField_FixedAssetTurnover\x10\x1c\x12$\n FinancialField_InventoryTurnover\x10\x1d\x12\'\n#FinancialField_OperatingCashFlowTTM\x10\x1e\x12%\n!FinancialField_AccountsReceivable\x10\x1f\x12!\n\x1d\x46inancialField_EBITGrowthRate\x10 \x12,\n(FinancialField_OperatingProfitGrowthRate\x10!\x12(\n$FinancialField_TotalAssetsGrowthRate\x10\"\x12\x31\n-FinancialField_ProfitToShareholdersGrowthRate\x10#\x12,\n(FinancialField_ProfitBeforeTaxGrowthRate\x10$\x12 \n\x1c\x46inancialField_EPSGrowthRate\x10%\x12 \n\x1c\x46inancialField_ROEGrowthRate\x10&\x12!\n\x1d\x46inancialField_ROICGrowthRate\x10\'\x12!\n\x1d\x46inancialField_NOCFGrowthRate\x10(\x12)\n%FinancialField_NOCFPerShareGrowthRate\x10)\x12,\n(FinancialField_OperatingRevenueCashCover\x10*\x12/\n+FinancialField_OperatingProfitToTotalProfit\x10+\x12\x1b\n\x17\x46inancialField_BasicEPS\x10,\x12\x1d\n\x19\x46inancialField_DilutedEPS\x10-\x12\x1f\n\x1b\x46inancialField_NOCFPerShare\x10.*\xc3\x07\n\x14\x43ustomIndicatorField\x12 \n\x1c\x43ustomIndicatorField_Unknown\x10\x00\x12\x1e\n\x1a\x43ustomIndicatorField_Price\x10\x01\x12\x1c\n\x18\x43ustomIndicatorField_MA5\x10\x02\x12\x1d\n\x19\x43ustomIndicatorField_MA10\x10\x03\x12\x1d\n\x19\x43ustomIndicatorField_MA20\x10\x04\x12\x1d\n\x19\x43ustomIndicatorField_MA30\x10\x05\x12\x1d\n\x19\x43ustomIndicatorField_MA60\x10\x06\x12\x1e\n\x1a\x43ustomIndicatorField_MA120\x10\x07\x12\x1e\n\x1a\x43ustomIndicatorField_MA250\x10\x08\x12\x1c\n\x18\x43ustomIndicatorField_RSI\x10\t\x12\x1d\n\x19\x43ustomIndicatorField_EMA5\x10\n\x12\x1e\n\x1a\x43ustomIndicatorField_EMA10\x10\x0b\x12\x1e\n\x1a\x43ustomIndicatorField_EMA20\x10\x0c\x12\x1e\n\x1a\x43ustomIndicatorField_EMA30\x10\r\x12\x1e\n\x1a\x43ustomIndicatorField_EMA60\x10\x0e\x12\x1f\n\x1b\x43ustomIndicatorField_EMA120\x10\x0f\x12\x1f\n\x1b\x43ustomIndicatorField_EMA250\x10\x10\x12\x1e\n\x1a\x43ustomIndicatorField_Value\x10\x11\x12\x1b\n\x17\x43ustomIndicatorField_MA\x10\x1e\x12\x1c\n\x18\x43ustomIndicatorField_EMA\x10(\x12\x1e\n\x1a\x43ustomIndicatorField_KDJ_K\x10\x32\x12\x1e\n\x1a\x43ustomIndicatorField_KDJ_D\x10\x33\x12\x1e\n\x1a\x43ustomIndicatorField_KDJ_J\x10\x34\x12\"\n\x1e\x43ustomIndicatorField_MACD_DIFF\x10<\x12!\n\x1d\x43ustomIndicatorField_MACD_DEA\x10=\x12\x1d\n\x19\x43ustomIndicatorField_MACD\x10>\x12#\n\x1f\x43ustomIndicatorField_BOLL_UPPER\x10\x46\x12%\n!CustomIndicatorField_BOLL_MIDDLER\x10G\x12#\n\x1f\x43ustomIndicatorField_BOLL_LOWER\x10H*\xec\x05\n\x0cPatternField\x12\x18\n\x14PatternField_Unknown\x10\x00\x12 \n\x1cPatternField_MAAlignmentLong\x10\x01\x12!\n\x1dPatternField_MAAlignmentShort\x10\x02\x12!\n\x1dPatternField_EMAAlignmentLong\x10\x03\x12\"\n\x1ePatternField_EMAAlignmentShort\x10\x04\x12 \n\x1cPatternField_RSIGoldCrossLow\x10\x05\x12\"\n\x1ePatternField_RSIDeathCrossHigh\x10\x06\x12!\n\x1dPatternField_RSITopDivergence\x10\x07\x12$\n PatternField_RSIBottomDivergence\x10\x08\x12 \n\x1cPatternField_KDJGoldCrossLow\x10\t\x12\"\n\x1ePatternField_KDJDeathCrossHigh\x10\n\x12!\n\x1dPatternField_KDJTopDivergence\x10\x0b\x12$\n PatternField_KDJBottomDivergence\x10\x0c\x12!\n\x1dPatternField_MACDGoldCrossLow\x10\r\x12#\n\x1fPatternField_MACDDeathCrossHigh\x10\x0e\x12\"\n\x1ePatternField_MACDTopDivergence\x10\x0f\x12%\n!PatternField_MACDBottomDivergence\x10\x10\x12\x1f\n\x1bPatternField_BOLLBreakUpper\x10\x11\x12\x1a\n\x16PatternField_BOLLLower\x10\x12\x12\"\n\x1ePatternField_BOLLCrossMiddleUp\x10\x13\x12$\n PatternField_BOLLCrossMiddleDown\x10\x14*\xd9\x01\n\x10\x46inancialQuarter\x12\x1c\n\x18\x46inancialQuarter_Unknown\x10\x00\x12\x1b\n\x17\x46inancialQuarter_Annual\x10\x01\x12!\n\x1d\x46inancialQuarter_FirstQuarter\x10\x02\x12\x1c\n\x18\x46inancialQuarter_Interim\x10\x03\x12!\n\x1d\x46inancialQuarter_ThirdQuarter\x10\x04\x12&\n\"FinancialQuarter_MostRecentQuarter\x10\x05*\xa4\x01\n\x10RelativePosition\x12\x1c\n\x18RelativePosition_Unknown\x10\x00\x12\x19\n\x15RelativePosition_More\x10\x01\x12\x19\n\x15RelativePosition_Less\x10\x02\x12\x1c\n\x18RelativePosition_CrossUp\x10\x03\x12\x1e\n\x1aRelativePosition_CrossDown\x10\x04*B\n\x07SortDir\x12\x0e\n\nSortDir_No\x10\x00\x12\x12\n\x0eSortDir_Ascend\x10\x01\x12\x13\n\x0fSortDir_Descend\x10\x02\x42\x45\n\x13\x63om.futu.openapi.pbZ.github.com/futuopen/ftapi4go/pb/qotstockfilter')
  ,
  dependencies=[Common__pb2.DESCRIPTOR,Qot__Common__pb2.DESCRIPTOR,])

_STOCKFIELD = _descriptor.EnumDescriptor(
  name='StockField',
  full_name='Qot_StockFilter.StockField',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='StockField_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_StockCode', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_StockName', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_CurPrice', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_CurPriceToHighest52WeeksRatio', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_CurPriceToLowest52WeeksRatio', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_HighPriceToHighest52WeeksRatio', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_LowPriceToLowest52WeeksRatio', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_VolumeRatio', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_BidAskRatio', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_LotPrice', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_MarketVal', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_PeAnnual', index=12, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_PeTTM', index=13, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_PbRate', index=14, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_ChangeRate5min', index=15, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_ChangeRateBeginYear', index=16, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_PSTTM', index=17, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_PCFTTM', index=18, number=18,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_TotalShare', index=19, number=19,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_FloatShare', index=20, number=20,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='StockField_FloatMarketVal', index=21, number=21,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2008,
  serialized_end=2678,
)
_sym_db.RegisterEnumDescriptor(_STOCKFIELD)

StockField = enum_type_wrapper.EnumTypeWrapper(_STOCKFIELD)
_ACCUMULATEFIELD = _descriptor.EnumDescriptor(
  name='AccumulateField',
  full_name='Qot_StockFilter.AccumulateField',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='AccumulateField_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AccumulateField_ChangeRate', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AccumulateField_Amplitude', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AccumulateField_Volume', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AccumulateField_Turnover', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AccumulateField_TurnoverRate', index=5, number=5,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2681,
  serialized_end=2882,
)
_sym_db.RegisterEnumDescriptor(_ACCUMULATEFIELD)

AccumulateField = enum_type_wrapper.EnumTypeWrapper(_ACCUMULATEFIELD)
_FINANCIALFIELD = _descriptor.EnumDescriptor(
  name='FinancialField',
  full_name='Qot_StockFilter.FinancialField',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FinancialField_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_NetProfit', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_NetProfitGrowth', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_SumOfBusiness', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_SumOfBusinessGrowth', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_NetProfitRate', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_GrossProfitRate', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_DebtAssetsRate', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_ReturnOnEquityRate', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_ROIC', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_ROATTM', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_EBITTTM', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_EBITDA', index=12, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_OperatingMarginTTM', index=13, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_EBITMargin', index=14, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_EBITDAMargin', index=15, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_FinancialCostRate', index=16, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_OperatingProfitTTM', index=17, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_ShareholderNetProfitTTM', index=18, number=18,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_NetProfitCashCoverTTM', index=19, number=19,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_CurrentRatio', index=20, number=20,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_QuickRatio', index=21, number=21,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_CurrentAssetRatio', index=22, number=22,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_CurrentDebtRatio', index=23, number=23,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_EquityMultiplier', index=24, number=24,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_PropertyRatio', index=25, number=25,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_CashAndCashEquivalents', index=26, number=26,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_TotalAssetTurnover', index=27, number=27,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_FixedAssetTurnover', index=28, number=28,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_InventoryTurnover', index=29, number=29,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_OperatingCashFlowTTM', index=30, number=30,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_AccountsReceivable', index=31, number=31,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_EBITGrowthRate', index=32, number=32,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_OperatingProfitGrowthRate', index=33, number=33,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_TotalAssetsGrowthRate', index=34, number=34,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_ProfitToShareholdersGrowthRate', index=35, number=35,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_ProfitBeforeTaxGrowthRate', index=36, number=36,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_EPSGrowthRate', index=37, number=37,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_ROEGrowthRate', index=38, number=38,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_ROICGrowthRate', index=39, number=39,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_NOCFGrowthRate', index=40, number=40,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_NOCFPerShareGrowthRate', index=41, number=41,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_OperatingRevenueCashCover', index=42, number=42,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_OperatingProfitToTotalProfit', index=43, number=43,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_BasicEPS', index=44, number=44,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_DilutedEPS', index=45, number=45,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialField_NOCFPerShare', index=46, number=46,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2885,
  serialized_end=4624,
)
_sym_db.RegisterEnumDescriptor(_FINANCIALFIELD)

FinancialField = enum_type_wrapper.EnumTypeWrapper(_FINANCIALFIELD)
_CUSTOMINDICATORFIELD = _descriptor.EnumDescriptor(
  name='CustomIndicatorField',
  full_name='Qot_StockFilter.CustomIndicatorField',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_Price', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MA5', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MA10', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MA20', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MA30', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MA60', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MA120', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MA250', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_RSI', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_EMA5', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_EMA10', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_EMA20', index=12, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_EMA30', index=13, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_EMA60', index=14, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_EMA120', index=15, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_EMA250', index=16, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_Value', index=17, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MA', index=18, number=30,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_EMA', index=19, number=40,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_KDJ_K', index=20, number=50,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_KDJ_D', index=21, number=51,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_KDJ_J', index=22, number=52,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MACD_DIFF', index=23, number=60,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MACD_DEA', index=24, number=61,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_MACD', index=25, number=62,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_BOLL_UPPER', index=26, number=70,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_BOLL_MIDDLER', index=27, number=71,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CustomIndicatorField_BOLL_LOWER', index=28, number=72,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4627,
  serialized_end=5590,
)
_sym_db.RegisterEnumDescriptor(_CUSTOMINDICATORFIELD)

CustomIndicatorField = enum_type_wrapper.EnumTypeWrapper(_CUSTOMINDICATORFIELD)
_PATTERNFIELD = _descriptor.EnumDescriptor(
  name='PatternField',
  full_name='Qot_StockFilter.PatternField',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PatternField_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_MAAlignmentLong', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_MAAlignmentShort', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_EMAAlignmentLong', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_EMAAlignmentShort', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_RSIGoldCrossLow', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_RSIDeathCrossHigh', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_RSITopDivergence', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_RSIBottomDivergence', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_KDJGoldCrossLow', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_KDJDeathCrossHigh', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_KDJTopDivergence', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_KDJBottomDivergence', index=12, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_MACDGoldCrossLow', index=13, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_MACDDeathCrossHigh', index=14, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_MACDTopDivergence', index=15, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_MACDBottomDivergence', index=16, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_BOLLBreakUpper', index=17, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_BOLLLower', index=18, number=18,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_BOLLCrossMiddleUp', index=19, number=19,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PatternField_BOLLCrossMiddleDown', index=20, number=20,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5593,
  serialized_end=6341,
)
_sym_db.RegisterEnumDescriptor(_PATTERNFIELD)

PatternField = enum_type_wrapper.EnumTypeWrapper(_PATTERNFIELD)
_FINANCIALQUARTER = _descriptor.EnumDescriptor(
  name='FinancialQuarter',
  full_name='Qot_StockFilter.FinancialQuarter',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FinancialQuarter_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialQuarter_Annual', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialQuarter_FirstQuarter', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialQuarter_Interim', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialQuarter_ThirdQuarter', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FinancialQuarter_MostRecentQuarter', index=5, number=5,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=6344,
  serialized_end=6561,
)
_sym_db.RegisterEnumDescriptor(_FINANCIALQUARTER)

FinancialQuarter = enum_type_wrapper.EnumTypeWrapper(_FINANCIALQUARTER)
_RELATIVEPOSITION = _descriptor.EnumDescriptor(
  name='RelativePosition',
  full_name='Qot_StockFilter.RelativePosition',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='RelativePosition_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RelativePosition_More', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RelativePosition_Less', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RelativePosition_CrossUp', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RelativePosition_CrossDown', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=6564,
  serialized_end=6728,
)
_sym_db.RegisterEnumDescriptor(_RELATIVEPOSITION)

RelativePosition = enum_type_wrapper.EnumTypeWrapper(_RELATIVEPOSITION)
_SORTDIR = _descriptor.EnumDescriptor(
  name='SortDir',
  full_name='Qot_StockFilter.SortDir',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SortDir_No', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortDir_Ascend', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortDir_Descend', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=6730,
  serialized_end=6796,
)
_sym_db.RegisterEnumDescriptor(_SORTDIR)

SortDir = enum_type_wrapper.EnumTypeWrapper(_SORTDIR)
StockField_Unknown = 0
StockField_StockCode = 1
StockField_StockName = 2
StockField_CurPrice = 3
StockField_CurPriceToHighest52WeeksRatio = 4
StockField_CurPriceToLowest52WeeksRatio = 5
StockField_HighPriceToHighest52WeeksRatio = 6
StockField_LowPriceToLowest52WeeksRatio = 7
StockField_VolumeRatio = 8
StockField_BidAskRatio = 9
StockField_LotPrice = 10
StockField_MarketVal = 11
StockField_PeAnnual = 12
StockField_PeTTM = 13
StockField_PbRate = 14
StockField_ChangeRate5min = 15
StockField_ChangeRateBeginYear = 16
StockField_PSTTM = 17
StockField_PCFTTM = 18
StockField_TotalShare = 19
StockField_FloatShare = 20
StockField_FloatMarketVal = 21
AccumulateField_Unknown = 0
AccumulateField_ChangeRate = 1
AccumulateField_Amplitude = 2
AccumulateField_Volume = 3
AccumulateField_Turnover = 4
AccumulateField_TurnoverRate = 5
FinancialField_Unknown = 0
FinancialField_NetProfit = 1
FinancialField_NetProfitGrowth = 2
FinancialField_SumOfBusiness = 3
FinancialField_SumOfBusinessGrowth = 4
FinancialField_NetProfitRate = 5
FinancialField_GrossProfitRate = 6
FinancialField_DebtAssetsRate = 7
FinancialField_ReturnOnEquityRate = 8
FinancialField_ROIC = 9
FinancialField_ROATTM = 10
FinancialField_EBITTTM = 11
FinancialField_EBITDA = 12
FinancialField_OperatingMarginTTM = 13
FinancialField_EBITMargin = 14
FinancialField_EBITDAMargin = 15
FinancialField_FinancialCostRate = 16
FinancialField_OperatingProfitTTM = 17
FinancialField_ShareholderNetProfitTTM = 18
FinancialField_NetProfitCashCoverTTM = 19
FinancialField_CurrentRatio = 20
FinancialField_QuickRatio = 21
FinancialField_CurrentAssetRatio = 22
FinancialField_CurrentDebtRatio = 23
FinancialField_EquityMultiplier = 24
FinancialField_PropertyRatio = 25
FinancialField_CashAndCashEquivalents = 26
FinancialField_TotalAssetTurnover = 27
FinancialField_FixedAssetTurnover = 28
FinancialField_InventoryTurnover = 29
FinancialField_OperatingCashFlowTTM = 30
FinancialField_AccountsReceivable = 31
FinancialField_EBITGrowthRate = 32
FinancialField_OperatingProfitGrowthRate = 33
FinancialField_TotalAssetsGrowthRate = 34
FinancialField_ProfitToShareholdersGrowthRate = 35
FinancialField_ProfitBeforeTaxGrowthRate = 36
FinancialField_EPSGrowthRate = 37
FinancialField_ROEGrowthRate = 38
FinancialField_ROICGrowthRate = 39
FinancialField_NOCFGrowthRate = 40
FinancialField_NOCFPerShareGrowthRate = 41
FinancialField_OperatingRevenueCashCover = 42
FinancialField_OperatingProfitToTotalProfit = 43
FinancialField_BasicEPS = 44
FinancialField_DilutedEPS = 45
FinancialField_NOCFPerShare = 46
CustomIndicatorField_Unknown = 0
CustomIndicatorField_Price = 1
CustomIndicatorField_MA5 = 2
CustomIndicatorField_MA10 = 3
CustomIndicatorField_MA20 = 4
CustomIndicatorField_MA30 = 5
CustomIndicatorField_MA60 = 6
CustomIndicatorField_MA120 = 7
CustomIndicatorField_MA250 = 8
CustomIndicatorField_RSI = 9
CustomIndicatorField_EMA5 = 10
CustomIndicatorField_EMA10 = 11
CustomIndicatorField_EMA20 = 12
CustomIndicatorField_EMA30 = 13
CustomIndicatorField_EMA60 = 14
CustomIndicatorField_EMA120 = 15
CustomIndicatorField_EMA250 = 16
CustomIndicatorField_Value = 17
CustomIndicatorField_MA = 30
CustomIndicatorField_EMA = 40
CustomIndicatorField_KDJ_K = 50
CustomIndicatorField_KDJ_D = 51
CustomIndicatorField_KDJ_J = 52
CustomIndicatorField_MACD_DIFF = 60
CustomIndicatorField_MACD_DEA = 61
CustomIndicatorField_MACD = 62
CustomIndicatorField_BOLL_UPPER = 70
CustomIndicatorField_BOLL_MIDDLER = 71
CustomIndicatorField_BOLL_LOWER = 72
PatternField_Unknown = 0
PatternField_MAAlignmentLong = 1
PatternField_MAAlignmentShort = 2
PatternField_EMAAlignmentLong = 3
PatternField_EMAAlignmentShort = 4
PatternField_RSIGoldCrossLow = 5
PatternField_RSIDeathCrossHigh = 6
PatternField_RSITopDivergence = 7
PatternField_RSIBottomDivergence = 8
PatternField_KDJGoldCrossLow = 9
PatternField_KDJDeathCrossHigh = 10
PatternField_KDJTopDivergence = 11
PatternField_KDJBottomDivergence = 12
PatternField_MACDGoldCrossLow = 13
PatternField_MACDDeathCrossHigh = 14
PatternField_MACDTopDivergence = 15
PatternField_MACDBottomDivergence = 16
PatternField_BOLLBreakUpper = 17
PatternField_BOLLLower = 18
PatternField_BOLLCrossMiddleUp = 19
PatternField_BOLLCrossMiddleDown = 20
FinancialQuarter_Unknown = 0
FinancialQuarter_Annual = 1
FinancialQuarter_FirstQuarter = 2
FinancialQuarter_Interim = 3
FinancialQuarter_ThirdQuarter = 4
FinancialQuarter_MostRecentQuarter = 5
RelativePosition_Unknown = 0
RelativePosition_More = 1
RelativePosition_Less = 2
RelativePosition_CrossUp = 3
RelativePosition_CrossDown = 4
SortDir_No = 0
SortDir_Ascend = 1
SortDir_Descend = 2



_BASEFILTER = _descriptor.Descriptor(
  name='BaseFilter',
  full_name='Qot_StockFilter.BaseFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fieldName', full_name='Qot_StockFilter.BaseFilter.fieldName', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filterMin', full_name='Qot_StockFilter.BaseFilter.filterMin', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filterMax', full_name='Qot_StockFilter.BaseFilter.filterMax', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isNoFilter', full_name='Qot_StockFilter.BaseFilter.isNoFilter', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sortDir', full_name='Qot_StockFilter.BaseFilter.sortDir', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=74,
  serialized_end=180,
)


_ACCUMULATEFILTER = _descriptor.Descriptor(
  name='AccumulateFilter',
  full_name='Qot_StockFilter.AccumulateFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fieldName', full_name='Qot_StockFilter.AccumulateFilter.fieldName', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filterMin', full_name='Qot_StockFilter.AccumulateFilter.filterMin', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filterMax', full_name='Qot_StockFilter.AccumulateFilter.filterMax', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isNoFilter', full_name='Qot_StockFilter.AccumulateFilter.isNoFilter', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sortDir', full_name='Qot_StockFilter.AccumulateFilter.sortDir', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='days', full_name='Qot_StockFilter.AccumulateFilter.days', index=5,
      number=6, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=182,
  serialized_end=308,
)


_FINANCIALFILTER = _descriptor.Descriptor(
  name='FinancialFilter',
  full_name='Qot_StockFilter.FinancialFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fieldName', full_name='Qot_StockFilter.FinancialFilter.fieldName', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filterMin', full_name='Qot_StockFilter.FinancialFilter.filterMin', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filterMax', full_name='Qot_StockFilter.FinancialFilter.filterMax', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isNoFilter', full_name='Qot_StockFilter.FinancialFilter.isNoFilter', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sortDir', full_name='Qot_StockFilter.FinancialFilter.sortDir', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quarter', full_name='Qot_StockFilter.FinancialFilter.quarter', index=5,
      number=6, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=311,
  serialized_end=439,
)


_PATTERNFILTER = _descriptor.Descriptor(
  name='PatternFilter',
  full_name='Qot_StockFilter.PatternFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fieldName', full_name='Qot_StockFilter.PatternFilter.fieldName', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='klType', full_name='Qot_StockFilter.PatternFilter.klType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isNoFilter', full_name='Qot_StockFilter.PatternFilter.isNoFilter', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='consecutivePeriod', full_name='Qot_StockFilter.PatternFilter.consecutivePeriod', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=441,
  serialized_end=538,
)


_CUSTOMINDICATORFILTER = _descriptor.Descriptor(
  name='CustomIndicatorFilter',
  full_name='Qot_StockFilter.CustomIndicatorFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='firstFieldName', full_name='Qot_StockFilter.CustomIndicatorFilter.firstFieldName', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='secondFieldName', full_name='Qot_StockFilter.CustomIndicatorFilter.secondFieldName', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relativePosition', full_name='Qot_StockFilter.CustomIndicatorFilter.relativePosition', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fieldValue', full_name='Qot_StockFilter.CustomIndicatorFilter.fieldValue', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='klType', full_name='Qot_StockFilter.CustomIndicatorFilter.klType', index=4,
      number=5, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isNoFilter', full_name='Qot_StockFilter.CustomIndicatorFilter.isNoFilter', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='firstFieldParaList', full_name='Qot_StockFilter.CustomIndicatorFilter.firstFieldParaList', index=6,
      number=7, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='secondFieldParaList', full_name='Qot_StockFilter.CustomIndicatorFilter.secondFieldParaList', index=7,
      number=8, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='consecutivePeriod', full_name='Qot_StockFilter.CustomIndicatorFilter.consecutivePeriod', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=541,
  serialized_end=779,
)


_BASEDATA = _descriptor.Descriptor(
  name='BaseData',
  full_name='Qot_StockFilter.BaseData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fieldName', full_name='Qot_StockFilter.BaseData.fieldName', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='Qot_StockFilter.BaseData.value', index=1,
      number=2, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=781,
  serialized_end=825,
)


_ACCUMULATEDATA = _descriptor.Descriptor(
  name='AccumulateData',
  full_name='Qot_StockFilter.AccumulateData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fieldName', full_name='Qot_StockFilter.AccumulateData.fieldName', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='Qot_StockFilter.AccumulateData.value', index=1,
      number=2, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='days', full_name='Qot_StockFilter.AccumulateData.days', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=827,
  serialized_end=891,
)


_FINANCIALDATA = _descriptor.Descriptor(
  name='FinancialData',
  full_name='Qot_StockFilter.FinancialData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fieldName', full_name='Qot_StockFilter.FinancialData.fieldName', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='Qot_StockFilter.FinancialData.value', index=1,
      number=2, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quarter', full_name='Qot_StockFilter.FinancialData.quarter', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=893,
  serialized_end=959,
)


_CUSTOMINDICATORDATA = _descriptor.Descriptor(
  name='CustomIndicatorData',
  full_name='Qot_StockFilter.CustomIndicatorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fieldName', full_name='Qot_StockFilter.CustomIndicatorData.fieldName', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='Qot_StockFilter.CustomIndicatorData.value', index=1,
      number=2, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='klType', full_name='Qot_StockFilter.CustomIndicatorData.klType', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fieldParaList', full_name='Qot_StockFilter.CustomIndicatorData.fieldParaList', index=3,
      number=4, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=961,
  serialized_end=1055,
)


_STOCKDATA = _descriptor.Descriptor(
  name='StockData',
  full_name='Qot_StockFilter.StockData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='security', full_name='Qot_StockFilter.StockData.security', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Qot_StockFilter.StockData.name', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='baseDataList', full_name='Qot_StockFilter.StockData.baseDataList', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accumulateDataList', full_name='Qot_StockFilter.StockData.accumulateDataList', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='financialDataList', full_name='Qot_StockFilter.StockData.financialDataList', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='customIndicatorDataList', full_name='Qot_StockFilter.StockData.customIndicatorDataList', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1058,
  serialized_end=1363,
)


_C2S = _descriptor.Descriptor(
  name='C2S',
  full_name='Qot_StockFilter.C2S',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='begin', full_name='Qot_StockFilter.C2S.begin', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num', full_name='Qot_StockFilter.C2S.num', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='market', full_name='Qot_StockFilter.C2S.market', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plate', full_name='Qot_StockFilter.C2S.plate', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='baseFilterList', full_name='Qot_StockFilter.C2S.baseFilterList', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accumulateFilterList', full_name='Qot_StockFilter.C2S.accumulateFilterList', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='financialFilterList', full_name='Qot_StockFilter.C2S.financialFilterList', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='patternFilterList', full_name='Qot_StockFilter.C2S.patternFilterList', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='customIndicatorFilterList', full_name='Qot_StockFilter.C2S.customIndicatorFilterList', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1366,
  serialized_end=1767,
)


_S2C = _descriptor.Descriptor(
  name='S2C',
  full_name='Qot_StockFilter.S2C',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lastPage', full_name='Qot_StockFilter.S2C.lastPage', index=0,
      number=1, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allCount', full_name='Qot_StockFilter.S2C.allCount', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dataList', full_name='Qot_StockFilter.S2C.dataList', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1769,
  serialized_end=1856,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='Qot_StockFilter.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='c2s', full_name='Qot_StockFilter.Request.c2s', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1858,
  serialized_end=1902,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Qot_StockFilter.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='retType', full_name='Qot_StockFilter.Response.retType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=True, default_value=-400,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retMsg', full_name='Qot_StockFilter.Response.retMsg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errCode', full_name='Qot_StockFilter.Response.errCode', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s2c', full_name='Qot_StockFilter.Response.s2c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1904,
  serialized_end=2005,
)

_STOCKDATA.fields_by_name['security'].message_type = Qot__Common__pb2._SECURITY
_STOCKDATA.fields_by_name['baseDataList'].message_type = _BASEDATA
_STOCKDATA.fields_by_name['accumulateDataList'].message_type = _ACCUMULATEDATA
_STOCKDATA.fields_by_name['financialDataList'].message_type = _FINANCIALDATA
_STOCKDATA.fields_by_name['customIndicatorDataList'].message_type = _CUSTOMINDICATORDATA
_C2S.fields_by_name['plate'].message_type = Qot__Common__pb2._SECURITY
_C2S.fields_by_name['baseFilterList'].message_type = _BASEFILTER
_C2S.fields_by_name['accumulateFilterList'].message_type = _ACCUMULATEFILTER
_C2S.fields_by_name['financialFilterList'].message_type = _FINANCIALFILTER
_C2S.fields_by_name['patternFilterList'].message_type = _PATTERNFILTER
_C2S.fields_by_name['customIndicatorFilterList'].message_type = _CUSTOMINDICATORFILTER
_S2C.fields_by_name['dataList'].message_type = _STOCKDATA
_REQUEST.fields_by_name['c2s'].message_type = _C2S
_RESPONSE.fields_by_name['s2c'].message_type = _S2C
DESCRIPTOR.message_types_by_name['BaseFilter'] = _BASEFILTER
DESCRIPTOR.message_types_by_name['AccumulateFilter'] = _ACCUMULATEFILTER
DESCRIPTOR.message_types_by_name['FinancialFilter'] = _FINANCIALFILTER
DESCRIPTOR.message_types_by_name['PatternFilter'] = _PATTERNFILTER
DESCRIPTOR.message_types_by_name['CustomIndicatorFilter'] = _CUSTOMINDICATORFILTER
DESCRIPTOR.message_types_by_name['BaseData'] = _BASEDATA
DESCRIPTOR.message_types_by_name['AccumulateData'] = _ACCUMULATEDATA
DESCRIPTOR.message_types_by_name['FinancialData'] = _FINANCIALDATA
DESCRIPTOR.message_types_by_name['CustomIndicatorData'] = _CUSTOMINDICATORDATA
DESCRIPTOR.message_types_by_name['StockData'] = _STOCKDATA
DESCRIPTOR.message_types_by_name['C2S'] = _C2S
DESCRIPTOR.message_types_by_name['S2C'] = _S2C
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.enum_types_by_name['StockField'] = _STOCKFIELD
DESCRIPTOR.enum_types_by_name['AccumulateField'] = _ACCUMULATEFIELD
DESCRIPTOR.enum_types_by_name['FinancialField'] = _FINANCIALFIELD
DESCRIPTOR.enum_types_by_name['CustomIndicatorField'] = _CUSTOMINDICATORFIELD
DESCRIPTOR.enum_types_by_name['PatternField'] = _PATTERNFIELD
DESCRIPTOR.enum_types_by_name['FinancialQuarter'] = _FINANCIALQUARTER
DESCRIPTOR.enum_types_by_name['RelativePosition'] = _RELATIVEPOSITION
DESCRIPTOR.enum_types_by_name['SortDir'] = _SORTDIR
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

BaseFilter = _reflection.GeneratedProtocolMessageType('BaseFilter', (_message.Message,), dict(
  DESCRIPTOR = _BASEFILTER,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.BaseFilter)
  ))
_sym_db.RegisterMessage(BaseFilter)

AccumulateFilter = _reflection.GeneratedProtocolMessageType('AccumulateFilter', (_message.Message,), dict(
  DESCRIPTOR = _ACCUMULATEFILTER,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.AccumulateFilter)
  ))
_sym_db.RegisterMessage(AccumulateFilter)

FinancialFilter = _reflection.GeneratedProtocolMessageType('FinancialFilter', (_message.Message,), dict(
  DESCRIPTOR = _FINANCIALFILTER,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.FinancialFilter)
  ))
_sym_db.RegisterMessage(FinancialFilter)

PatternFilter = _reflection.GeneratedProtocolMessageType('PatternFilter', (_message.Message,), dict(
  DESCRIPTOR = _PATTERNFILTER,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.PatternFilter)
  ))
_sym_db.RegisterMessage(PatternFilter)

CustomIndicatorFilter = _reflection.GeneratedProtocolMessageType('CustomIndicatorFilter', (_message.Message,), dict(
  DESCRIPTOR = _CUSTOMINDICATORFILTER,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.CustomIndicatorFilter)
  ))
_sym_db.RegisterMessage(CustomIndicatorFilter)

BaseData = _reflection.GeneratedProtocolMessageType('BaseData', (_message.Message,), dict(
  DESCRIPTOR = _BASEDATA,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.BaseData)
  ))
_sym_db.RegisterMessage(BaseData)

AccumulateData = _reflection.GeneratedProtocolMessageType('AccumulateData', (_message.Message,), dict(
  DESCRIPTOR = _ACCUMULATEDATA,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.AccumulateData)
  ))
_sym_db.RegisterMessage(AccumulateData)

FinancialData = _reflection.GeneratedProtocolMessageType('FinancialData', (_message.Message,), dict(
  DESCRIPTOR = _FINANCIALDATA,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.FinancialData)
  ))
_sym_db.RegisterMessage(FinancialData)

CustomIndicatorData = _reflection.GeneratedProtocolMessageType('CustomIndicatorData', (_message.Message,), dict(
  DESCRIPTOR = _CUSTOMINDICATORDATA,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.CustomIndicatorData)
  ))
_sym_db.RegisterMessage(CustomIndicatorData)

StockData = _reflection.GeneratedProtocolMessageType('StockData', (_message.Message,), dict(
  DESCRIPTOR = _STOCKDATA,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.StockData)
  ))
_sym_db.RegisterMessage(StockData)

C2S = _reflection.GeneratedProtocolMessageType('C2S', (_message.Message,), dict(
  DESCRIPTOR = _C2S,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.C2S)
  ))
_sym_db.RegisterMessage(C2S)

S2C = _reflection.GeneratedProtocolMessageType('S2C', (_message.Message,), dict(
  DESCRIPTOR = _S2C,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.S2C)
  ))
_sym_db.RegisterMessage(S2C)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'Qot_StockFilter_pb2'
  # @@protoc_insertion_point(class_scope:Qot_StockFilter.Response)
  ))
_sym_db.RegisterMessage(Response)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ.github.com/futuopen/ftapi4go/pb/qotstockfilter'))
# @@protoc_insertion_point(module_scope)
