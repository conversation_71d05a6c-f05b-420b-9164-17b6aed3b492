#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復版恆生指數期貨實時監控程式
解決了所有導入和類名問題

配置信息:
- 牛牛號: 22188140
- 期貨權限: LV1
"""

import time
import datetime
from futu import OpenQuoteContext, RET_OK, SubType, StockQuoteHandlerBase

class HSIFuturesMonitor:
    """恆生指數期貨監控器"""
    
    def __init__(self, host='127.0.0.1', port=11111):
        self.host = host
        self.port = port
        self.quote_ctx = None
        self.hsi_code = "HK.HSImain"
        self.is_running = False
        self.last_price = None
        
    def connect(self):
        """連接到 FUTU OpenD"""
        try:
            self.quote_ctx = OpenQuoteContext(host=self.host, port=self.port)
            print(f"✅ 成功連接到 FUTU OpenD ({self.host}:{self.port})")
            return True
        except Exception as e:
            print(f"❌ 連接失敗: {e}")
            return False
    
    def subscribe_hsi_futures(self):
        """訂閱恆生指數期貨"""
        try:
            ret_sub, err_message = self.quote_ctx.subscribe(
                [self.hsi_code], 
                [SubType.QUOTE], 
                subscribe_push=True
            )
            
            if ret_sub == RET_OK:
                print(f"✅ 成功訂閱 {self.hsi_code} 實時行情")
                
                # 設置推送處理器
                handler = HSIQuoteHandler(self)
                self.quote_ctx.set_handler(handler)
                
                return True
            else:
                print(f"❌ 訂閱失敗: {err_message}")
                return False
                
        except Exception as e:
            print(f"❌ 訂閱過程發生錯誤: {e}")
            return False
    
    def get_current_price(self):
        """獲取當前價格"""
        try:
            ret, data = self.quote_ctx.get_stock_quote([self.hsi_code])
            if ret == RET_OK and not data.empty:
                return data.iloc[0]
            else:
                print(f"❌ 獲取價格失敗: {data}")
                return None
        except Exception as e:
            print(f"❌ 獲取價格時發生錯誤: {e}")
            return None
    
    def display_price_info(self, quote_data):
        """顯示價格信息"""
        if quote_data is None:
            return
            
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print("\n" + "="*60)
        print(f"📊 恆生指數期貨實時行情 - {current_time}")
        print("="*60)
        print(f"代碼: {quote_data['code']}")
        print(f"名稱: {quote_data['name']}")
        print(f"最新價: {quote_data['last_price']:.1f}")
        print(f"開盤價: {quote_data['open_price']:.1f}")
        print(f"最高價: {quote_data['high_price']:.1f}")
        print(f"最低價: {quote_data['low_price']:.1f}")
        print(f"昨收價: {quote_data['prev_close_price']:.1f}")
        
        # 計算漲跌
        change = quote_data['last_price'] - quote_data['prev_close_price']
        change_percent = (change / quote_data['prev_close_price']) * 100
        
        change_symbol = "📈" if change > 0 else "📉" if change < 0 else "➡️"
        print(f"漲跌額: {change_symbol} {change:+.1f}")
        print(f"漲跌幅: {change_symbol} {change_percent:+.2f}%")
        
        print(f"成交量: {quote_data['volume']:,}")
        print(f"成交額: {quote_data['turnover']:,.0f}")
        print(f"數據時間: {quote_data['data_time']}")
        print("="*60)
        
        # 更新最後價格
        self.last_price = quote_data['last_price']
    
    def start_monitoring(self, refresh_interval=5):
        """開始監控"""
        if not self.connect():
            return
            
        if not self.subscribe_hsi_futures():
            return
            
        self.is_running = True
        print(f"\n🚀 開始監控恆生指數期貨，每 {refresh_interval} 秒刷新一次")
        print("📡 實時推送已啟用")
        print("按 Ctrl+C 停止監控\n")
        
        try:
            while self.is_running:
                quote_data = self.get_current_price()
                self.display_price_info(quote_data)
                time.sleep(refresh_interval)
                
        except KeyboardInterrupt:
            print("\n\n⏹️  用戶中斷，停止監控...")
        except Exception as e:
            print(f"\n❌ 監控過程中發生錯誤: {e}")
        finally:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止監控"""
        self.is_running = False
        if self.quote_ctx:
            try:
                self.quote_ctx.unsubscribe([self.hsi_code], [SubType.QUOTE])
                self.quote_ctx.close()
                print("✅ 已斷開連接並清理資源")
            except Exception as e:
                print(f"⚠️  清理資源時發生錯誤: {e}")


class HSIQuoteHandler(StockQuoteHandlerBase):
    """恆指期貨實時報價推送處理器"""
    
    def __init__(self, monitor):
        super().__init__()
        self.monitor = monitor
    
    def on_recv_rsp(self, rsp_pb):
        """接收實時報價推送"""
        ret_code, data = super().on_recv_rsp(rsp_pb)
        
        if ret_code != RET_OK:
            print(f"❌ 推送數據錯誤: {data}")
            return ret_code, data
        
        # 處理推送數據
        for index, row in data.iterrows():
            if row['code'] == 'HK.HSImain':
                current_time = datetime.datetime.now().strftime("%H:%M:%S")
                
                # 只在價格有變化時顯示推送
                if self.monitor.last_price and abs(row['last_price'] - self.monitor.last_price) >= 1:
                    change = row['last_price'] - row['prev_close_price']
                    change_percent = (change / row['prev_close_price']) * 100
                    change_symbol = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                    
                    print(f"\n🔔 [{current_time}] 實時推送更新:")
                    print(f"   恆指期貨: {row['last_price']:.1f} ({change_symbol}{change:+.1f}, {change_percent:+.2f}%)")
                
                self.monitor.last_price = row['last_price']
        
        return ret_code, data


def get_single_quote():
    """獲取單次報價（簡化版本）"""
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        hsi_code = "HK.HSImain"
        print(f"正在連接並獲取 {hsi_code} 報價...")
        
        # 訂閱
        ret_sub, err_message = quote_ctx.subscribe([hsi_code], [SubType.QUOTE])
        
        if ret_sub == RET_OK:
            # 獲取報價
            ret, data = quote_ctx.get_stock_quote([hsi_code])
            
            if ret == RET_OK:
                quote_data = data.iloc[0]
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                print(f"\n📊 恆生指數期貨報價 - {current_time}")
                print("-" * 40)
                print(f"最新價: {quote_data['last_price']:.1f}")
                print(f"漲跌額: {quote_data['last_price'] - quote_data['prev_close_price']:+.1f}")
                print(f"漲跌幅: {((quote_data['last_price'] - quote_data['prev_close_price']) / quote_data['prev_close_price'] * 100):+.2f}%")
                print(f"成交量: {quote_data['volume']:,}")
                print("-" * 40)
                
            else:
                print(f"❌ 獲取報價失敗: {data}")
        else:
            print(f"❌ 訂閱失敗: {err_message}")
            
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")
    finally:
        quote_ctx.close()


def main():
    """主函數"""
    print("🎯 恆生指數期貨實時監控程式 (修復版)")
    print("=" * 50)
    print("📋 程式信息:")
    print("   - 數據來源: FUTU API")
    print("   - 監控標的: 恆生指數期貨主連 (HK.HSImain)")
    print("   - 實時推送: 啟用")
    print("=" * 50)
    
    while True:
        print("\n📌 請選擇運行模式:")
        print("1. 獲取一次當前報價")
        print("2. 持續監控模式 (每5秒更新)")
        print("3. 持續監控模式 (每10秒更新)")
        print("4. 退出程式")
        
        choice = input("\n請輸入選項 (1-4): ").strip()
        
        if choice == "1":
            get_single_quote()
            
        elif choice == "2":
            monitor = HSIFuturesMonitor()
            monitor.start_monitoring(refresh_interval=5)
            
        elif choice == "3":
            monitor = HSIFuturesMonitor()
            monitor.start_monitoring(refresh_interval=10)
            
        elif choice == "4":
            print("👋 程式已退出")
            break
            
        else:
            print("❌ 無效選項，請重新選擇")


if __name__ == "__main__":
    main()
