# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Qot_Common.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Common_pb2 as Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Qot_Common.proto',
  package='Qot_Common',
  syntax='proto2',
  serialized_pb=_b('\n\x10Qot_Common.proto\x12\nQot_Common\x1a\x0c\x43ommon.proto\"(\n\x08Security\x12\x0e\n\x06market\x18\x01 \x02(\x05\x12\x0c\n\x04\x63ode\x18\x02 \x02(\t\"\xf5\x01\n\x05KLine\x12\x0c\n\x04time\x18\x01 \x02(\t\x12\x0f\n\x07isBlank\x18\x02 \x02(\x08\x12\x11\n\thighPrice\x18\x03 \x01(\x01\x12\x11\n\topenPrice\x18\x04 \x01(\x01\x12\x10\n\x08lowPrice\x18\x05 \x01(\x01\x12\x12\n\nclosePrice\x18\x06 \x01(\x01\x12\x16\n\x0elastClosePrice\x18\x07 \x01(\x01\x12\x0e\n\x06volume\x18\x08 \x01(\x03\x12\x10\n\x08turnover\x18\t \x01(\x01\x12\x14\n\x0cturnoverRate\x18\n \x01(\x01\x12\n\n\x02pe\x18\x0b \x01(\x01\x12\x12\n\nchangeRate\x18\x0c \x01(\x01\x12\x11\n\ttimestamp\x18\r \x01(\x01\"\xa2\x03\n\x14OptionBasicQotExData\x12\x13\n\x0bstrikePrice\x18\x01 \x02(\x01\x12\x14\n\x0c\x63ontractSize\x18\x02 \x02(\x05\x12\x19\n\x11\x63ontractSizeFloat\x18\x11 \x01(\x01\x12\x14\n\x0copenInterest\x18\x03 \x02(\x05\x12\x19\n\x11impliedVolatility\x18\x04 \x02(\x01\x12\x0f\n\x07premium\x18\x05 \x02(\x01\x12\r\n\x05\x64\x65lta\x18\x06 \x02(\x01\x12\r\n\x05gamma\x18\x07 \x02(\x01\x12\x0c\n\x04vega\x18\x08 \x02(\x01\x12\r\n\x05theta\x18\t \x02(\x01\x12\x0b\n\x03rho\x18\n \x02(\x01\x12\x17\n\x0fnetOpenInterest\x18\x0b \x01(\x05\x12\x1a\n\x12\x65xpiryDateDistance\x18\x0c \x01(\x05\x12\x1c\n\x14\x63ontractNominalValue\x18\r \x01(\x01\x12\x1a\n\x12ownerLotMultiplier\x18\x0e \x01(\x01\x12\x16\n\x0eoptionAreaType\x18\x0f \x01(\x05\x12\x1a\n\x12\x63ontractMultiplier\x18\x10 \x01(\x01\x12\x17\n\x0findexOptionType\x18\x12 \x01(\x05\"\xa4\x01\n\x12PreAfterMarketData\x12\r\n\x05price\x18\x01 \x01(\x01\x12\x11\n\thighPrice\x18\x02 \x01(\x01\x12\x10\n\x08lowPrice\x18\x03 \x01(\x01\x12\x0e\n\x06volume\x18\x04 \x01(\x03\x12\x10\n\x08turnover\x18\x05 \x01(\x01\x12\x11\n\tchangeVal\x18\x06 \x01(\x01\x12\x12\n\nchangeRate\x18\x07 \x01(\x01\x12\x11\n\tamplitude\x18\x08 \x01(\x01\"u\n\x14\x46utureBasicQotExData\x12\x17\n\x0flastSettlePrice\x18\x01 \x02(\x01\x12\x10\n\x08position\x18\x02 \x02(\x05\x12\x16\n\x0epositionChange\x18\x03 \x02(\x05\x12\x1a\n\x12\x65xpiryDateDistance\x18\x04 \x01(\x05\"R\n\x15WarrantBasicQotExData\x12\r\n\x05\x64\x65lta\x18\x01 \x01(\x01\x12\x19\n\x11impliedVolatility\x18\x02 \x01(\x01\x12\x0f\n\x07premium\x18\x03 \x02(\x01\"\xd9\x05\n\x08\x42\x61sicQot\x12&\n\x08security\x18\x01 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x0c\n\x04name\x18\x18 \x01(\t\x12\x13\n\x0bisSuspended\x18\x02 \x02(\x08\x12\x10\n\x08listTime\x18\x03 \x02(\t\x12\x13\n\x0bpriceSpread\x18\x04 \x02(\x01\x12\x12\n\nupdateTime\x18\x05 \x02(\t\x12\x11\n\thighPrice\x18\x06 \x02(\x01\x12\x11\n\topenPrice\x18\x07 \x02(\x01\x12\x10\n\x08lowPrice\x18\x08 \x02(\x01\x12\x10\n\x08\x63urPrice\x18\t \x02(\x01\x12\x16\n\x0elastClosePrice\x18\n \x02(\x01\x12\x0e\n\x06volume\x18\x0b \x02(\x03\x12\x10\n\x08turnover\x18\x0c \x02(\x01\x12\x14\n\x0cturnoverRate\x18\r \x02(\x01\x12\x11\n\tamplitude\x18\x0e \x02(\x01\x12\x12\n\ndarkStatus\x18\x0f \x01(\x05\x12\x36\n\x0coptionExData\x18\x10 \x01(\x0b\x32 .Qot_Common.OptionBasicQotExData\x12\x15\n\rlistTimestamp\x18\x11 \x01(\x01\x12\x17\n\x0fupdateTimestamp\x18\x12 \x01(\x01\x12\x31\n\tpreMarket\x18\x13 \x01(\x0b\x32\x1e.Qot_Common.PreAfterMarketData\x12\x33\n\x0b\x61\x66terMarket\x18\x14 \x01(\x0b\x32\x1e.Qot_Common.PreAfterMarketData\x12\x11\n\tsecStatus\x18\x15 \x01(\x05\x12\x36\n\x0c\x66utureExData\x18\x16 \x01(\x0b\x32 .Qot_Common.FutureBasicQotExData\x12\x38\n\rwarrantExData\x18\x17 \x01(\x0b\x32!.Qot_Common.WarrantBasicQotExData\x12\x31\n\tovernight\x18\x19 \x01(\x0b\x32\x1e.Qot_Common.PreAfterMarketData\"\xa8\x01\n\tTimeShare\x12\x0c\n\x04time\x18\x01 \x02(\t\x12\x0e\n\x06minute\x18\x02 \x02(\x05\x12\x0f\n\x07isBlank\x18\x03 \x02(\x08\x12\r\n\x05price\x18\x04 \x01(\x01\x12\x16\n\x0elastClosePrice\x18\x05 \x01(\x01\x12\x10\n\x08\x61vgPrice\x18\x06 \x01(\x01\x12\x0e\n\x06volume\x18\x07 \x01(\x03\x12\x10\n\x08turnover\x18\x08 \x01(\x01\x12\x11\n\ttimestamp\x18\t \x01(\x01\"\xc7\x01\n\x13SecurityStaticBasic\x12&\n\x08security\x18\x01 \x02(\x0b\x32\x14.Qot_Common.Security\x12\n\n\x02id\x18\x02 \x02(\x03\x12\x0f\n\x07lotSize\x18\x03 \x02(\x05\x12\x0f\n\x07secType\x18\x04 \x02(\x05\x12\x0c\n\x04name\x18\x05 \x02(\t\x12\x10\n\x08listTime\x18\x06 \x02(\t\x12\x11\n\tdelisting\x18\x07 \x01(\x08\x12\x15\n\rlistTimestamp\x18\x08 \x01(\x01\x12\x10\n\x08\x65xchType\x18\t \x01(\x05\"H\n\x13WarrantStaticExData\x12\x0c\n\x04type\x18\x01 \x02(\x05\x12#\n\x05owner\x18\x02 \x02(\x0b\x32\x14.Qot_Common.Security\"\x96\x02\n\x12OptionStaticExData\x12\x0c\n\x04type\x18\x01 \x02(\x05\x12#\n\x05owner\x18\x02 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x12\n\nstrikeTime\x18\x03 \x02(\t\x12\x13\n\x0bstrikePrice\x18\x04 \x02(\x01\x12\x0f\n\x07suspend\x18\x05 \x02(\x08\x12\x0e\n\x06market\x18\x06 \x02(\t\x12\x17\n\x0fstrikeTimestamp\x18\x07 \x01(\x01\x12\x17\n\x0findexOptionType\x18\x08 \x01(\x05\x12\x17\n\x0f\x65xpirationCycle\x18\t \x01(\x05\x12\x1a\n\x12optionStandardType\x18\n \x01(\x05\x12\x1c\n\x14optionSettlementMode\x18\x0b \x01(\x05\"_\n\x12\x46utureStaticExData\x12\x15\n\rlastTradeTime\x18\x01 \x02(\t\x12\x1a\n\x12lastTradeTimestamp\x18\x02 \x01(\x01\x12\x16\n\x0eisMainContract\x18\x03 \x02(\x08\"\xe8\x01\n\x12SecurityStaticInfo\x12.\n\x05\x62\x61sic\x18\x01 \x02(\x0b\x32\x1f.Qot_Common.SecurityStaticBasic\x12\x36\n\rwarrantExData\x18\x02 \x01(\x0b\x32\x1f.Qot_Common.WarrantStaticExData\x12\x34\n\x0coptionExData\x18\x03 \x01(\x0b\x32\x1e.Qot_Common.OptionStaticExData\x12\x34\n\x0c\x66utureExData\x18\x04 \x01(\x0b\x32\x1e.Qot_Common.FutureStaticExData\"P\n\x06\x42roker\x12\n\n\x02id\x18\x01 \x02(\x03\x12\x0c\n\x04name\x18\x02 \x02(\t\x12\x0b\n\x03pos\x18\x03 \x02(\x05\x12\x0f\n\x07orderID\x18\x04 \x01(\x03\x12\x0e\n\x06volume\x18\x05 \x01(\x03\"\xc1\x01\n\x06Ticker\x12\x0c\n\x04time\x18\x01 \x02(\t\x12\x10\n\x08sequence\x18\x02 \x02(\x03\x12\x0b\n\x03\x64ir\x18\x03 \x02(\x05\x12\r\n\x05price\x18\x04 \x02(\x01\x12\x0e\n\x06volume\x18\x05 \x02(\x03\x12\x10\n\x08turnover\x18\x06 \x02(\x01\x12\x10\n\x08recvTime\x18\x07 \x01(\x01\x12\x0c\n\x04type\x18\x08 \x01(\x05\x12\x10\n\x08typeSign\x18\t \x01(\x05\x12\x14\n\x0cpushDataType\x18\n \x01(\x05\x12\x11\n\ttimestamp\x18\x0b \x01(\x01\"2\n\x0fOrderBookDetail\x12\x0f\n\x07orderID\x18\x01 \x02(\x03\x12\x0e\n\x06volume\x18\x02 \x02(\x03\"p\n\tOrderBook\x12\r\n\x05price\x18\x01 \x02(\x01\x12\x0e\n\x06volume\x18\x02 \x02(\x03\x12\x13\n\x0borederCount\x18\x03 \x02(\x05\x12/\n\ndetailList\x18\x04 \x03(\x0b\x32\x1b.Qot_Common.OrderBookDetail\"\x9b\x01\n\x12ShareHoldingChange\x12\x12\n\nholderName\x18\x01 \x02(\t\x12\x12\n\nholdingQty\x18\x02 \x02(\x01\x12\x14\n\x0choldingRatio\x18\x03 \x02(\x01\x12\x11\n\tchangeQty\x18\x04 \x02(\x01\x12\x13\n\x0b\x63hangeRatio\x18\x05 \x02(\x01\x12\x0c\n\x04time\x18\x06 \x02(\t\x12\x11\n\ttimestamp\x18\x07 \x01(\x01\"F\n\x07SubInfo\x12\x0f\n\x07subType\x18\x01 \x02(\x05\x12*\n\x0csecurityList\x18\x02 \x03(\x0b\x32\x14.Qot_Common.Security\"a\n\x0b\x43onnSubInfo\x12(\n\x0bsubInfoList\x18\x01 \x03(\x0b\x32\x13.Qot_Common.SubInfo\x12\x11\n\tusedQuota\x18\x02 \x02(\x05\x12\x15\n\risOwnConnData\x18\x03 \x02(\x08\"Q\n\tPlateInfo\x12#\n\x05plate\x18\x01 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x0c\n\x04name\x18\x02 \x02(\t\x12\x11\n\tplateType\x18\x03 \x01(\x05\"\xba\x03\n\x05Rehab\x12\x0c\n\x04time\x18\x01 \x02(\t\x12\x16\n\x0e\x63ompanyActFlag\x18\x02 \x02(\x03\x12\x12\n\nfwdFactorA\x18\x03 \x02(\x01\x12\x12\n\nfwdFactorB\x18\x04 \x02(\x01\x12\x12\n\nbwdFactorA\x18\x05 \x02(\x01\x12\x12\n\nbwdFactorB\x18\x06 \x02(\x01\x12\x11\n\tsplitBase\x18\x07 \x01(\x05\x12\x10\n\x08splitErt\x18\x08 \x01(\x05\x12\x10\n\x08joinBase\x18\t \x01(\x05\x12\x0f\n\x07joinErt\x18\n \x01(\x05\x12\x11\n\tbonusBase\x18\x0b \x01(\x05\x12\x10\n\x08\x62onusErt\x18\x0c \x01(\x05\x12\x14\n\x0ctransferBase\x18\r \x01(\x05\x12\x13\n\x0btransferErt\x18\x0e \x01(\x05\x12\x11\n\tallotBase\x18\x0f \x01(\x05\x12\x10\n\x08\x61llotErt\x18\x10 \x01(\x05\x12\x12\n\nallotPrice\x18\x11 \x01(\x01\x12\x0f\n\x07\x61\x64\x64\x42\x61se\x18\x12 \x01(\x05\x12\x0e\n\x06\x61\x64\x64\x45rt\x18\x13 \x01(\x05\x12\x10\n\x08\x61\x64\x64Price\x18\x14 \x01(\x01\x12\x10\n\x08\x64ividend\x18\x15 \x01(\x01\x12\x12\n\nspDividend\x18\x16 \x01(\x01\x12\x11\n\ttimestamp\x18\x17 \x01(\x01*\xcd\x02\n\tQotMarket\x12\x15\n\x11QotMarket_Unknown\x10\x00\x12\x19\n\x15QotMarket_HK_Security\x10\x01\x12\x17\n\x13QotMarket_HK_Future\x10\x02\x12\x19\n\x15QotMarket_US_Security\x10\x0b\x12\x1b\n\x17QotMarket_CNSH_Security\x10\x15\x12\x1b\n\x17QotMarket_CNSZ_Security\x10\x16\x12\x19\n\x15QotMarket_SG_Security\x10\x1f\x12\x19\n\x15QotMarket_JP_Security\x10)\x12\x19\n\x15QotMarket_AU_Security\x10\x33\x12\x19\n\x15QotMarket_MY_Security\x10=\x12\x19\n\x15QotMarket_CA_Security\x10G\x12\x19\n\x15QotMarket_FX_Security\x10Q*\x9a\x02\n\x0cSecurityType\x12\x18\n\x14SecurityType_Unknown\x10\x00\x12\x15\n\x11SecurityType_Bond\x10\x01\x12\x15\n\x11SecurityType_Bwrt\x10\x02\x12\x15\n\x11SecurityType_Eqty\x10\x03\x12\x16\n\x12SecurityType_Trust\x10\x04\x12\x18\n\x14SecurityType_Warrant\x10\x05\x12\x16\n\x12SecurityType_Index\x10\x06\x12\x16\n\x12SecurityType_Plate\x10\x07\x12\x15\n\x11SecurityType_Drvt\x10\x08\x12\x19\n\x15SecurityType_PlateSet\x10\t\x12\x17\n\x13SecurityType_Future\x10\n*\x8a\x01\n\x0cPlateSetType\x12\x14\n\x10PlateSetType_All\x10\x00\x12\x19\n\x15PlateSetType_Industry\x10\x01\x12\x17\n\x13PlateSetType_Region\x10\x02\x12\x18\n\x14PlateSetType_Concept\x10\x03\x12\x16\n\x12PlateSetType_Other\x10\x04*\x95\x01\n\x0bWarrantType\x12\x17\n\x13WarrantType_Unknown\x10\x00\x12\x13\n\x0fWarrantType_Buy\x10\x01\x12\x14\n\x10WarrantType_Sell\x10\x02\x12\x14\n\x10WarrantType_Bull\x10\x03\x12\x14\n\x10WarrantType_Bear\x10\x04\x12\x16\n\x12WarrantType_InLine\x10\x05*M\n\nOptionType\x12\x16\n\x12OptionType_Unknown\x10\x00\x12\x13\n\x0fOptionType_Call\x10\x01\x12\x12\n\x0eOptionType_Put\x10\x02*e\n\x0fIndexOptionType\x12\x1b\n\x17IndexOptionType_Unknown\x10\x00\x12\x1a\n\x16IndexOptionType_Normal\x10\x01\x12\x19\n\x15IndexOptionType_Small\x10\x02*\x82\x01\n\x0eOptionAreaType\x12\x1a\n\x16OptionAreaType_Unknown\x10\x00\x12\x1b\n\x17OptionAreaType_American\x10\x01\x12\x1b\n\x17OptionAreaType_European\x10\x02\x12\x1a\n\x16OptionAreaType_Bermuda\x10\x03*\xd3\t\n\x0eQotMarketState\x12\x17\n\x13QotMarketState_None\x10\x00\x12\x1a\n\x16QotMarketState_Auction\x10\x01\x12\x1e\n\x1aQotMarketState_WaitingOpen\x10\x02\x12\x1a\n\x16QotMarketState_Morning\x10\x03\x12\x17\n\x13QotMarketState_Rest\x10\x04\x12\x1c\n\x18QotMarketState_Afternoon\x10\x05\x12\x19\n\x15QotMarketState_Closed\x10\x06\x12!\n\x1dQotMarketState_PreMarketBegin\x10\x08\x12\x1f\n\x1bQotMarketState_PreMarketEnd\x10\t\x12\"\n\x1eQotMarketState_AfterHoursBegin\x10\n\x12 \n\x1cQotMarketState_AfterHoursEnd\x10\x0b\x12#\n\x1fQotMarketState_FUTU_SWITCH_DATE\x10\x0c\x12\x1c\n\x18QotMarketState_NightOpen\x10\r\x12\x1b\n\x17QotMarketState_NightEnd\x10\x0e\x12 \n\x1cQotMarketState_FutureDayOpen\x10\x0f\x12!\n\x1dQotMarketState_FutureDayBreak\x10\x10\x12!\n\x1dQotMarketState_FutureDayClose\x10\x11\x12\'\n#QotMarketState_FutureDayWaitForOpen\x10\x12\x12\x18\n\x14QotMarketState_HkCas\x10\x13\x12\"\n\x1eQotMarketState_FutureNightWait\x10\x14\x12\"\n\x1eQotMarketState_FutureAfternoon\x10\x15\x12#\n\x1fQotMarketState_FutureSwitchDate\x10\x16\x12\x1d\n\x19QotMarketState_FutureOpen\x10\x17\x12\x1e\n\x1aQotMarketState_FutureBreak\x10\x18\x12\"\n\x1eQotMarketState_FutureBreakOver\x10\x19\x12\x1e\n\x1aQotMarketState_FutureClose\x10\x1a\x12%\n!QotMarketState_StibAfterHoursWait\x10\x1b\x12&\n\"QotMarketState_StibAfterHoursBegin\x10\x1c\x12$\n QotMarketState_StibAfterHoursEnd\x10\x1d\x12 \n\x1cQotMarketState_CLOSE_AUCTION\x10\x1e\x12 \n\x1cQotMarketState_AFTERNOON_END\x10\x1f\x12\x18\n\x14QotMarketState_NIGHT\x10 \x12\"\n\x1eQotMarketState_OVERNIGHT_BEGIN\x10!\x12 \n\x1cQotMarketState_OVERNIGHT_END\x10\"\x12 \n\x1cQotMarketState_TRADE_AT_LAST\x10#\x12 \n\x1cQotMarketState_TRADE_AUCTION\x10$\x12\x1c\n\x18QotMarketState_OVERNIGHT\x10%*\xe4\x01\n\x0fTradeDateMarket\x12\x1b\n\x17TradeDateMarket_Unknown\x10\x00\x12\x16\n\x12TradeDateMarket_HK\x10\x01\x12\x16\n\x12TradeDateMarket_US\x10\x02\x12\x16\n\x12TradeDateMarket_CN\x10\x03\x12\x16\n\x12TradeDateMarket_NT\x10\x04\x12\x16\n\x12TradeDateMarket_ST\x10\x05\x12\x1d\n\x19TradeDateMarket_JP_Future\x10\x06\x12\x1d\n\x19TradeDateMarket_SG_Future\x10\x07*`\n\rTradeDateType\x12\x17\n\x13TradeDateType_Whole\x10\x00\x12\x19\n\x15TradeDateType_Morning\x10\x01\x12\x1b\n\x17TradeDateType_Afternoon\x10\x02*N\n\tRehabType\x12\x12\n\x0eRehabType_None\x10\x00\x12\x15\n\x11RehabType_Forward\x10\x01\x12\x16\n\x12RehabType_Backward\x10\x02*\xdd\x01\n\x06KLType\x12\x12\n\x0eKLType_Unknown\x10\x00\x12\x0f\n\x0bKLType_1Min\x10\x01\x12\x0e\n\nKLType_Day\x10\x02\x12\x0f\n\x0bKLType_Week\x10\x03\x12\x10\n\x0cKLType_Month\x10\x04\x12\x0f\n\x0bKLType_Year\x10\x05\x12\x0f\n\x0bKLType_5Min\x10\x06\x12\x10\n\x0cKLType_15Min\x10\x07\x12\x10\n\x0cKLType_30Min\x10\x08\x12\x10\n\x0cKLType_60Min\x10\t\x12\x0f\n\x0bKLType_3Min\x10\n\x12\x12\n\x0eKLType_Quarter\x10\x0b*\xf5\x01\n\x08KLFields\x12\x11\n\rKLFields_None\x10\x00\x12\x11\n\rKLFields_High\x10\x01\x12\x11\n\rKLFields_Open\x10\x02\x12\x10\n\x0cKLFields_Low\x10\x04\x12\x12\n\x0eKLFields_Close\x10\x08\x12\x16\n\x12KLFields_LastClose\x10\x10\x12\x13\n\x0fKLFields_Volume\x10 \x12\x15\n\x11KLFields_Turnover\x10@\x12\x1a\n\x15KLFields_TurnoverRate\x10\x80\x01\x12\x10\n\x0bKLFields_PE\x10\x80\x02\x12\x18\n\x13KLFields_ChangeRate\x10\x80\x04*\xea\x02\n\x07SubType\x12\x10\n\x0cSubType_None\x10\x00\x12\x11\n\rSubType_Basic\x10\x01\x12\x15\n\x11SubType_OrderBook\x10\x02\x12\x12\n\x0eSubType_Ticker\x10\x04\x12\x0e\n\nSubType_RT\x10\x05\x12\x12\n\x0eSubType_KL_Day\x10\x06\x12\x13\n\x0fSubType_KL_5Min\x10\x07\x12\x14\n\x10SubType_KL_15Min\x10\x08\x12\x14\n\x10SubType_KL_30Min\x10\t\x12\x14\n\x10SubType_KL_60Min\x10\n\x12\x13\n\x0fSubType_KL_1Min\x10\x0b\x12\x13\n\x0fSubType_KL_Week\x10\x0c\x12\x14\n\x10SubType_KL_Month\x10\r\x12\x12\n\x0eSubType_Broker\x10\x0e\x12\x16\n\x12SubType_KL_Qurater\x10\x0f\x12\x13\n\x0fSubType_KL_Year\x10\x10\x12\x13\n\x0fSubType_KL_3Min\x10\x11*}\n\x0fTickerDirection\x12\x1b\n\x17TickerDirection_Unknown\x10\x00\x12\x17\n\x13TickerDirection_Bid\x10\x01\x12\x17\n\x13TickerDirection_Ask\x10\x02\x12\x1b\n\x17TickerDirection_Neutral\x10\x03*\x9c\x07\n\nTickerType\x12\x16\n\x12TickerType_Unknown\x10\x00\x12\x18\n\x14TickerType_Automatch\x10\x01\x12\x13\n\x0fTickerType_Late\x10\x02\x12\x1c\n\x18TickerType_NoneAutomatch\x10\x03\x12\x1d\n\x19TickerType_InterAutomatch\x10\x04\x12!\n\x1dTickerType_InterNoneAutomatch\x10\x05\x12\x15\n\x11TickerType_OddLot\x10\x06\x12\x16\n\x12TickerType_Auction\x10\x07\x12\x13\n\x0fTickerType_Bulk\x10\x08\x12\x14\n\x10TickerType_Crash\x10\t\x12\x1a\n\x16TickerType_CrossMarket\x10\n\x12\x17\n\x13TickerType_BulkSold\x10\x0b\x12\x1a\n\x16TickerType_FreeOnBoard\x10\x0c\x12\x1b\n\x17TickerType_Rule127Or155\x10\r\x12\x14\n\x10TickerType_Delay\x10\x0e\x12%\n!TickerType_MarketCenterClosePrice\x10\x0f\x12\x16\n\x12TickerType_NextDay\x10\x10\x12\"\n\x1eTickerType_MarketCenterOpening\x10\x11\x12\"\n\x1eTickerType_PriorReferencePrice\x10\x12\x12$\n TickerType_MarketCenterOpenPrice\x10\x13\x12\x15\n\x11TickerType_Seller\x10\x14\x12\x10\n\x0cTickerType_T\x10\x15\x12#\n\x1fTickerType_ExtendedTradingHours\x10\x16\x12\x19\n\x15TickerType_Contingent\x10\x17\x12\x17\n\x13TickerType_AvgPrice\x10\x18\x12\x16\n\x12TickerType_OTCSold\x10\x19\x12 \n\x1cTickerType_OddLotCrossMarket\x10\x1a\x12!\n\x1dTickerType_DerivativelyPriced\x10\x1b\x12\x1e\n\x1aTickerType_ReOpeningPriced\x10\x1c\x12\x1c\n\x18TickerType_ClosingPriced\x10\x1d\x12&\n\"TickerType_ComprehensiveDelayPrice\x10\x1e\x12\x17\n\x13TickerType_Overseas\x10\x1f*M\n\nDarkStatus\x12\x13\n\x0f\x44\x61rkStatus_None\x10\x00\x12\x16\n\x12\x44\x61rkStatus_Trading\x10\x01\x12\x12\n\x0e\x44\x61rkStatus_End\x10\x02*\x94\x06\n\x0eSecurityStatus\x12\x1a\n\x16SecurityStatus_Unknown\x10\x00\x12\x19\n\x15SecurityStatus_Normal\x10\x01\x12\x1a\n\x16SecurityStatus_Listing\x10\x02\x12\x1d\n\x19SecurityStatus_Purchasing\x10\x03\x12\x1e\n\x1aSecurityStatus_Subscribing\x10\x04\x12)\n%SecurityStatus_BeforeDrakTradeOpening\x10\x05\x12\x1e\n\x1aSecurityStatus_DrakTrading\x10\x06\x12\x1f\n\x1bSecurityStatus_DrakTradeEnd\x10\x07\x12\x1b\n\x17SecurityStatus_ToBeOpen\x10\x08\x12\x1c\n\x18SecurityStatus_Suspended\x10\t\x12\x19\n\x15SecurityStatus_Called\x10\n\x12)\n%SecurityStatus_ExpiredLastTradingDate\x10\x0b\x12\x1a\n\x16SecurityStatus_Expired\x10\x0c\x12\x1b\n\x17SecurityStatus_Delisted\x10\r\x12(\n$SecurityStatus_ChangeToTemporaryCode\x10\x0e\x12(\n$SecurityStatus_TemporaryCodeTradeEnd\x10\x0f\x12\'\n#SecurityStatus_ChangedPlateTradeEnd\x10\x10\x12&\n\"SecurityStatus_ChangedCodeTradeEnd\x10\x11\x12,\n(SecurityStatus_RecoverableCircuitBreaker\x10\x12\x12.\n*SecurityStatus_UnRecoverableCircuitBreaker\x10\x13\x12#\n\x1fSecurityStatus_AfterCombination\x10\x14\x12\"\n\x1eSecurityStatus_AfterTransation\x10\x15*\x81\x01\n\x0eHolderCategory\x12\x19\n\x15HolderCategory_Unknow\x10\x00\x12\x19\n\x15HolderCategory_Agency\x10\x01\x12\x17\n\x13HolderCategory_Fund\x10\x02\x12 \n\x1cHolderCategory_SeniorManager\x10\x03*v\n\x0cPushDataType\x12\x17\n\x13PushDataType_Unknow\x10\x00\x12\x19\n\x15PushDataType_Realtime\x10\x01\x12\x1a\n\x16PushDataType_ByDisConn\x10\x02\x12\x16\n\x12PushDataType_Cache\x10\x03*\xd3\n\n\tSortField\x12\x14\n\x10SortField_Unknow\x10\x00\x12\x12\n\x0eSortField_Code\x10\x01\x12\x16\n\x12SortField_CurPrice\x10\x02\x12\x1c\n\x18SortField_PriceChangeVal\x10\x03\x12\x18\n\x14SortField_ChangeRate\x10\x04\x12\x14\n\x10SortField_Status\x10\x05\x12\x16\n\x12SortField_BidPrice\x10\x06\x12\x16\n\x12SortField_AskPrice\x10\x07\x12\x14\n\x10SortField_BidVol\x10\x08\x12\x14\n\x10SortField_AskVol\x10\t\x12\x14\n\x10SortField_Volume\x10\n\x12\x16\n\x12SortField_Turnover\x10\x0b\x12\x17\n\x13SortField_Amplitude\x10\x1e\x12\x13\n\x0fSortField_Score\x10\x0c\x12\x15\n\x11SortField_Premium\x10\r\x12\x1f\n\x1bSortField_EffectiveLeverage\x10\x0e\x12\x13\n\x0fSortField_Delta\x10\x0f\x12\x1f\n\x1bSortField_ImpliedVolatility\x10\x10\x12\x12\n\x0eSortField_Type\x10\x11\x12\x19\n\x15SortField_StrikePrice\x10\x12\x12\x1c\n\x18SortField_BreakEvenPoint\x10\x13\x12\x1a\n\x16SortField_MaturityTime\x10\x14\x12\x16\n\x12SortField_ListTime\x10\x15\x12\x1b\n\x17SortField_LastTradeTime\x10\x16\x12\x16\n\x12SortField_Leverage\x10\x17\x12\x18\n\x14SortField_InOutMoney\x10\x18\x12\x1b\n\x17SortField_RecoveryPrice\x10\x19\x12\x19\n\x15SortField_ChangePrice\x10\x1a\x12\x14\n\x10SortField_Change\x10\x1b\x12\x18\n\x14SortField_StreetRate\x10\x1c\x12\x17\n\x13SortField_StreetVol\x10\x1d\x12\x19\n\x15SortField_WarrantName\x10\x1f\x12\x14\n\x10SortField_Issuer\x10 \x12\x15\n\x11SortField_LotSize\x10!\x12\x17\n\x13SortField_IssueSize\x10\"\x12\x1e\n\x1aSortField_UpperStrikePrice\x10-\x12\x1e\n\x1aSortField_LowerStrikePrice\x10.\x12\x1f\n\x1bSortField_InLinePriceStatus\x10/\x12\x19\n\x15SortField_PreCurPrice\x10#\x12\x1b\n\x17SortField_AfterCurPrice\x10$\x12\x1f\n\x1bSortField_PrePriceChangeVal\x10%\x12!\n\x1dSortField_AfterPriceChangeVal\x10&\x12\x1b\n\x17SortField_PreChangeRate\x10\'\x12\x1d\n\x19SortField_AfterChangeRate\x10(\x12\x1a\n\x16SortField_PreAmplitude\x10)\x12\x1c\n\x18SortField_AfterAmplitude\x10*\x12\x19\n\x15SortField_PreTurnover\x10+\x12\x1b\n\x17SortField_AfterTurnover\x10,\x12\x1d\n\x19SortField_LastSettlePrice\x10\x30\x12\x16\n\x12SortField_Position\x10\x31\x12\x1c\n\x18SortField_PositionChange\x10\x32*\xbf\x03\n\x06Issuer\x12\x11\n\rIssuer_Unknow\x10\x00\x12\r\n\tIssuer_SG\x10\x01\x12\r\n\tIssuer_BP\x10\x02\x12\r\n\tIssuer_CS\x10\x03\x12\r\n\tIssuer_CT\x10\x04\x12\r\n\tIssuer_EA\x10\x05\x12\r\n\tIssuer_GS\x10\x06\x12\r\n\tIssuer_HS\x10\x07\x12\r\n\tIssuer_JP\x10\x08\x12\r\n\tIssuer_MB\x10\t\x12\r\n\tIssuer_SC\x10\n\x12\r\n\tIssuer_UB\x10\x0b\x12\r\n\tIssuer_BI\x10\x0c\x12\r\n\tIssuer_DB\x10\r\x12\r\n\tIssuer_DC\x10\x0e\x12\r\n\tIssuer_ML\x10\x0f\x12\r\n\tIssuer_NM\x10\x10\x12\r\n\tIssuer_RB\x10\x11\x12\r\n\tIssuer_RS\x10\x12\x12\r\n\tIssuer_BC\x10\x13\x12\r\n\tIssuer_HT\x10\x14\x12\r\n\tIssuer_VT\x10\x15\x12\r\n\tIssuer_KC\x10\x16\x12\r\n\tIssuer_MS\x10\x17\x12\r\n\tIssuer_GJ\x10\x18\x12\r\n\tIssuer_XZ\x10\x19\x12\r\n\tIssuer_HU\x10\x1a\x12\r\n\tIssuer_KS\x10\x1b\x12\r\n\tIssuer_CI\x10\x1c*\x97\x01\n\tIpoPeriod\x12\x14\n\x10IpoPeriod_Unknow\x10\x00\x12\x13\n\x0fIpoPeriod_Today\x10\x01\x12\x16\n\x12IpoPeriod_Tomorrow\x10\x02\x12\x16\n\x12IpoPeriod_Nextweek\x10\x03\x12\x16\n\x12IpoPeriod_Lastweek\x10\x04\x12\x17\n\x13IpoPeriod_Lastmonth\x10\x05*N\n\tPriceType\x12\x14\n\x10PriceType_Unknow\x10\x00\x12\x15\n\x11PriceType_Outside\x10\x01\x12\x14\n\x10PriceType_WithIn\x10\x02*\x9d\x01\n\rWarrantStatus\x12\x18\n\x14WarrantStatus_Unknow\x10\x00\x12\x18\n\x14WarrantStatus_Normal\x10\x01\x12\x19\n\x15WarrantStatus_Suspend\x10\x02\x12\x1b\n\x17WarrantStatus_StopTrade\x10\x03\x12 \n\x1cWarrantStatus_PendingListing\x10\x04*\xda\x01\n\nCompanyAct\x12\x13\n\x0f\x43ompanyAct_None\x10\x00\x12\x14\n\x10\x43ompanyAct_Split\x10\x01\x12\x13\n\x0f\x43ompanyAct_Join\x10\x02\x12\x14\n\x10\x43ompanyAct_Bonus\x10\x04\x12\x17\n\x13\x43ompanyAct_Transfer\x10\x08\x12\x14\n\x10\x43ompanyAct_Allot\x10\x10\x12\x12\n\x0e\x43ompanyAct_Add\x10 \x12\x17\n\x13\x43ompanyAct_Dividend\x10@\x12\x1a\n\x15\x43ompanyAct_SPDividend\x10\x80\x01*}\n\x08QotRight\x12\x13\n\x0fQotRight_Unknow\x10\x00\x12\x10\n\x0cQotRight_Bmp\x10\x01\x12\x13\n\x0fQotRight_Level1\x10\x02\x12\x13\n\x0fQotRight_Level2\x10\x03\x12\x0f\n\x0bQotRight_SF\x10\x04\x12\x0f\n\x0bQotRight_No\x10\x05*\xce\x04\n\x11PriceReminderType\x12\x1d\n\x19PriceReminderType_Unknown\x10\x00\x12\x1d\n\x19PriceReminderType_PriceUp\x10\x01\x12\x1f\n\x1bPriceReminderType_PriceDown\x10\x02\x12\"\n\x1ePriceReminderType_ChangeRateUp\x10\x03\x12$\n PriceReminderType_ChangeRateDown\x10\x04\x12&\n\"PriceReminderType_5MinChangeRateUp\x10\x05\x12(\n$PriceReminderType_5MinChangeRateDown\x10\x06\x12\x1e\n\x1aPriceReminderType_VolumeUp\x10\x07\x12 \n\x1cPriceReminderType_TurnoverUp\x10\x08\x12$\n PriceReminderType_TurnoverRateUp\x10\t\x12 \n\x1cPriceReminderType_BidPriceUp\x10\n\x12\"\n\x1ePriceReminderType_AskPriceDown\x10\x0b\x12\x1e\n\x1aPriceReminderType_BidVolUp\x10\x0c\x12\x1e\n\x1aPriceReminderType_AskVolUp\x10\r\x12&\n\"PriceReminderType_3MinChangeRateUp\x10\x0e\x12(\n$PriceReminderType_3MinChangeRateDown\x10\x0f*\x90\x01\n\x11PriceReminderFreq\x12\x1d\n\x19PriceReminderFreq_Unknown\x10\x00\x12\x1c\n\x18PriceReminderFreq_Always\x10\x01\x12\x1e\n\x1aPriceReminderFreq_OnceADay\x10\x02\x12\x1e\n\x1aPriceReminderFreq_OnlyOnce\x10\x03*\xdc\x01\n\x19PriceReminderMarketStatus\x12$\n PriceReminderMarketStatus_Unknow\x10\x00\x12\"\n\x1ePriceReminderMarketStatus_Open\x10\x01\x12#\n\x1fPriceReminderMarketStatus_USPre\x10\x02\x12%\n!PriceReminderMarketStatus_USAfter\x10\x03\x12)\n%PriceReminderMarketStatus_USOverNight\x10\x04*\xb3\x01\n\nAssetClass\x12\x15\n\x11\x41ssetClass_Unknow\x10\x00\x12\x14\n\x10\x41ssetClass_Stock\x10\x01\x12\x13\n\x0f\x41ssetClass_Bond\x10\x02\x12\x18\n\x14\x41ssetClass_Commodity\x10\x03\x12\x1d\n\x19\x41ssetClass_CurrencyMarket\x10\x04\x12\x15\n\x11\x41ssetClass_Future\x10\x05\x12\x13\n\x0f\x41ssetClass_Swap\x10\x06*\xaf\x02\n\x0f\x45xpirationCycle\x12\x1b\n\x17\x45xpirationCycle_Unknown\x10\x00\x12\x18\n\x14\x45xpirationCycle_Week\x10\x01\x12\x19\n\x15\x45xpirationCycle_Month\x10\x02\x12\x1c\n\x18\x45xpirationCycle_MonthEnd\x10\x03\x12\x1b\n\x17\x45xpirationCycle_Quarter\x10\x04\x12\x1b\n\x17\x45xpirationCycle_WeekMon\x10\x0b\x12\x1b\n\x17\x45xpirationCycle_WeekTue\x10\x0c\x12\x1b\n\x17\x45xpirationCycle_WeekWed\x10\r\x12\x1b\n\x17\x45xpirationCycle_WeekThu\x10\x0e\x12\x1b\n\x17\x45xpirationCycle_WeekFri\x10\x0f*y\n\x12OptionStandardType\x12\x1e\n\x1aOptionStandardType_Unknown\x10\x00\x12\x1f\n\x1bOptionStandardType_Standard\x10\x01\x12\"\n\x1eOptionStandardType_NonStandard\x10\x02*r\n\x14OptionSettlementMode\x12 \n\x1cOptionSettlementMode_Unknown\x10\x00\x12\x1b\n\x17OptionSettlementMode_AM\x10\x01\x12\x1b\n\x17OptionSettlementMode_PM\x10\x02*\xb4\x03\n\x08\x45xchType\x12\x14\n\x10\x45xchType_Unknown\x10\x00\x12\x19\n\x15\x45xchType_HK_MainBoard\x10\x01\x12\x18\n\x14\x45xchType_HK_GEMBoard\x10\x02\x12\x14\n\x10\x45xchType_HK_HKEX\x10\x03\x12\x14\n\x10\x45xchType_US_NYSE\x10\x04\x12\x16\n\x12\x45xchType_US_Nasdaq\x10\x05\x12\x14\n\x10\x45xchType_US_Pink\x10\x06\x12\x14\n\x10\x45xchType_US_AMEX\x10\x07\x12\x16\n\x12\x45xchType_US_Option\x10\x08\x12\x15\n\x11\x45xchType_US_NYMEX\x10\t\x12\x15\n\x11\x45xchType_US_COMEX\x10\n\x12\x14\n\x10\x45xchType_US_CBOT\x10\x0b\x12\x13\n\x0f\x45xchType_US_CME\x10\x0c\x12\x14\n\x10\x45xchType_US_CBOE\x10\r\x12\x12\n\x0e\x45xchType_CN_SH\x10\x0e\x12\x12\n\x0e\x45xchType_CN_SZ\x10\x0f\x12\x14\n\x10\x45xchType_CN_STIB\x10\x10\x12\x13\n\x0f\x45xchType_SG_SGX\x10\x11\x12\x13\n\x0f\x45xchType_JP_OSE\x10\x12*|\n\nPeriodType\x12\x16\n\x12PeriodType_Unknown\x10\x00\x12\x17\n\x13PeriodType_INTRADAY\x10\x01\x12\x12\n\x0ePeriodType_DAY\x10\x02\x12\x13\n\x0fPeriodType_WEEK\x10\x03\x12\x14\n\x10PeriodType_MONTH\x10\x04\x42@\n\x13\x63om.futu.openapi.pbZ)github.com/futuopen/ftapi4go/pb/qotcommon')
  ,
  dependencies=[Common__pb2.DESCRIPTOR,])

_QOTMARKET = _descriptor.EnumDescriptor(
  name='QotMarket',
  full_name='Qot_Common.QotMarket',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='QotMarket_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_HK_Security', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_HK_Future', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_US_Security', index=3, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_CNSH_Security', index=4, number=21,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_CNSZ_Security', index=5, number=22,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_SG_Security', index=6, number=31,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_JP_Security', index=7, number=41,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_AU_Security', index=8, number=51,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_MY_Security', index=9, number=61,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_CA_Security', index=10, number=71,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarket_FX_Security', index=11, number=81,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4221,
  serialized_end=4554,
)
_sym_db.RegisterEnumDescriptor(_QOTMARKET)

QotMarket = enum_type_wrapper.EnumTypeWrapper(_QOTMARKET)
_SECURITYTYPE = _descriptor.EnumDescriptor(
  name='SecurityType',
  full_name='Qot_Common.SecurityType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SecurityType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityType_Bond', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityType_Bwrt', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityType_Eqty', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityType_Trust', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityType_Warrant', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityType_Index', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityType_Plate', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityType_Drvt', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityType_PlateSet', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityType_Future', index=10, number=10,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4557,
  serialized_end=4839,
)
_sym_db.RegisterEnumDescriptor(_SECURITYTYPE)

SecurityType = enum_type_wrapper.EnumTypeWrapper(_SECURITYTYPE)
_PLATESETTYPE = _descriptor.EnumDescriptor(
  name='PlateSetType',
  full_name='Qot_Common.PlateSetType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PlateSetType_All', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PlateSetType_Industry', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PlateSetType_Region', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PlateSetType_Concept', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PlateSetType_Other', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4842,
  serialized_end=4980,
)
_sym_db.RegisterEnumDescriptor(_PLATESETTYPE)

PlateSetType = enum_type_wrapper.EnumTypeWrapper(_PLATESETTYPE)
_WARRANTTYPE = _descriptor.EnumDescriptor(
  name='WarrantType',
  full_name='Qot_Common.WarrantType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='WarrantType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WarrantType_Buy', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WarrantType_Sell', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WarrantType_Bull', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WarrantType_Bear', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WarrantType_InLine', index=5, number=5,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4983,
  serialized_end=5132,
)
_sym_db.RegisterEnumDescriptor(_WARRANTTYPE)

WarrantType = enum_type_wrapper.EnumTypeWrapper(_WARRANTTYPE)
_OPTIONTYPE = _descriptor.EnumDescriptor(
  name='OptionType',
  full_name='Qot_Common.OptionType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OptionType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OptionType_Call', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OptionType_Put', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5134,
  serialized_end=5211,
)
_sym_db.RegisterEnumDescriptor(_OPTIONTYPE)

OptionType = enum_type_wrapper.EnumTypeWrapper(_OPTIONTYPE)
_INDEXOPTIONTYPE = _descriptor.EnumDescriptor(
  name='IndexOptionType',
  full_name='Qot_Common.IndexOptionType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='IndexOptionType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IndexOptionType_Normal', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IndexOptionType_Small', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5213,
  serialized_end=5314,
)
_sym_db.RegisterEnumDescriptor(_INDEXOPTIONTYPE)

IndexOptionType = enum_type_wrapper.EnumTypeWrapper(_INDEXOPTIONTYPE)
_OPTIONAREATYPE = _descriptor.EnumDescriptor(
  name='OptionAreaType',
  full_name='Qot_Common.OptionAreaType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OptionAreaType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OptionAreaType_American', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OptionAreaType_European', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OptionAreaType_Bermuda', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5317,
  serialized_end=5447,
)
_sym_db.RegisterEnumDescriptor(_OPTIONAREATYPE)

OptionAreaType = enum_type_wrapper.EnumTypeWrapper(_OPTIONAREATYPE)
_QOTMARKETSTATE = _descriptor.EnumDescriptor(
  name='QotMarketState',
  full_name='Qot_Common.QotMarketState',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_None', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_Auction', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_WaitingOpen', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_Morning', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_Rest', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_Afternoon', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_Closed', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_PreMarketBegin', index=7, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_PreMarketEnd', index=8, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_AfterHoursBegin', index=9, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_AfterHoursEnd', index=10, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FUTU_SWITCH_DATE', index=11, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_NightOpen', index=12, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_NightEnd', index=13, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureDayOpen', index=14, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureDayBreak', index=15, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureDayClose', index=16, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureDayWaitForOpen', index=17, number=18,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_HkCas', index=18, number=19,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureNightWait', index=19, number=20,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureAfternoon', index=20, number=21,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureSwitchDate', index=21, number=22,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureOpen', index=22, number=23,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureBreak', index=23, number=24,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureBreakOver', index=24, number=25,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_FutureClose', index=25, number=26,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_StibAfterHoursWait', index=26, number=27,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_StibAfterHoursBegin', index=27, number=28,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_StibAfterHoursEnd', index=28, number=29,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_CLOSE_AUCTION', index=29, number=30,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_AFTERNOON_END', index=30, number=31,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_NIGHT', index=31, number=32,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_OVERNIGHT_BEGIN', index=32, number=33,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_OVERNIGHT_END', index=33, number=34,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_TRADE_AT_LAST', index=34, number=35,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_TRADE_AUCTION', index=35, number=36,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotMarketState_OVERNIGHT', index=36, number=37,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5450,
  serialized_end=6685,
)
_sym_db.RegisterEnumDescriptor(_QOTMARKETSTATE)

QotMarketState = enum_type_wrapper.EnumTypeWrapper(_QOTMARKETSTATE)
_TRADEDATEMARKET = _descriptor.EnumDescriptor(
  name='TradeDateMarket',
  full_name='Qot_Common.TradeDateMarket',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TradeDateMarket_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TradeDateMarket_HK', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TradeDateMarket_US', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TradeDateMarket_CN', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TradeDateMarket_NT', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TradeDateMarket_ST', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TradeDateMarket_JP_Future', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TradeDateMarket_SG_Future', index=7, number=7,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=6688,
  serialized_end=6916,
)
_sym_db.RegisterEnumDescriptor(_TRADEDATEMARKET)

TradeDateMarket = enum_type_wrapper.EnumTypeWrapper(_TRADEDATEMARKET)
_TRADEDATETYPE = _descriptor.EnumDescriptor(
  name='TradeDateType',
  full_name='Qot_Common.TradeDateType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TradeDateType_Whole', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TradeDateType_Morning', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TradeDateType_Afternoon', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=6918,
  serialized_end=7014,
)
_sym_db.RegisterEnumDescriptor(_TRADEDATETYPE)

TradeDateType = enum_type_wrapper.EnumTypeWrapper(_TRADEDATETYPE)
_REHABTYPE = _descriptor.EnumDescriptor(
  name='RehabType',
  full_name='Qot_Common.RehabType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='RehabType_None', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RehabType_Forward', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RehabType_Backward', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=7016,
  serialized_end=7094,
)
_sym_db.RegisterEnumDescriptor(_REHABTYPE)

RehabType = enum_type_wrapper.EnumTypeWrapper(_REHABTYPE)
_KLTYPE = _descriptor.EnumDescriptor(
  name='KLType',
  full_name='Qot_Common.KLType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='KLType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_1Min', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_Day', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_Week', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_Month', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_Year', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_5Min', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_15Min', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_30Min', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_60Min', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_3Min', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLType_Quarter', index=11, number=11,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=7097,
  serialized_end=7318,
)
_sym_db.RegisterEnumDescriptor(_KLTYPE)

KLType = enum_type_wrapper.EnumTypeWrapper(_KLTYPE)
_KLFIELDS = _descriptor.EnumDescriptor(
  name='KLFields',
  full_name='Qot_Common.KLFields',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='KLFields_None', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLFields_High', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLFields_Open', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLFields_Low', index=3, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLFields_Close', index=4, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLFields_LastClose', index=5, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLFields_Volume', index=6, number=32,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLFields_Turnover', index=7, number=64,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLFields_TurnoverRate', index=8, number=128,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLFields_PE', index=9, number=256,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KLFields_ChangeRate', index=10, number=512,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=7321,
  serialized_end=7566,
)
_sym_db.RegisterEnumDescriptor(_KLFIELDS)

KLFields = enum_type_wrapper.EnumTypeWrapper(_KLFIELDS)
_SUBTYPE = _descriptor.EnumDescriptor(
  name='SubType',
  full_name='Qot_Common.SubType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SubType_None', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_Basic', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_OrderBook', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_Ticker', index=3, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_RT', index=4, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_Day', index=5, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_5Min', index=6, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_15Min', index=7, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_30Min', index=8, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_60Min', index=9, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_1Min', index=10, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_Week', index=11, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_Month', index=12, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_Broker', index=13, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_Qurater', index=14, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_Year', index=15, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SubType_KL_3Min', index=16, number=17,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=7569,
  serialized_end=7931,
)
_sym_db.RegisterEnumDescriptor(_SUBTYPE)

SubType = enum_type_wrapper.EnumTypeWrapper(_SUBTYPE)
_TICKERDIRECTION = _descriptor.EnumDescriptor(
  name='TickerDirection',
  full_name='Qot_Common.TickerDirection',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TickerDirection_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerDirection_Bid', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerDirection_Ask', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerDirection_Neutral', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=7933,
  serialized_end=8058,
)
_sym_db.RegisterEnumDescriptor(_TICKERDIRECTION)

TickerDirection = enum_type_wrapper.EnumTypeWrapper(_TICKERDIRECTION)
_TICKERTYPE = _descriptor.EnumDescriptor(
  name='TickerType',
  full_name='Qot_Common.TickerType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TickerType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_Automatch', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_Late', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_NoneAutomatch', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_InterAutomatch', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_InterNoneAutomatch', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_OddLot', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_Auction', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_Bulk', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_Crash', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_CrossMarket', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_BulkSold', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_FreeOnBoard', index=12, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_Rule127Or155', index=13, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_Delay', index=14, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_MarketCenterClosePrice', index=15, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_NextDay', index=16, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_MarketCenterOpening', index=17, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_PriorReferencePrice', index=18, number=18,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_MarketCenterOpenPrice', index=19, number=19,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_Seller', index=20, number=20,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_T', index=21, number=21,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_ExtendedTradingHours', index=22, number=22,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_Contingent', index=23, number=23,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_AvgPrice', index=24, number=24,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_OTCSold', index=25, number=25,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_OddLotCrossMarket', index=26, number=26,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_DerivativelyPriced', index=27, number=27,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_ReOpeningPriced', index=28, number=28,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_ClosingPriced', index=29, number=29,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_ComprehensiveDelayPrice', index=30, number=30,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TickerType_Overseas', index=31, number=31,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=8061,
  serialized_end=8985,
)
_sym_db.RegisterEnumDescriptor(_TICKERTYPE)

TickerType = enum_type_wrapper.EnumTypeWrapper(_TICKERTYPE)
_DARKSTATUS = _descriptor.EnumDescriptor(
  name='DarkStatus',
  full_name='Qot_Common.DarkStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DarkStatus_None', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DarkStatus_Trading', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DarkStatus_End', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=8987,
  serialized_end=9064,
)
_sym_db.RegisterEnumDescriptor(_DARKSTATUS)

DarkStatus = enum_type_wrapper.EnumTypeWrapper(_DARKSTATUS)
_SECURITYSTATUS = _descriptor.EnumDescriptor(
  name='SecurityStatus',
  full_name='Qot_Common.SecurityStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_Normal', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_Listing', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_Purchasing', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_Subscribing', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_BeforeDrakTradeOpening', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_DrakTrading', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_DrakTradeEnd', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_ToBeOpen', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_Suspended', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_Called', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_ExpiredLastTradingDate', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_Expired', index=12, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_Delisted', index=13, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_ChangeToTemporaryCode', index=14, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_TemporaryCodeTradeEnd', index=15, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_ChangedPlateTradeEnd', index=16, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_ChangedCodeTradeEnd', index=17, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_RecoverableCircuitBreaker', index=18, number=18,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_UnRecoverableCircuitBreaker', index=19, number=19,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_AfterCombination', index=20, number=20,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SecurityStatus_AfterTransation', index=21, number=21,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=9067,
  serialized_end=9855,
)
_sym_db.RegisterEnumDescriptor(_SECURITYSTATUS)

SecurityStatus = enum_type_wrapper.EnumTypeWrapper(_SECURITYSTATUS)
_HOLDERCATEGORY = _descriptor.EnumDescriptor(
  name='HolderCategory',
  full_name='Qot_Common.HolderCategory',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='HolderCategory_Unknow', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HolderCategory_Agency', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HolderCategory_Fund', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HolderCategory_SeniorManager', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=9858,
  serialized_end=9987,
)
_sym_db.RegisterEnumDescriptor(_HOLDERCATEGORY)

HolderCategory = enum_type_wrapper.EnumTypeWrapper(_HOLDERCATEGORY)
_PUSHDATATYPE = _descriptor.EnumDescriptor(
  name='PushDataType',
  full_name='Qot_Common.PushDataType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PushDataType_Unknow', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PushDataType_Realtime', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PushDataType_ByDisConn', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PushDataType_Cache', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=9989,
  serialized_end=10107,
)
_sym_db.RegisterEnumDescriptor(_PUSHDATATYPE)

PushDataType = enum_type_wrapper.EnumTypeWrapper(_PUSHDATATYPE)
_SORTFIELD = _descriptor.EnumDescriptor(
  name='SortField',
  full_name='Qot_Common.SortField',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SortField_Unknow', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Code', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_CurPrice', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_PriceChangeVal', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_ChangeRate', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Status', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_BidPrice', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_AskPrice', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_BidVol', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_AskVol', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Volume', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Turnover', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Amplitude', index=12, number=30,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Score', index=13, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Premium', index=14, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_EffectiveLeverage', index=15, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Delta', index=16, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_ImpliedVolatility', index=17, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Type', index=18, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_StrikePrice', index=19, number=18,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_BreakEvenPoint', index=20, number=19,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_MaturityTime', index=21, number=20,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_ListTime', index=22, number=21,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_LastTradeTime', index=23, number=22,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Leverage', index=24, number=23,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_InOutMoney', index=25, number=24,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_RecoveryPrice', index=26, number=25,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_ChangePrice', index=27, number=26,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Change', index=28, number=27,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_StreetRate', index=29, number=28,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_StreetVol', index=30, number=29,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_WarrantName', index=31, number=31,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Issuer', index=32, number=32,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_LotSize', index=33, number=33,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_IssueSize', index=34, number=34,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_UpperStrikePrice', index=35, number=45,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_LowerStrikePrice', index=36, number=46,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_InLinePriceStatus', index=37, number=47,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_PreCurPrice', index=38, number=35,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_AfterCurPrice', index=39, number=36,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_PrePriceChangeVal', index=40, number=37,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_AfterPriceChangeVal', index=41, number=38,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_PreChangeRate', index=42, number=39,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_AfterChangeRate', index=43, number=40,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_PreAmplitude', index=44, number=41,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_AfterAmplitude', index=45, number=42,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_PreTurnover', index=46, number=43,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_AfterTurnover', index=47, number=44,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_LastSettlePrice', index=48, number=48,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_Position', index=49, number=49,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SortField_PositionChange', index=50, number=50,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=10110,
  serialized_end=11473,
)
_sym_db.RegisterEnumDescriptor(_SORTFIELD)

SortField = enum_type_wrapper.EnumTypeWrapper(_SORTFIELD)
_ISSUER = _descriptor.EnumDescriptor(
  name='Issuer',
  full_name='Qot_Common.Issuer',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='Issuer_Unknow', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_SG', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_BP', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_CS', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_CT', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_EA', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_GS', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_HS', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_JP', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_MB', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_SC', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_UB', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_BI', index=12, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_DB', index=13, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_DC', index=14, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_ML', index=15, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_NM', index=16, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_RB', index=17, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_RS', index=18, number=18,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_BC', index=19, number=19,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_HT', index=20, number=20,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_VT', index=21, number=21,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_KC', index=22, number=22,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_MS', index=23, number=23,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_GJ', index=24, number=24,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_XZ', index=25, number=25,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_HU', index=26, number=26,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_KS', index=27, number=27,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Issuer_CI', index=28, number=28,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=11476,
  serialized_end=11923,
)
_sym_db.RegisterEnumDescriptor(_ISSUER)

Issuer = enum_type_wrapper.EnumTypeWrapper(_ISSUER)
_IPOPERIOD = _descriptor.EnumDescriptor(
  name='IpoPeriod',
  full_name='Qot_Common.IpoPeriod',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='IpoPeriod_Unknow', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IpoPeriod_Today', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IpoPeriod_Tomorrow', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IpoPeriod_Nextweek', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IpoPeriod_Lastweek', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IpoPeriod_Lastmonth', index=5, number=5,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=11926,
  serialized_end=12077,
)
_sym_db.RegisterEnumDescriptor(_IPOPERIOD)

IpoPeriod = enum_type_wrapper.EnumTypeWrapper(_IPOPERIOD)
_PRICETYPE = _descriptor.EnumDescriptor(
  name='PriceType',
  full_name='Qot_Common.PriceType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PriceType_Unknow', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceType_Outside', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceType_WithIn', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=12079,
  serialized_end=12157,
)
_sym_db.RegisterEnumDescriptor(_PRICETYPE)

PriceType = enum_type_wrapper.EnumTypeWrapper(_PRICETYPE)
_WARRANTSTATUS = _descriptor.EnumDescriptor(
  name='WarrantStatus',
  full_name='Qot_Common.WarrantStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='WarrantStatus_Unknow', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WarrantStatus_Normal', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WarrantStatus_Suspend', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WarrantStatus_StopTrade', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WarrantStatus_PendingListing', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=12160,
  serialized_end=12317,
)
_sym_db.RegisterEnumDescriptor(_WARRANTSTATUS)

WarrantStatus = enum_type_wrapper.EnumTypeWrapper(_WARRANTSTATUS)
_COMPANYACT = _descriptor.EnumDescriptor(
  name='CompanyAct',
  full_name='Qot_Common.CompanyAct',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CompanyAct_None', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CompanyAct_Split', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CompanyAct_Join', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CompanyAct_Bonus', index=3, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CompanyAct_Transfer', index=4, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CompanyAct_Allot', index=5, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CompanyAct_Add', index=6, number=32,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CompanyAct_Dividend', index=7, number=64,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CompanyAct_SPDividend', index=8, number=128,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=12320,
  serialized_end=12538,
)
_sym_db.RegisterEnumDescriptor(_COMPANYACT)

CompanyAct = enum_type_wrapper.EnumTypeWrapper(_COMPANYACT)
_QOTRIGHT = _descriptor.EnumDescriptor(
  name='QotRight',
  full_name='Qot_Common.QotRight',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='QotRight_Unknow', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotRight_Bmp', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotRight_Level1', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotRight_Level2', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotRight_SF', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QotRight_No', index=5, number=5,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=12540,
  serialized_end=12665,
)
_sym_db.RegisterEnumDescriptor(_QOTRIGHT)

QotRight = enum_type_wrapper.EnumTypeWrapper(_QOTRIGHT)
_PRICEREMINDERTYPE = _descriptor.EnumDescriptor(
  name='PriceReminderType',
  full_name='Qot_Common.PriceReminderType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_PriceUp', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_PriceDown', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_ChangeRateUp', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_ChangeRateDown', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_5MinChangeRateUp', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_5MinChangeRateDown', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_VolumeUp', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_TurnoverUp', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_TurnoverRateUp', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_BidPriceUp', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_AskPriceDown', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_BidVolUp', index=12, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_AskVolUp', index=13, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_3MinChangeRateUp', index=14, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderType_3MinChangeRateDown', index=15, number=15,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=12668,
  serialized_end=13258,
)
_sym_db.RegisterEnumDescriptor(_PRICEREMINDERTYPE)

PriceReminderType = enum_type_wrapper.EnumTypeWrapper(_PRICEREMINDERTYPE)
_PRICEREMINDERFREQ = _descriptor.EnumDescriptor(
  name='PriceReminderFreq',
  full_name='Qot_Common.PriceReminderFreq',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PriceReminderFreq_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderFreq_Always', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderFreq_OnceADay', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderFreq_OnlyOnce', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=13261,
  serialized_end=13405,
)
_sym_db.RegisterEnumDescriptor(_PRICEREMINDERFREQ)

PriceReminderFreq = enum_type_wrapper.EnumTypeWrapper(_PRICEREMINDERFREQ)
_PRICEREMINDERMARKETSTATUS = _descriptor.EnumDescriptor(
  name='PriceReminderMarketStatus',
  full_name='Qot_Common.PriceReminderMarketStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PriceReminderMarketStatus_Unknow', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderMarketStatus_Open', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderMarketStatus_USPre', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderMarketStatus_USAfter', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PriceReminderMarketStatus_USOverNight', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=13408,
  serialized_end=13628,
)
_sym_db.RegisterEnumDescriptor(_PRICEREMINDERMARKETSTATUS)

PriceReminderMarketStatus = enum_type_wrapper.EnumTypeWrapper(_PRICEREMINDERMARKETSTATUS)
_ASSETCLASS = _descriptor.EnumDescriptor(
  name='AssetClass',
  full_name='Qot_Common.AssetClass',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='AssetClass_Unknow', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AssetClass_Stock', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AssetClass_Bond', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AssetClass_Commodity', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AssetClass_CurrencyMarket', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AssetClass_Future', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AssetClass_Swap', index=6, number=6,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=13631,
  serialized_end=13810,
)
_sym_db.RegisterEnumDescriptor(_ASSETCLASS)

AssetClass = enum_type_wrapper.EnumTypeWrapper(_ASSETCLASS)
_EXPIRATIONCYCLE = _descriptor.EnumDescriptor(
  name='ExpirationCycle',
  full_name='Qot_Common.ExpirationCycle',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ExpirationCycle_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExpirationCycle_Week', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExpirationCycle_Month', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExpirationCycle_MonthEnd', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExpirationCycle_Quarter', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExpirationCycle_WeekMon', index=5, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExpirationCycle_WeekTue', index=6, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExpirationCycle_WeekWed', index=7, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExpirationCycle_WeekThu', index=8, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExpirationCycle_WeekFri', index=9, number=15,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=13813,
  serialized_end=14116,
)
_sym_db.RegisterEnumDescriptor(_EXPIRATIONCYCLE)

ExpirationCycle = enum_type_wrapper.EnumTypeWrapper(_EXPIRATIONCYCLE)
_OPTIONSTANDARDTYPE = _descriptor.EnumDescriptor(
  name='OptionStandardType',
  full_name='Qot_Common.OptionStandardType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OptionStandardType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OptionStandardType_Standard', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OptionStandardType_NonStandard', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=14118,
  serialized_end=14239,
)
_sym_db.RegisterEnumDescriptor(_OPTIONSTANDARDTYPE)

OptionStandardType = enum_type_wrapper.EnumTypeWrapper(_OPTIONSTANDARDTYPE)
_OPTIONSETTLEMENTMODE = _descriptor.EnumDescriptor(
  name='OptionSettlementMode',
  full_name='Qot_Common.OptionSettlementMode',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OptionSettlementMode_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OptionSettlementMode_AM', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OptionSettlementMode_PM', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=14241,
  serialized_end=14355,
)
_sym_db.RegisterEnumDescriptor(_OPTIONSETTLEMENTMODE)

OptionSettlementMode = enum_type_wrapper.EnumTypeWrapper(_OPTIONSETTLEMENTMODE)
_EXCHTYPE = _descriptor.EnumDescriptor(
  name='ExchType',
  full_name='Qot_Common.ExchType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ExchType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_HK_MainBoard', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_HK_GEMBoard', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_HK_HKEX', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_US_NYSE', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_US_Nasdaq', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_US_Pink', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_US_AMEX', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_US_Option', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_US_NYMEX', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_US_COMEX', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_US_CBOT', index=11, number=11,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_US_CME', index=12, number=12,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_US_CBOE', index=13, number=13,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_CN_SH', index=14, number=14,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_CN_SZ', index=15, number=15,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_CN_STIB', index=16, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_SG_SGX', index=17, number=17,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ExchType_JP_OSE', index=18, number=18,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=14358,
  serialized_end=14794,
)
_sym_db.RegisterEnumDescriptor(_EXCHTYPE)

ExchType = enum_type_wrapper.EnumTypeWrapper(_EXCHTYPE)
_PERIODTYPE = _descriptor.EnumDescriptor(
  name='PeriodType',
  full_name='Qot_Common.PeriodType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PeriodType_Unknown', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PeriodType_INTRADAY', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PeriodType_DAY', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PeriodType_WEEK', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PeriodType_MONTH', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=14796,
  serialized_end=14920,
)
_sym_db.RegisterEnumDescriptor(_PERIODTYPE)

PeriodType = enum_type_wrapper.EnumTypeWrapper(_PERIODTYPE)
QotMarket_Unknown = 0
QotMarket_HK_Security = 1
QotMarket_HK_Future = 2
QotMarket_US_Security = 11
QotMarket_CNSH_Security = 21
QotMarket_CNSZ_Security = 22
QotMarket_SG_Security = 31
QotMarket_JP_Security = 41
QotMarket_AU_Security = 51
QotMarket_MY_Security = 61
QotMarket_CA_Security = 71
QotMarket_FX_Security = 81
SecurityType_Unknown = 0
SecurityType_Bond = 1
SecurityType_Bwrt = 2
SecurityType_Eqty = 3
SecurityType_Trust = 4
SecurityType_Warrant = 5
SecurityType_Index = 6
SecurityType_Plate = 7
SecurityType_Drvt = 8
SecurityType_PlateSet = 9
SecurityType_Future = 10
PlateSetType_All = 0
PlateSetType_Industry = 1
PlateSetType_Region = 2
PlateSetType_Concept = 3
PlateSetType_Other = 4
WarrantType_Unknown = 0
WarrantType_Buy = 1
WarrantType_Sell = 2
WarrantType_Bull = 3
WarrantType_Bear = 4
WarrantType_InLine = 5
OptionType_Unknown = 0
OptionType_Call = 1
OptionType_Put = 2
IndexOptionType_Unknown = 0
IndexOptionType_Normal = 1
IndexOptionType_Small = 2
OptionAreaType_Unknown = 0
OptionAreaType_American = 1
OptionAreaType_European = 2
OptionAreaType_Bermuda = 3
QotMarketState_None = 0
QotMarketState_Auction = 1
QotMarketState_WaitingOpen = 2
QotMarketState_Morning = 3
QotMarketState_Rest = 4
QotMarketState_Afternoon = 5
QotMarketState_Closed = 6
QotMarketState_PreMarketBegin = 8
QotMarketState_PreMarketEnd = 9
QotMarketState_AfterHoursBegin = 10
QotMarketState_AfterHoursEnd = 11
QotMarketState_FUTU_SWITCH_DATE = 12
QotMarketState_NightOpen = 13
QotMarketState_NightEnd = 14
QotMarketState_FutureDayOpen = 15
QotMarketState_FutureDayBreak = 16
QotMarketState_FutureDayClose = 17
QotMarketState_FutureDayWaitForOpen = 18
QotMarketState_HkCas = 19
QotMarketState_FutureNightWait = 20
QotMarketState_FutureAfternoon = 21
QotMarketState_FutureSwitchDate = 22
QotMarketState_FutureOpen = 23
QotMarketState_FutureBreak = 24
QotMarketState_FutureBreakOver = 25
QotMarketState_FutureClose = 26
QotMarketState_StibAfterHoursWait = 27
QotMarketState_StibAfterHoursBegin = 28
QotMarketState_StibAfterHoursEnd = 29
QotMarketState_CLOSE_AUCTION = 30
QotMarketState_AFTERNOON_END = 31
QotMarketState_NIGHT = 32
QotMarketState_OVERNIGHT_BEGIN = 33
QotMarketState_OVERNIGHT_END = 34
QotMarketState_TRADE_AT_LAST = 35
QotMarketState_TRADE_AUCTION = 36
QotMarketState_OVERNIGHT = 37
TradeDateMarket_Unknown = 0
TradeDateMarket_HK = 1
TradeDateMarket_US = 2
TradeDateMarket_CN = 3
TradeDateMarket_NT = 4
TradeDateMarket_ST = 5
TradeDateMarket_JP_Future = 6
TradeDateMarket_SG_Future = 7
TradeDateType_Whole = 0
TradeDateType_Morning = 1
TradeDateType_Afternoon = 2
RehabType_None = 0
RehabType_Forward = 1
RehabType_Backward = 2
KLType_Unknown = 0
KLType_1Min = 1
KLType_Day = 2
KLType_Week = 3
KLType_Month = 4
KLType_Year = 5
KLType_5Min = 6
KLType_15Min = 7
KLType_30Min = 8
KLType_60Min = 9
KLType_3Min = 10
KLType_Quarter = 11
KLFields_None = 0
KLFields_High = 1
KLFields_Open = 2
KLFields_Low = 4
KLFields_Close = 8
KLFields_LastClose = 16
KLFields_Volume = 32
KLFields_Turnover = 64
KLFields_TurnoverRate = 128
KLFields_PE = 256
KLFields_ChangeRate = 512
SubType_None = 0
SubType_Basic = 1
SubType_OrderBook = 2
SubType_Ticker = 4
SubType_RT = 5
SubType_KL_Day = 6
SubType_KL_5Min = 7
SubType_KL_15Min = 8
SubType_KL_30Min = 9
SubType_KL_60Min = 10
SubType_KL_1Min = 11
SubType_KL_Week = 12
SubType_KL_Month = 13
SubType_Broker = 14
SubType_KL_Qurater = 15
SubType_KL_Year = 16
SubType_KL_3Min = 17
TickerDirection_Unknown = 0
TickerDirection_Bid = 1
TickerDirection_Ask = 2
TickerDirection_Neutral = 3
TickerType_Unknown = 0
TickerType_Automatch = 1
TickerType_Late = 2
TickerType_NoneAutomatch = 3
TickerType_InterAutomatch = 4
TickerType_InterNoneAutomatch = 5
TickerType_OddLot = 6
TickerType_Auction = 7
TickerType_Bulk = 8
TickerType_Crash = 9
TickerType_CrossMarket = 10
TickerType_BulkSold = 11
TickerType_FreeOnBoard = 12
TickerType_Rule127Or155 = 13
TickerType_Delay = 14
TickerType_MarketCenterClosePrice = 15
TickerType_NextDay = 16
TickerType_MarketCenterOpening = 17
TickerType_PriorReferencePrice = 18
TickerType_MarketCenterOpenPrice = 19
TickerType_Seller = 20
TickerType_T = 21
TickerType_ExtendedTradingHours = 22
TickerType_Contingent = 23
TickerType_AvgPrice = 24
TickerType_OTCSold = 25
TickerType_OddLotCrossMarket = 26
TickerType_DerivativelyPriced = 27
TickerType_ReOpeningPriced = 28
TickerType_ClosingPriced = 29
TickerType_ComprehensiveDelayPrice = 30
TickerType_Overseas = 31
DarkStatus_None = 0
DarkStatus_Trading = 1
DarkStatus_End = 2
SecurityStatus_Unknown = 0
SecurityStatus_Normal = 1
SecurityStatus_Listing = 2
SecurityStatus_Purchasing = 3
SecurityStatus_Subscribing = 4
SecurityStatus_BeforeDrakTradeOpening = 5
SecurityStatus_DrakTrading = 6
SecurityStatus_DrakTradeEnd = 7
SecurityStatus_ToBeOpen = 8
SecurityStatus_Suspended = 9
SecurityStatus_Called = 10
SecurityStatus_ExpiredLastTradingDate = 11
SecurityStatus_Expired = 12
SecurityStatus_Delisted = 13
SecurityStatus_ChangeToTemporaryCode = 14
SecurityStatus_TemporaryCodeTradeEnd = 15
SecurityStatus_ChangedPlateTradeEnd = 16
SecurityStatus_ChangedCodeTradeEnd = 17
SecurityStatus_RecoverableCircuitBreaker = 18
SecurityStatus_UnRecoverableCircuitBreaker = 19
SecurityStatus_AfterCombination = 20
SecurityStatus_AfterTransation = 21
HolderCategory_Unknow = 0
HolderCategory_Agency = 1
HolderCategory_Fund = 2
HolderCategory_SeniorManager = 3
PushDataType_Unknow = 0
PushDataType_Realtime = 1
PushDataType_ByDisConn = 2
PushDataType_Cache = 3
SortField_Unknow = 0
SortField_Code = 1
SortField_CurPrice = 2
SortField_PriceChangeVal = 3
SortField_ChangeRate = 4
SortField_Status = 5
SortField_BidPrice = 6
SortField_AskPrice = 7
SortField_BidVol = 8
SortField_AskVol = 9
SortField_Volume = 10
SortField_Turnover = 11
SortField_Amplitude = 30
SortField_Score = 12
SortField_Premium = 13
SortField_EffectiveLeverage = 14
SortField_Delta = 15
SortField_ImpliedVolatility = 16
SortField_Type = 17
SortField_StrikePrice = 18
SortField_BreakEvenPoint = 19
SortField_MaturityTime = 20
SortField_ListTime = 21
SortField_LastTradeTime = 22
SortField_Leverage = 23
SortField_InOutMoney = 24
SortField_RecoveryPrice = 25
SortField_ChangePrice = 26
SortField_Change = 27
SortField_StreetRate = 28
SortField_StreetVol = 29
SortField_WarrantName = 31
SortField_Issuer = 32
SortField_LotSize = 33
SortField_IssueSize = 34
SortField_UpperStrikePrice = 45
SortField_LowerStrikePrice = 46
SortField_InLinePriceStatus = 47
SortField_PreCurPrice = 35
SortField_AfterCurPrice = 36
SortField_PrePriceChangeVal = 37
SortField_AfterPriceChangeVal = 38
SortField_PreChangeRate = 39
SortField_AfterChangeRate = 40
SortField_PreAmplitude = 41
SortField_AfterAmplitude = 42
SortField_PreTurnover = 43
SortField_AfterTurnover = 44
SortField_LastSettlePrice = 48
SortField_Position = 49
SortField_PositionChange = 50
Issuer_Unknow = 0
Issuer_SG = 1
Issuer_BP = 2
Issuer_CS = 3
Issuer_CT = 4
Issuer_EA = 5
Issuer_GS = 6
Issuer_HS = 7
Issuer_JP = 8
Issuer_MB = 9
Issuer_SC = 10
Issuer_UB = 11
Issuer_BI = 12
Issuer_DB = 13
Issuer_DC = 14
Issuer_ML = 15
Issuer_NM = 16
Issuer_RB = 17
Issuer_RS = 18
Issuer_BC = 19
Issuer_HT = 20
Issuer_VT = 21
Issuer_KC = 22
Issuer_MS = 23
Issuer_GJ = 24
Issuer_XZ = 25
Issuer_HU = 26
Issuer_KS = 27
Issuer_CI = 28
IpoPeriod_Unknow = 0
IpoPeriod_Today = 1
IpoPeriod_Tomorrow = 2
IpoPeriod_Nextweek = 3
IpoPeriod_Lastweek = 4
IpoPeriod_Lastmonth = 5
PriceType_Unknow = 0
PriceType_Outside = 1
PriceType_WithIn = 2
WarrantStatus_Unknow = 0
WarrantStatus_Normal = 1
WarrantStatus_Suspend = 2
WarrantStatus_StopTrade = 3
WarrantStatus_PendingListing = 4
CompanyAct_None = 0
CompanyAct_Split = 1
CompanyAct_Join = 2
CompanyAct_Bonus = 4
CompanyAct_Transfer = 8
CompanyAct_Allot = 16
CompanyAct_Add = 32
CompanyAct_Dividend = 64
CompanyAct_SPDividend = 128
QotRight_Unknow = 0
QotRight_Bmp = 1
QotRight_Level1 = 2
QotRight_Level2 = 3
QotRight_SF = 4
QotRight_No = 5
PriceReminderType_Unknown = 0
PriceReminderType_PriceUp = 1
PriceReminderType_PriceDown = 2
PriceReminderType_ChangeRateUp = 3
PriceReminderType_ChangeRateDown = 4
PriceReminderType_5MinChangeRateUp = 5
PriceReminderType_5MinChangeRateDown = 6
PriceReminderType_VolumeUp = 7
PriceReminderType_TurnoverUp = 8
PriceReminderType_TurnoverRateUp = 9
PriceReminderType_BidPriceUp = 10
PriceReminderType_AskPriceDown = 11
PriceReminderType_BidVolUp = 12
PriceReminderType_AskVolUp = 13
PriceReminderType_3MinChangeRateUp = 14
PriceReminderType_3MinChangeRateDown = 15
PriceReminderFreq_Unknown = 0
PriceReminderFreq_Always = 1
PriceReminderFreq_OnceADay = 2
PriceReminderFreq_OnlyOnce = 3
PriceReminderMarketStatus_Unknow = 0
PriceReminderMarketStatus_Open = 1
PriceReminderMarketStatus_USPre = 2
PriceReminderMarketStatus_USAfter = 3
PriceReminderMarketStatus_USOverNight = 4
AssetClass_Unknow = 0
AssetClass_Stock = 1
AssetClass_Bond = 2
AssetClass_Commodity = 3
AssetClass_CurrencyMarket = 4
AssetClass_Future = 5
AssetClass_Swap = 6
ExpirationCycle_Unknown = 0
ExpirationCycle_Week = 1
ExpirationCycle_Month = 2
ExpirationCycle_MonthEnd = 3
ExpirationCycle_Quarter = 4
ExpirationCycle_WeekMon = 11
ExpirationCycle_WeekTue = 12
ExpirationCycle_WeekWed = 13
ExpirationCycle_WeekThu = 14
ExpirationCycle_WeekFri = 15
OptionStandardType_Unknown = 0
OptionStandardType_Standard = 1
OptionStandardType_NonStandard = 2
OptionSettlementMode_Unknown = 0
OptionSettlementMode_AM = 1
OptionSettlementMode_PM = 2
ExchType_Unknown = 0
ExchType_HK_MainBoard = 1
ExchType_HK_GEMBoard = 2
ExchType_HK_HKEX = 3
ExchType_US_NYSE = 4
ExchType_US_Nasdaq = 5
ExchType_US_Pink = 6
ExchType_US_AMEX = 7
ExchType_US_Option = 8
ExchType_US_NYMEX = 9
ExchType_US_COMEX = 10
ExchType_US_CBOT = 11
ExchType_US_CME = 12
ExchType_US_CBOE = 13
ExchType_CN_SH = 14
ExchType_CN_SZ = 15
ExchType_CN_STIB = 16
ExchType_SG_SGX = 17
ExchType_JP_OSE = 18
PeriodType_Unknown = 0
PeriodType_INTRADAY = 1
PeriodType_DAY = 2
PeriodType_WEEK = 3
PeriodType_MONTH = 4



_SECURITY = _descriptor.Descriptor(
  name='Security',
  full_name='Qot_Common.Security',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='market', full_name='Qot_Common.Security.market', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='Qot_Common.Security.code', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=46,
  serialized_end=86,
)


_KLINE = _descriptor.Descriptor(
  name='KLine',
  full_name='Qot_Common.KLine',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='time', full_name='Qot_Common.KLine.time', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isBlank', full_name='Qot_Common.KLine.isBlank', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='highPrice', full_name='Qot_Common.KLine.highPrice', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='openPrice', full_name='Qot_Common.KLine.openPrice', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lowPrice', full_name='Qot_Common.KLine.lowPrice', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='closePrice', full_name='Qot_Common.KLine.closePrice', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastClosePrice', full_name='Qot_Common.KLine.lastClosePrice', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volume', full_name='Qot_Common.KLine.volume', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turnover', full_name='Qot_Common.KLine.turnover', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turnoverRate', full_name='Qot_Common.KLine.turnoverRate', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pe', full_name='Qot_Common.KLine.pe', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='changeRate', full_name='Qot_Common.KLine.changeRate', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='Qot_Common.KLine.timestamp', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=89,
  serialized_end=334,
)


_OPTIONBASICQOTEXDATA = _descriptor.Descriptor(
  name='OptionBasicQotExData',
  full_name='Qot_Common.OptionBasicQotExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='strikePrice', full_name='Qot_Common.OptionBasicQotExData.strikePrice', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractSize', full_name='Qot_Common.OptionBasicQotExData.contractSize', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractSizeFloat', full_name='Qot_Common.OptionBasicQotExData.contractSizeFloat', index=2,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='openInterest', full_name='Qot_Common.OptionBasicQotExData.openInterest', index=3,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='impliedVolatility', full_name='Qot_Common.OptionBasicQotExData.impliedVolatility', index=4,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='premium', full_name='Qot_Common.OptionBasicQotExData.premium', index=5,
      number=5, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delta', full_name='Qot_Common.OptionBasicQotExData.delta', index=6,
      number=6, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gamma', full_name='Qot_Common.OptionBasicQotExData.gamma', index=7,
      number=7, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vega', full_name='Qot_Common.OptionBasicQotExData.vega', index=8,
      number=8, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta', full_name='Qot_Common.OptionBasicQotExData.theta', index=9,
      number=9, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rho', full_name='Qot_Common.OptionBasicQotExData.rho', index=10,
      number=10, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='netOpenInterest', full_name='Qot_Common.OptionBasicQotExData.netOpenInterest', index=11,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expiryDateDistance', full_name='Qot_Common.OptionBasicQotExData.expiryDateDistance', index=12,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractNominalValue', full_name='Qot_Common.OptionBasicQotExData.contractNominalValue', index=13,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ownerLotMultiplier', full_name='Qot_Common.OptionBasicQotExData.ownerLotMultiplier', index=14,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='optionAreaType', full_name='Qot_Common.OptionBasicQotExData.optionAreaType', index=15,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractMultiplier', full_name='Qot_Common.OptionBasicQotExData.contractMultiplier', index=16,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='indexOptionType', full_name='Qot_Common.OptionBasicQotExData.indexOptionType', index=17,
      number=18, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=337,
  serialized_end=755,
)


_PREAFTERMARKETDATA = _descriptor.Descriptor(
  name='PreAfterMarketData',
  full_name='Qot_Common.PreAfterMarketData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='price', full_name='Qot_Common.PreAfterMarketData.price', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='highPrice', full_name='Qot_Common.PreAfterMarketData.highPrice', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lowPrice', full_name='Qot_Common.PreAfterMarketData.lowPrice', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volume', full_name='Qot_Common.PreAfterMarketData.volume', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turnover', full_name='Qot_Common.PreAfterMarketData.turnover', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='changeVal', full_name='Qot_Common.PreAfterMarketData.changeVal', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='changeRate', full_name='Qot_Common.PreAfterMarketData.changeRate', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amplitude', full_name='Qot_Common.PreAfterMarketData.amplitude', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=758,
  serialized_end=922,
)


_FUTUREBASICQOTEXDATA = _descriptor.Descriptor(
  name='FutureBasicQotExData',
  full_name='Qot_Common.FutureBasicQotExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lastSettlePrice', full_name='Qot_Common.FutureBasicQotExData.lastSettlePrice', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position', full_name='Qot_Common.FutureBasicQotExData.position', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='positionChange', full_name='Qot_Common.FutureBasicQotExData.positionChange', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expiryDateDistance', full_name='Qot_Common.FutureBasicQotExData.expiryDateDistance', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=924,
  serialized_end=1041,
)


_WARRANTBASICQOTEXDATA = _descriptor.Descriptor(
  name='WarrantBasicQotExData',
  full_name='Qot_Common.WarrantBasicQotExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='delta', full_name='Qot_Common.WarrantBasicQotExData.delta', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='impliedVolatility', full_name='Qot_Common.WarrantBasicQotExData.impliedVolatility', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='premium', full_name='Qot_Common.WarrantBasicQotExData.premium', index=2,
      number=3, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1043,
  serialized_end=1125,
)


_BASICQOT = _descriptor.Descriptor(
  name='BasicQot',
  full_name='Qot_Common.BasicQot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='security', full_name='Qot_Common.BasicQot.security', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Qot_Common.BasicQot.name', index=1,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isSuspended', full_name='Qot_Common.BasicQot.isSuspended', index=2,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listTime', full_name='Qot_Common.BasicQot.listTime', index=3,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priceSpread', full_name='Qot_Common.BasicQot.priceSpread', index=4,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updateTime', full_name='Qot_Common.BasicQot.updateTime', index=5,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='highPrice', full_name='Qot_Common.BasicQot.highPrice', index=6,
      number=6, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='openPrice', full_name='Qot_Common.BasicQot.openPrice', index=7,
      number=7, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lowPrice', full_name='Qot_Common.BasicQot.lowPrice', index=8,
      number=8, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='curPrice', full_name='Qot_Common.BasicQot.curPrice', index=9,
      number=9, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastClosePrice', full_name='Qot_Common.BasicQot.lastClosePrice', index=10,
      number=10, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volume', full_name='Qot_Common.BasicQot.volume', index=11,
      number=11, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turnover', full_name='Qot_Common.BasicQot.turnover', index=12,
      number=12, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turnoverRate', full_name='Qot_Common.BasicQot.turnoverRate', index=13,
      number=13, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amplitude', full_name='Qot_Common.BasicQot.amplitude', index=14,
      number=14, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='darkStatus', full_name='Qot_Common.BasicQot.darkStatus', index=15,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='optionExData', full_name='Qot_Common.BasicQot.optionExData', index=16,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listTimestamp', full_name='Qot_Common.BasicQot.listTimestamp', index=17,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updateTimestamp', full_name='Qot_Common.BasicQot.updateTimestamp', index=18,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='preMarket', full_name='Qot_Common.BasicQot.preMarket', index=19,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='afterMarket', full_name='Qot_Common.BasicQot.afterMarket', index=20,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='secStatus', full_name='Qot_Common.BasicQot.secStatus', index=21,
      number=21, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='futureExData', full_name='Qot_Common.BasicQot.futureExData', index=22,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warrantExData', full_name='Qot_Common.BasicQot.warrantExData', index=23,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='overnight', full_name='Qot_Common.BasicQot.overnight', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1128,
  serialized_end=1857,
)


_TIMESHARE = _descriptor.Descriptor(
  name='TimeShare',
  full_name='Qot_Common.TimeShare',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='time', full_name='Qot_Common.TimeShare.time', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='minute', full_name='Qot_Common.TimeShare.minute', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isBlank', full_name='Qot_Common.TimeShare.isBlank', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='Qot_Common.TimeShare.price', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastClosePrice', full_name='Qot_Common.TimeShare.lastClosePrice', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='avgPrice', full_name='Qot_Common.TimeShare.avgPrice', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volume', full_name='Qot_Common.TimeShare.volume', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turnover', full_name='Qot_Common.TimeShare.turnover', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='Qot_Common.TimeShare.timestamp', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1860,
  serialized_end=2028,
)


_SECURITYSTATICBASIC = _descriptor.Descriptor(
  name='SecurityStaticBasic',
  full_name='Qot_Common.SecurityStaticBasic',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='security', full_name='Qot_Common.SecurityStaticBasic.security', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='Qot_Common.SecurityStaticBasic.id', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lotSize', full_name='Qot_Common.SecurityStaticBasic.lotSize', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='secType', full_name='Qot_Common.SecurityStaticBasic.secType', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Qot_Common.SecurityStaticBasic.name', index=4,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listTime', full_name='Qot_Common.SecurityStaticBasic.listTime', index=5,
      number=6, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delisting', full_name='Qot_Common.SecurityStaticBasic.delisting', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listTimestamp', full_name='Qot_Common.SecurityStaticBasic.listTimestamp', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exchType', full_name='Qot_Common.SecurityStaticBasic.exchType', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2031,
  serialized_end=2230,
)


_WARRANTSTATICEXDATA = _descriptor.Descriptor(
  name='WarrantStaticExData',
  full_name='Qot_Common.WarrantStaticExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='Qot_Common.WarrantStaticExData.type', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='owner', full_name='Qot_Common.WarrantStaticExData.owner', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2232,
  serialized_end=2304,
)


_OPTIONSTATICEXDATA = _descriptor.Descriptor(
  name='OptionStaticExData',
  full_name='Qot_Common.OptionStaticExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='Qot_Common.OptionStaticExData.type', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='owner', full_name='Qot_Common.OptionStaticExData.owner', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strikeTime', full_name='Qot_Common.OptionStaticExData.strikeTime', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strikePrice', full_name='Qot_Common.OptionStaticExData.strikePrice', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='suspend', full_name='Qot_Common.OptionStaticExData.suspend', index=4,
      number=5, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='market', full_name='Qot_Common.OptionStaticExData.market', index=5,
      number=6, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strikeTimestamp', full_name='Qot_Common.OptionStaticExData.strikeTimestamp', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='indexOptionType', full_name='Qot_Common.OptionStaticExData.indexOptionType', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expirationCycle', full_name='Qot_Common.OptionStaticExData.expirationCycle', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='optionStandardType', full_name='Qot_Common.OptionStaticExData.optionStandardType', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='optionSettlementMode', full_name='Qot_Common.OptionStaticExData.optionSettlementMode', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2307,
  serialized_end=2585,
)


_FUTURESTATICEXDATA = _descriptor.Descriptor(
  name='FutureStaticExData',
  full_name='Qot_Common.FutureStaticExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lastTradeTime', full_name='Qot_Common.FutureStaticExData.lastTradeTime', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastTradeTimestamp', full_name='Qot_Common.FutureStaticExData.lastTradeTimestamp', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isMainContract', full_name='Qot_Common.FutureStaticExData.isMainContract', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2587,
  serialized_end=2682,
)


_SECURITYSTATICINFO = _descriptor.Descriptor(
  name='SecurityStaticInfo',
  full_name='Qot_Common.SecurityStaticInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='basic', full_name='Qot_Common.SecurityStaticInfo.basic', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warrantExData', full_name='Qot_Common.SecurityStaticInfo.warrantExData', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='optionExData', full_name='Qot_Common.SecurityStaticInfo.optionExData', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='futureExData', full_name='Qot_Common.SecurityStaticInfo.futureExData', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2685,
  serialized_end=2917,
)


_BROKER = _descriptor.Descriptor(
  name='Broker',
  full_name='Qot_Common.Broker',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='Qot_Common.Broker.id', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Qot_Common.Broker.name', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pos', full_name='Qot_Common.Broker.pos', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderID', full_name='Qot_Common.Broker.orderID', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volume', full_name='Qot_Common.Broker.volume', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2919,
  serialized_end=2999,
)


_TICKER = _descriptor.Descriptor(
  name='Ticker',
  full_name='Qot_Common.Ticker',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='time', full_name='Qot_Common.Ticker.time', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sequence', full_name='Qot_Common.Ticker.sequence', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dir', full_name='Qot_Common.Ticker.dir', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='Qot_Common.Ticker.price', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volume', full_name='Qot_Common.Ticker.volume', index=4,
      number=5, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turnover', full_name='Qot_Common.Ticker.turnover', index=5,
      number=6, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recvTime', full_name='Qot_Common.Ticker.recvTime', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='Qot_Common.Ticker.type', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='typeSign', full_name='Qot_Common.Ticker.typeSign', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pushDataType', full_name='Qot_Common.Ticker.pushDataType', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='Qot_Common.Ticker.timestamp', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3002,
  serialized_end=3195,
)


_ORDERBOOKDETAIL = _descriptor.Descriptor(
  name='OrderBookDetail',
  full_name='Qot_Common.OrderBookDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='orderID', full_name='Qot_Common.OrderBookDetail.orderID', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volume', full_name='Qot_Common.OrderBookDetail.volume', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3197,
  serialized_end=3247,
)


_ORDERBOOK = _descriptor.Descriptor(
  name='OrderBook',
  full_name='Qot_Common.OrderBook',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='price', full_name='Qot_Common.OrderBook.price', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volume', full_name='Qot_Common.OrderBook.volume', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orederCount', full_name='Qot_Common.OrderBook.orederCount', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detailList', full_name='Qot_Common.OrderBook.detailList', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3249,
  serialized_end=3361,
)


_SHAREHOLDINGCHANGE = _descriptor.Descriptor(
  name='ShareHoldingChange',
  full_name='Qot_Common.ShareHoldingChange',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='holderName', full_name='Qot_Common.ShareHoldingChange.holderName', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='holdingQty', full_name='Qot_Common.ShareHoldingChange.holdingQty', index=1,
      number=2, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='holdingRatio', full_name='Qot_Common.ShareHoldingChange.holdingRatio', index=2,
      number=3, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='changeQty', full_name='Qot_Common.ShareHoldingChange.changeQty', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='changeRatio', full_name='Qot_Common.ShareHoldingChange.changeRatio', index=4,
      number=5, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='time', full_name='Qot_Common.ShareHoldingChange.time', index=5,
      number=6, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='Qot_Common.ShareHoldingChange.timestamp', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3364,
  serialized_end=3519,
)


_SUBINFO = _descriptor.Descriptor(
  name='SubInfo',
  full_name='Qot_Common.SubInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='subType', full_name='Qot_Common.SubInfo.subType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='securityList', full_name='Qot_Common.SubInfo.securityList', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3521,
  serialized_end=3591,
)


_CONNSUBINFO = _descriptor.Descriptor(
  name='ConnSubInfo',
  full_name='Qot_Common.ConnSubInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='subInfoList', full_name='Qot_Common.ConnSubInfo.subInfoList', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usedQuota', full_name='Qot_Common.ConnSubInfo.usedQuota', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isOwnConnData', full_name='Qot_Common.ConnSubInfo.isOwnConnData', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3593,
  serialized_end=3690,
)


_PLATEINFO = _descriptor.Descriptor(
  name='PlateInfo',
  full_name='Qot_Common.PlateInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plate', full_name='Qot_Common.PlateInfo.plate', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Qot_Common.PlateInfo.name', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plateType', full_name='Qot_Common.PlateInfo.plateType', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3692,
  serialized_end=3773,
)


_REHAB = _descriptor.Descriptor(
  name='Rehab',
  full_name='Qot_Common.Rehab',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='time', full_name='Qot_Common.Rehab.time', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='companyActFlag', full_name='Qot_Common.Rehab.companyActFlag', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fwdFactorA', full_name='Qot_Common.Rehab.fwdFactorA', index=2,
      number=3, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fwdFactorB', full_name='Qot_Common.Rehab.fwdFactorB', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bwdFactorA', full_name='Qot_Common.Rehab.bwdFactorA', index=4,
      number=5, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bwdFactorB', full_name='Qot_Common.Rehab.bwdFactorB', index=5,
      number=6, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='splitBase', full_name='Qot_Common.Rehab.splitBase', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='splitErt', full_name='Qot_Common.Rehab.splitErt', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='joinBase', full_name='Qot_Common.Rehab.joinBase', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='joinErt', full_name='Qot_Common.Rehab.joinErt', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bonusBase', full_name='Qot_Common.Rehab.bonusBase', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bonusErt', full_name='Qot_Common.Rehab.bonusErt', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transferBase', full_name='Qot_Common.Rehab.transferBase', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transferErt', full_name='Qot_Common.Rehab.transferErt', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allotBase', full_name='Qot_Common.Rehab.allotBase', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allotErt', full_name='Qot_Common.Rehab.allotErt', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allotPrice', full_name='Qot_Common.Rehab.allotPrice', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='addBase', full_name='Qot_Common.Rehab.addBase', index=17,
      number=18, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='addErt', full_name='Qot_Common.Rehab.addErt', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='addPrice', full_name='Qot_Common.Rehab.addPrice', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dividend', full_name='Qot_Common.Rehab.dividend', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spDividend', full_name='Qot_Common.Rehab.spDividend', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='Qot_Common.Rehab.timestamp', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3776,
  serialized_end=4218,
)

_BASICQOT.fields_by_name['security'].message_type = _SECURITY
_BASICQOT.fields_by_name['optionExData'].message_type = _OPTIONBASICQOTEXDATA
_BASICQOT.fields_by_name['preMarket'].message_type = _PREAFTERMARKETDATA
_BASICQOT.fields_by_name['afterMarket'].message_type = _PREAFTERMARKETDATA
_BASICQOT.fields_by_name['futureExData'].message_type = _FUTUREBASICQOTEXDATA
_BASICQOT.fields_by_name['warrantExData'].message_type = _WARRANTBASICQOTEXDATA
_BASICQOT.fields_by_name['overnight'].message_type = _PREAFTERMARKETDATA
_SECURITYSTATICBASIC.fields_by_name['security'].message_type = _SECURITY
_WARRANTSTATICEXDATA.fields_by_name['owner'].message_type = _SECURITY
_OPTIONSTATICEXDATA.fields_by_name['owner'].message_type = _SECURITY
_SECURITYSTATICINFO.fields_by_name['basic'].message_type = _SECURITYSTATICBASIC
_SECURITYSTATICINFO.fields_by_name['warrantExData'].message_type = _WARRANTSTATICEXDATA
_SECURITYSTATICINFO.fields_by_name['optionExData'].message_type = _OPTIONSTATICEXDATA
_SECURITYSTATICINFO.fields_by_name['futureExData'].message_type = _FUTURESTATICEXDATA
_ORDERBOOK.fields_by_name['detailList'].message_type = _ORDERBOOKDETAIL
_SUBINFO.fields_by_name['securityList'].message_type = _SECURITY
_CONNSUBINFO.fields_by_name['subInfoList'].message_type = _SUBINFO
_PLATEINFO.fields_by_name['plate'].message_type = _SECURITY
DESCRIPTOR.message_types_by_name['Security'] = _SECURITY
DESCRIPTOR.message_types_by_name['KLine'] = _KLINE
DESCRIPTOR.message_types_by_name['OptionBasicQotExData'] = _OPTIONBASICQOTEXDATA
DESCRIPTOR.message_types_by_name['PreAfterMarketData'] = _PREAFTERMARKETDATA
DESCRIPTOR.message_types_by_name['FutureBasicQotExData'] = _FUTUREBASICQOTEXDATA
DESCRIPTOR.message_types_by_name['WarrantBasicQotExData'] = _WARRANTBASICQOTEXDATA
DESCRIPTOR.message_types_by_name['BasicQot'] = _BASICQOT
DESCRIPTOR.message_types_by_name['TimeShare'] = _TIMESHARE
DESCRIPTOR.message_types_by_name['SecurityStaticBasic'] = _SECURITYSTATICBASIC
DESCRIPTOR.message_types_by_name['WarrantStaticExData'] = _WARRANTSTATICEXDATA
DESCRIPTOR.message_types_by_name['OptionStaticExData'] = _OPTIONSTATICEXDATA
DESCRIPTOR.message_types_by_name['FutureStaticExData'] = _FUTURESTATICEXDATA
DESCRIPTOR.message_types_by_name['SecurityStaticInfo'] = _SECURITYSTATICINFO
DESCRIPTOR.message_types_by_name['Broker'] = _BROKER
DESCRIPTOR.message_types_by_name['Ticker'] = _TICKER
DESCRIPTOR.message_types_by_name['OrderBookDetail'] = _ORDERBOOKDETAIL
DESCRIPTOR.message_types_by_name['OrderBook'] = _ORDERBOOK
DESCRIPTOR.message_types_by_name['ShareHoldingChange'] = _SHAREHOLDINGCHANGE
DESCRIPTOR.message_types_by_name['SubInfo'] = _SUBINFO
DESCRIPTOR.message_types_by_name['ConnSubInfo'] = _CONNSUBINFO
DESCRIPTOR.message_types_by_name['PlateInfo'] = _PLATEINFO
DESCRIPTOR.message_types_by_name['Rehab'] = _REHAB
DESCRIPTOR.enum_types_by_name['QotMarket'] = _QOTMARKET
DESCRIPTOR.enum_types_by_name['SecurityType'] = _SECURITYTYPE
DESCRIPTOR.enum_types_by_name['PlateSetType'] = _PLATESETTYPE
DESCRIPTOR.enum_types_by_name['WarrantType'] = _WARRANTTYPE
DESCRIPTOR.enum_types_by_name['OptionType'] = _OPTIONTYPE
DESCRIPTOR.enum_types_by_name['IndexOptionType'] = _INDEXOPTIONTYPE
DESCRIPTOR.enum_types_by_name['OptionAreaType'] = _OPTIONAREATYPE
DESCRIPTOR.enum_types_by_name['QotMarketState'] = _QOTMARKETSTATE
DESCRIPTOR.enum_types_by_name['TradeDateMarket'] = _TRADEDATEMARKET
DESCRIPTOR.enum_types_by_name['TradeDateType'] = _TRADEDATETYPE
DESCRIPTOR.enum_types_by_name['RehabType'] = _REHABTYPE
DESCRIPTOR.enum_types_by_name['KLType'] = _KLTYPE
DESCRIPTOR.enum_types_by_name['KLFields'] = _KLFIELDS
DESCRIPTOR.enum_types_by_name['SubType'] = _SUBTYPE
DESCRIPTOR.enum_types_by_name['TickerDirection'] = _TICKERDIRECTION
DESCRIPTOR.enum_types_by_name['TickerType'] = _TICKERTYPE
DESCRIPTOR.enum_types_by_name['DarkStatus'] = _DARKSTATUS
DESCRIPTOR.enum_types_by_name['SecurityStatus'] = _SECURITYSTATUS
DESCRIPTOR.enum_types_by_name['HolderCategory'] = _HOLDERCATEGORY
DESCRIPTOR.enum_types_by_name['PushDataType'] = _PUSHDATATYPE
DESCRIPTOR.enum_types_by_name['SortField'] = _SORTFIELD
DESCRIPTOR.enum_types_by_name['Issuer'] = _ISSUER
DESCRIPTOR.enum_types_by_name['IpoPeriod'] = _IPOPERIOD
DESCRIPTOR.enum_types_by_name['PriceType'] = _PRICETYPE
DESCRIPTOR.enum_types_by_name['WarrantStatus'] = _WARRANTSTATUS
DESCRIPTOR.enum_types_by_name['CompanyAct'] = _COMPANYACT
DESCRIPTOR.enum_types_by_name['QotRight'] = _QOTRIGHT
DESCRIPTOR.enum_types_by_name['PriceReminderType'] = _PRICEREMINDERTYPE
DESCRIPTOR.enum_types_by_name['PriceReminderFreq'] = _PRICEREMINDERFREQ
DESCRIPTOR.enum_types_by_name['PriceReminderMarketStatus'] = _PRICEREMINDERMARKETSTATUS
DESCRIPTOR.enum_types_by_name['AssetClass'] = _ASSETCLASS
DESCRIPTOR.enum_types_by_name['ExpirationCycle'] = _EXPIRATIONCYCLE
DESCRIPTOR.enum_types_by_name['OptionStandardType'] = _OPTIONSTANDARDTYPE
DESCRIPTOR.enum_types_by_name['OptionSettlementMode'] = _OPTIONSETTLEMENTMODE
DESCRIPTOR.enum_types_by_name['ExchType'] = _EXCHTYPE
DESCRIPTOR.enum_types_by_name['PeriodType'] = _PERIODTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Security = _reflection.GeneratedProtocolMessageType('Security', (_message.Message,), dict(
  DESCRIPTOR = _SECURITY,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.Security)
  ))
_sym_db.RegisterMessage(Security)

KLine = _reflection.GeneratedProtocolMessageType('KLine', (_message.Message,), dict(
  DESCRIPTOR = _KLINE,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.KLine)
  ))
_sym_db.RegisterMessage(KLine)

OptionBasicQotExData = _reflection.GeneratedProtocolMessageType('OptionBasicQotExData', (_message.Message,), dict(
  DESCRIPTOR = _OPTIONBASICQOTEXDATA,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.OptionBasicQotExData)
  ))
_sym_db.RegisterMessage(OptionBasicQotExData)

PreAfterMarketData = _reflection.GeneratedProtocolMessageType('PreAfterMarketData', (_message.Message,), dict(
  DESCRIPTOR = _PREAFTERMARKETDATA,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.PreAfterMarketData)
  ))
_sym_db.RegisterMessage(PreAfterMarketData)

FutureBasicQotExData = _reflection.GeneratedProtocolMessageType('FutureBasicQotExData', (_message.Message,), dict(
  DESCRIPTOR = _FUTUREBASICQOTEXDATA,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.FutureBasicQotExData)
  ))
_sym_db.RegisterMessage(FutureBasicQotExData)

WarrantBasicQotExData = _reflection.GeneratedProtocolMessageType('WarrantBasicQotExData', (_message.Message,), dict(
  DESCRIPTOR = _WARRANTBASICQOTEXDATA,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.WarrantBasicQotExData)
  ))
_sym_db.RegisterMessage(WarrantBasicQotExData)

BasicQot = _reflection.GeneratedProtocolMessageType('BasicQot', (_message.Message,), dict(
  DESCRIPTOR = _BASICQOT,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.BasicQot)
  ))
_sym_db.RegisterMessage(BasicQot)

TimeShare = _reflection.GeneratedProtocolMessageType('TimeShare', (_message.Message,), dict(
  DESCRIPTOR = _TIMESHARE,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.TimeShare)
  ))
_sym_db.RegisterMessage(TimeShare)

SecurityStaticBasic = _reflection.GeneratedProtocolMessageType('SecurityStaticBasic', (_message.Message,), dict(
  DESCRIPTOR = _SECURITYSTATICBASIC,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.SecurityStaticBasic)
  ))
_sym_db.RegisterMessage(SecurityStaticBasic)

WarrantStaticExData = _reflection.GeneratedProtocolMessageType('WarrantStaticExData', (_message.Message,), dict(
  DESCRIPTOR = _WARRANTSTATICEXDATA,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.WarrantStaticExData)
  ))
_sym_db.RegisterMessage(WarrantStaticExData)

OptionStaticExData = _reflection.GeneratedProtocolMessageType('OptionStaticExData', (_message.Message,), dict(
  DESCRIPTOR = _OPTIONSTATICEXDATA,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.OptionStaticExData)
  ))
_sym_db.RegisterMessage(OptionStaticExData)

FutureStaticExData = _reflection.GeneratedProtocolMessageType('FutureStaticExData', (_message.Message,), dict(
  DESCRIPTOR = _FUTURESTATICEXDATA,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.FutureStaticExData)
  ))
_sym_db.RegisterMessage(FutureStaticExData)

SecurityStaticInfo = _reflection.GeneratedProtocolMessageType('SecurityStaticInfo', (_message.Message,), dict(
  DESCRIPTOR = _SECURITYSTATICINFO,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.SecurityStaticInfo)
  ))
_sym_db.RegisterMessage(SecurityStaticInfo)

Broker = _reflection.GeneratedProtocolMessageType('Broker', (_message.Message,), dict(
  DESCRIPTOR = _BROKER,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.Broker)
  ))
_sym_db.RegisterMessage(Broker)

Ticker = _reflection.GeneratedProtocolMessageType('Ticker', (_message.Message,), dict(
  DESCRIPTOR = _TICKER,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.Ticker)
  ))
_sym_db.RegisterMessage(Ticker)

OrderBookDetail = _reflection.GeneratedProtocolMessageType('OrderBookDetail', (_message.Message,), dict(
  DESCRIPTOR = _ORDERBOOKDETAIL,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.OrderBookDetail)
  ))
_sym_db.RegisterMessage(OrderBookDetail)

OrderBook = _reflection.GeneratedProtocolMessageType('OrderBook', (_message.Message,), dict(
  DESCRIPTOR = _ORDERBOOK,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.OrderBook)
  ))
_sym_db.RegisterMessage(OrderBook)

ShareHoldingChange = _reflection.GeneratedProtocolMessageType('ShareHoldingChange', (_message.Message,), dict(
  DESCRIPTOR = _SHAREHOLDINGCHANGE,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.ShareHoldingChange)
  ))
_sym_db.RegisterMessage(ShareHoldingChange)

SubInfo = _reflection.GeneratedProtocolMessageType('SubInfo', (_message.Message,), dict(
  DESCRIPTOR = _SUBINFO,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.SubInfo)
  ))
_sym_db.RegisterMessage(SubInfo)

ConnSubInfo = _reflection.GeneratedProtocolMessageType('ConnSubInfo', (_message.Message,), dict(
  DESCRIPTOR = _CONNSUBINFO,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.ConnSubInfo)
  ))
_sym_db.RegisterMessage(ConnSubInfo)

PlateInfo = _reflection.GeneratedProtocolMessageType('PlateInfo', (_message.Message,), dict(
  DESCRIPTOR = _PLATEINFO,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.PlateInfo)
  ))
_sym_db.RegisterMessage(PlateInfo)

Rehab = _reflection.GeneratedProtocolMessageType('Rehab', (_message.Message,), dict(
  DESCRIPTOR = _REHAB,
  __module__ = 'Qot_Common_pb2'
  # @@protoc_insertion_point(class_scope:Qot_Common.Rehab)
  ))
_sym_db.RegisterMessage(Rehab)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ)github.com/futuopen/ftapi4go/pb/qotcommon'))
# @@protoc_insertion_point(module_scope)
