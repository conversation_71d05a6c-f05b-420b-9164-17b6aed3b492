syntax = "proto2";
package GetGlobalState;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/getglobalstate";

import "Common.proto";
import "Qot_Common.proto";


message C2S
{
	required uint64 userID = 1; //历史原因，目前已废弃，填0即可
}

message S2C
{
	required int32 marketHK = 1; //Qot_Common.QotMarketState,港股主板市场状态 
	required int32 marketUS = 2; //Qot_Common.QotMarketState,美股Nasdaq市场状态 
	required int32 marketSH = 3; //Qot_Common.QotMarketState,沪市状态 
	required int32 marketSZ = 4; //Qot_Common.QotMarketState,深市状态 
	required int32 marketHKFuture = 5; //Qot_Common.QotMarketState,港股期货市场状态 
	optional int32 marketUSFuture = 15; //Qot_Common.QotMarketState,美国期货市场状态 
	optional int32 marketSGFuture = 17; //Qot_Common.QotMarketState,新加坡期货市场状态 
	optional int32 marketJPFuture = 18; //Qot_Common.QotMarketState,日本期货市场状态
	required bool qotLogined = 6; //是否登陆行情服务器
	required bool trdLogined = 7; //是否登陆交易服务器
	required int32 serverVer = 8; //版本号
	required int32 serverBuildNo = 9; //buildNo
	required int64 time = 10; //当前服务器时间
	optional double localTime = 11; //当前本地时间
	optional Common.ProgramStatus programStatus = 12; //当前程序状态
	optional string qotSvrIpAddr = 13;
	optional string trdSvrIpAddr = 14;
	optional uint64 connID = 16; //此连接的连接ID，连接的唯一标识
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
