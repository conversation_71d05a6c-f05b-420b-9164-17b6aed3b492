#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FUTU API 配置文件
包含帳戶信息和連接設置
"""

# FUTU 帳戶信息
FUTU_ACCOUNT = {
    "user_id": "********",  # 牛牛號
    "password": "<PERSON>@0609",  # 登入密碼
    "websocket_key": "3e8229abd3ccdfdc"  # WebSocket密鑰
}

# OpenD 連接設置
OPEND_CONFIG = {
    "host": "127.0.0.1",  # OpenD 服務器地址
    "port": 11111,        # OpenD 服務器端口
    "is_encrypt": False   # 是否加密連接
}

# 期貨代碼配置
FUTURES_CODES = {
    "HSI_MAIN": "HK.HSImain",      # 恆生指數期貨主連
    "HSI_CURRENT": "HK.HSI2501",   # 恆生指數期貨當月合約 (示例)
    "MHI_MAIN": "HK.MHImain",      # 小型恆生指數期貨主連
}

# 監控設置
MONITOR_CONFIG = {
    "refresh_interval": 5,     # 刷新間隔（秒）
    "enable_push": True,       # 是否啟用推送
    "display_format": "table"  # 顯示格式: table, simple
}

# 權限信息
PERMISSIONS = {
    "futures_lv1": True,   # 期貨 LV1 權限
    "hk_stock": True,      # 港股權限
    "us_stock": False,     # 美股權限
    "a_stock": False       # A股權限
}

# 日誌設置
LOG_CONFIG = {
    "enable_logging": True,
    "log_level": "INFO",
    "log_file": "hsi_monitor.log"
}
