# -*- coding: utf-8 -*-
"""
    Trade query
"""
import datetime as dt

from ..common.utils import *
from ..quote.quote_query import pack_pb_req

# 无数据时的值
NoneDataValue = 'N/A'

def is_HKTrade_order_status_finish(status):
    val = int(status)
    if val == 3 or val == 5 or val == 6 or val == 7:
        return True
    return False


def is_USTrade_order_status_finish(status):
    val = int(status)
    if val == 3 or val == 5 or val == 6 or val == 7:
        return True
    return False


class GetAccountList:
    """Get the trade account list"""
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, user_id, conn_id, trd_category, need_general_sec_acc):
        from ..common.pb.Trd_GetAccList_pb2 import Request

        req = Request()
        req.c2s.userID = user_id
        _, req.c2s.trdCategory = TrdCategory.to_number(trd_category)
        req.c2s.needGeneralSecAccount = need_general_sec_acc
        return pack_pb_req(req, ProtoId.Trd_GetAccList, conn_id)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        raw_acc_list = rsp_pb.s2c.accList
        acc_list = [{
            'acc_id': record.accID,
            'trd_env': TrdEnv.to_string2(record.trdEnv) if record.HasField('trdEnv') else 'N/A',# 初始化枚举类型
            'trdMarket_list': [TrdMarket.to_string2(trdMkt) for trdMkt in record.trdMarketAuthList],
            'acc_type': TrdAccType.to_string2(record.accType) if record.HasField("accType") else TrdAccType.NONE,# 初始化枚举类型
            'uni_card_num': record.uniCardNum if record.HasField("uniCardNum") else "N/A",
            'card_num': record.cardNum if record.HasField("cardNum") else "N/A",
            'security_firm': SecurityFirm.to_string2(record.securityFirm) if record.HasField('securityFirm') else SecurityFirm.NONE,# 初始化枚举类型
            'sim_acc_type': SimAccType.to_string2(record.simAccType) if record.HasField('simAccType') else SimAccType.NONE,# 初始化枚举类型
            'trdmarket_auth': list(record.trdMarketAuthList),
            'acc_status': TrdAccStatus.to_string2(record.accStatus) if record.HasField('accStatus') else 'N/A',# 初始化枚举类型
        } for record in raw_acc_list]

        return RET_OK, "", acc_list


class UnlockTrade:
    """Unlock trade limitation lock"""
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, is_unlock, password_md5, conn_id, security_firm):
        """Convert from user request for trading days to PLS request"""
        from ..common.pb.Trd_UnlockTrade_pb2 import Request
        req = Request()
        req.c2s.unlock = is_unlock
        req.c2s.pwdMD5 = password_md5
        _, req.c2s.securityFirm = SecurityFirm.to_number(security_firm)

        return pack_pb_req(req, ProtoId.Trd_UnlockTrade, conn_id)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        if rsp_pb.HasField('retMsg'):
            return RET_OK, rsp_pb.retMsg, None
        return RET_OK, "", None


class SubAccPush:
    """sub acc push"""
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, acc_id_list, conn_id):
        from ..common.pb.Trd_SubAccPush_pb2 import Request
        req = Request()
        for x in acc_id_list:
            req.c2s.accIDList.append(x)

        return pack_pb_req(req, ProtoId.Trd_SubAccPush, conn_id)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        return RET_OK, "", None


class AccInfoQuery:
    """Class for querying information of account"""

    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, acc_id, trd_market, trd_env, conn_id, refresh_cache, currency):
        from ..common.pb.Trd_GetFunds_pb2 import Request
        req = Request()
        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_market)
        if refresh_cache:
            req.c2s.refreshCache = refresh_cache
        req.c2s.currency = Currency.to_number(currency)[1]
        return pack_pb_req(req, ProtoId.Trd_GetFunds, conn_id)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        raw_funds = rsp_pb.s2c.funds
        accinfo_list = [{
            'power': raw_funds.power,
            'max_power_short': raw_funds.maxPowerShort if raw_funds.HasField('maxPowerShort') else NoneDataValue,
            'net_cash_power': raw_funds.netCashPower if raw_funds.HasField('netCashPower') else NoneDataValue,
            'total_assets': raw_funds.totalAssets,
            'securities_assets': raw_funds.securitiesAssets if raw_funds.HasField('securitiesAssets') else NoneDataValue,
            'fund_assets': raw_funds.fundAssets if raw_funds.HasField('fundAssets') else NoneDataValue,
            'bond_assets': raw_funds.bondAssets if raw_funds.HasField('bondAssets') else NoneDataValue,
            'cash': raw_funds.cash,
            'market_val': raw_funds.marketVal,
            'long_mv': raw_funds.longMv if raw_funds.HasField('longMv') else NoneDataValue,
            'short_mv': raw_funds.shortMv if raw_funds.HasField('shortMv') else NoneDataValue,
            'pending_asset': raw_funds.pendingAsset if raw_funds.HasField('pendingAsset') else NoneDataValue,
            'interest_charged_amount': raw_funds.debtCash if raw_funds.HasField('debtCash') else NoneDataValue,
            'frozen_cash': raw_funds.frozenCash,
            'avl_withdrawal_cash': raw_funds.avlWithdrawalCash if raw_funds.HasField('avlWithdrawalCash') else NoneDataValue,
            'max_withdrawal': raw_funds.maxWithdrawal if raw_funds.HasField('maxWithdrawal') else NoneDataValue,
            'currency': Currency.to_string2(raw_funds.currency) if raw_funds.HasField('currency') else Currency.NONE,# 初始化枚举类型
            'available_funds': raw_funds.availableFunds if raw_funds.HasField('availableFunds') else NoneDataValue,
            'unrealized_pl': raw_funds.unrealizedPL if raw_funds.HasField('unrealizedPL') else NoneDataValue,
            'realized_pl': raw_funds.realizedPL if raw_funds.HasField('realizedPL') else NoneDataValue,
            'risk_level': CltRiskLevel.to_string2(raw_funds.riskLevel) if raw_funds.HasField('riskLevel') else CltRiskLevel.NONE,# 初始化枚举类型
            'risk_status': CltRiskStatus.to_string2(raw_funds.riskStatus) if raw_funds.HasField('riskStatus') else CltRiskStatus.NONE,# 初始化枚举类型
            'initial_margin': raw_funds.initialMargin if raw_funds.HasField('initialMargin') else NoneDataValue,
            'margin_call_margin': raw_funds.marginCallMargin if raw_funds.HasField('marginCallMargin') else NoneDataValue,
            'maintenance_margin': raw_funds.maintenanceMargin if raw_funds.HasField('maintenanceMargin') else NoneDataValue,
            'hk_cash': NoneDataValue,
            'hk_avl_withdrawal_cash': NoneDataValue,
            'hkd_net_cash_power': NoneDataValue,
            'hkd_assets': NoneDataValue,
            'us_cash': NoneDataValue,
            'us_avl_withdrawal_cash': NoneDataValue,
            'usd_net_cash_power': NoneDataValue,
            'usd_assets': NoneDataValue,
            'cn_cash': NoneDataValue,
            'cn_avl_withdrawal_cash': NoneDataValue,
            'cnh_net_cash_power': NoneDataValue,
            'cnh_assets': NoneDataValue,
            'jp_cash': NoneDataValue,
            'jp_avl_withdrawal_cash': NoneDataValue,
            'jpy_net_cash_power': NoneDataValue,
            'jpy_assets': NoneDataValue,
            'sg_cash': NoneDataValue,
            'sg_avl_withdrawal_cash': NoneDataValue,
            'sgd_net_cash_power': NoneDataValue,
            'sgd_assets': NoneDataValue,
            'au_cash': NoneDataValue,
            'au_avl_withdrawal_cash': NoneDataValue,
            'aud_net_cash_power': NoneDataValue,
            'aud_assets': NoneDataValue,
            'is_pdt':  get_pb_value(raw_funds, 'isPdt'),
            'pdt_seq': get_pb_value(raw_funds, 'pdtSeq'),
            'beginning_dtbp': get_pb_value(raw_funds, 'beginningDTBP'),
            'remaining_dtbp': get_pb_value(raw_funds, 'remainingDTBP'),
            'dt_call_amount': get_pb_value(raw_funds, 'dtCallAmount'),
            'dt_status': get_pb_enum(raw_funds, 'dtStatus', DTStatus, DTStatus.NONE),    
        }]
        for cashInfo in raw_funds.cashInfoList:
            if cashInfo.currency == Trd_Common_pb2.Currency_HKD:
                accinfo_list[0]['hk_cash'] = cashInfo.cash
                accinfo_list[0]['hk_avl_withdrawal_cash'] = cashInfo.availableBalance
                accinfo_list[0]['hkd_net_cash_power'] = cashInfo.netCashPower if cashInfo.HasField('netCashPower') else NoneDataValue
            elif cashInfo.currency == Trd_Common_pb2.Currency_USD:
                accinfo_list[0]['us_cash'] = cashInfo.cash
                accinfo_list[0]['us_avl_withdrawal_cash'] = cashInfo.availableBalance
                accinfo_list[0]['usd_net_cash_power'] = cashInfo.netCashPower if cashInfo.HasField('netCashPower') else NoneDataValue
            elif cashInfo.currency == Trd_Common_pb2.Currency_CNH:
                accinfo_list[0]['cn_cash'] = cashInfo.cash
                accinfo_list[0]['cn_avl_withdrawal_cash'] = cashInfo.availableBalance
                accinfo_list[0]['cnh_net_cash_power'] = cashInfo.netCashPower if cashInfo.HasField('netCashPower') else NoneDataValue
            elif cashInfo.currency == Trd_Common_pb2.Currency_JPY:
                accinfo_list[0]['jp_cash'] = cashInfo.cash
                accinfo_list[0]['jp_avl_withdrawal_cash'] = cashInfo.availableBalance
                accinfo_list[0]['jpy_net_cash_power'] = cashInfo.netCashPower if cashInfo.HasField('netCashPower') else NoneDataValue
            elif cashInfo.currency == Trd_Common_pb2.Currency_SGD:
                accinfo_list[0]['sg_cash'] = cashInfo.cash
                accinfo_list[0]['sg_avl_withdrawal_cash'] = cashInfo.availableBalance
                accinfo_list[0]['sgd_net_cash_power'] = cashInfo.netCashPower if cashInfo.HasField('netCashPower') else NoneDataValue
            elif cashInfo.currency == Trd_Common_pb2.Currency_AUD:
                accinfo_list[0]['au_cash'] = cashInfo.cash
                accinfo_list[0]['au_avl_withdrawal_cash'] = cashInfo.availableBalance
                accinfo_list[0]['aud_net_cash_power'] = cashInfo.netCashPower if cashInfo.HasField('netCashPower') else NoneDataValue
        for marketInfo in raw_funds.marketInfoList:
            if marketInfo.trdMarket == Trd_Common_pb2.TrdMarket_HK:
                accinfo_list[0]['hkd_assets'] = marketInfo.assets if marketInfo.HasField('assets') else NoneDataValue
            elif marketInfo.trdMarket == Trd_Common_pb2.TrdMarket_US:
                accinfo_list[0]['usd_assets'] = marketInfo.assets if marketInfo.HasField('assets') else NoneDataValue
            elif marketInfo.trdMarket == Trd_Common_pb2.TrdMarket_HKCC:
                accinfo_list[0]['cnh_assets'] = marketInfo.assets if marketInfo.HasField('assets') else NoneDataValue
            elif marketInfo.trdMarket == Trd_Common_pb2.TrdMarket_JP:
                accinfo_list[0]['jpy_assets'] = marketInfo.assets if marketInfo.HasField('assets') else NoneDataValue
            elif marketInfo.trdMarket == Trd_Common_pb2.TrdMarket_SG:
                accinfo_list[0]['sgd_assets'] = marketInfo.assets if marketInfo.HasField('assets') else NoneDataValue
            elif marketInfo.trdMarket == Trd_Common_pb2.TrdMarket_AU:
                accinfo_list[0]['aud_assets'] = marketInfo.assets if marketInfo.HasField('assets') else NoneDataValue
        return RET_OK, "", accinfo_list


class PositionListQuery:
    """Class for querying position list"""

    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, code, pl_ratio_min,
                 pl_ratio_max, trd_env, acc_id, trd_mkt, conn_id, refresh_cache, position_market):
        """Convert from user request for trading days to PLS request"""
        from ..common.pb.Trd_GetPositionList_pb2 import Request
        req = Request()
        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)
        if code:
            req.c2s.filterConditions.codeList.append(code)
        _, req.c2s.filterConditions.filterMarket = TrdMarket.to_number(position_market)
        if pl_ratio_min is not None:
            req.c2s.filterPLRatioMin = float(pl_ratio_min) / 100.0
        if pl_ratio_max is not None:
            req.c2s.filterPLRatioMax = float(pl_ratio_max) / 100.0
        if refresh_cache:
            req.c2s.refreshCache = refresh_cache

        return pack_pb_req(req, ProtoId.Trd_GetPositionList, conn_id)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        raw_position_list = rsp_pb.s2c.positionList

        position_list = [{
                             "code": merge_trd_mkt_stock_str(position.secMarket, position.code),
                             "stock_name": position.name,
                             "position_market": TrdMarket.to_string2(position.trdMarket) if position.HasField('trdMarket') else 'N/A',# 初始化枚举类型
                             "qty": position.qty,
                             "can_sell_qty": position.canSellQty,
                             "cost_price": position.costPrice if position.HasField('costPrice') else NoneDataValue,
                             "cost_price_valid": position.HasField('costPrice'),
                             "average_cost": position.averageCostPrice if position.HasField('averageCostPrice') else NoneDataValue,
                             "diluted_cost": position.dilutedCostPrice if position.HasField('dilutedCostPrice') else NoneDataValue,
                             "market_val": position.val,
                             "nominal_price": position.price,
                             "pl_ratio": 100 * position.plRatio if position.HasField('plRatio') else NoneDataValue,
                             "pl_ratio_valid": position.HasField('plRatio'),
                             "pl_ratio_avg_cost": 100 * position.averagePlRatio if position.HasField('averagePlRatio') else NoneDataValue,
                             "pl_val": position.plVal,
                             "pl_val_valid": position.HasField('plVal'),
                             "today_buy_qty": position.td_buyQty if position.HasField('td_buyQty') else NoneDataValue,
                             "today_buy_val": position.td_buyVal if position.HasField('td_buyVal') else NoneDataValue,
                             "today_pl_val": position.td_plVal if position.HasField('td_plVal') else NoneDataValue,
                             "today_trd_val": position.td_trdVal if position.HasField('td_trdVal') else NoneDataValue,
                             "today_sell_qty": position.td_sellQty if position.HasField('td_sellQty') else NoneDataValue,
                             "today_sell_val": position.td_sellVal if position.HasField('td_sellVal') else NoneDataValue,
                             "position_side": PositionSide.to_string2(position.positionSide) if position.HasField('positionSide') else 'N/A',# 初始化枚举类型
                             "unrealized_pl": position.unrealizedPL if position.HasField('unrealizedPL') else NoneDataValue,
                             "realized_pl": position.realizedPL if position.HasField('realizedPL') else NoneDataValue,
                             "currency": Currency.to_string2(position.currency) if position.HasField('currency') else NoneDataValue,
                         } for position in raw_position_list]
        return RET_OK, "", position_list


class OrderListQuery:
    """Class for querying list queue"""
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, order_id, status_filter_list, code, start, end,
                 trd_env, acc_id, trd_mkt, conn_id, refresh_cache, order_market):
        """Convert from user request for trading days to PLS request"""
        from ..common.pb.Trd_GetOrderList_pb2 import Request
        req = Request()
        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)

        if code:
            req.c2s.filterConditions.codeList.append(code)
        if order_id:
            req.c2s.filterConditions.orderIDExList.append(order_id)
        _, req.c2s.filterConditions.filterMarket = TrdMarket.to_number(order_market)

        if start:
            req.c2s.filterConditions.beginTime = start
        if end:
            req.c2s.filterConditions.endTime = end
        if refresh_cache:
            req.c2s.refreshCache = refresh_cache

        if len(status_filter_list):
            for order_status in status_filter_list:
                r, v = OrderStatus.to_number(order_status)
                if r:
                    req.c2s.filterStatusList.append(v)

        return pack_pb_req(req, ProtoId.Trd_GetOrderList, conn_id)

    @classmethod
    def parse_order(cls, rsp_pb, order):
        order_dict = {
            "code": merge_trd_mkt_stock_str(order.secMarket, order.code),
            "stock_name": order.name,
            "order_market": TrdMarket.to_string2(order.trdMarket) if order.HasField('trdMarket') else 'N/A',# 初始化枚举类型
            "trd_side": TrdSide.to_string2(order.trdSide) if order.HasField('trdSide') else 'N/A',# 初始化枚举类型
            "order_type": OrderType.to_string2(order.orderType) if order.HasField('orderType') else 'N/A',# 初始化枚举类型
            "order_status": OrderStatus.to_string2(order.orderStatus) if order.HasField('orderStatus') else 'N/A',# 初始化枚举类型
            "order_id": str(order.orderIDEx),
            "qty": order.qty,
            "price": order.price,
            "create_time": order.createTime,
            "updated_time": order.updateTime,
            "dealt_qty": order.fillQty,
            "dealt_avg_price": order.fillAvgPrice,
            "last_err_msg": order.lastErrMsg,
            "remark": order.remark if order.HasField("remark") else "",
            "time_in_force": TimeInForce.to_string2(order.timeInForce) if order.HasField('timeInForce') else 'N/A',# 初始化枚举类型
            "fill_outside_rth": order.fillOutsideRTH if order.HasField("fillOutsideRTH") else 'N/A',
            "session": Session.to_string2(order.session) if order.HasField('session') else 'N/A',
            "aux_price": order.auxPrice if order.HasField("auxPrice") else 'N/A',
            "trail_type": TrailType.to_string2(order.trailType) if order.HasField("trailType") else 'N/A',
            "trail_value": order.trailValue if order.HasField("trailValue") else 'N/A',
            "trail_spread": order.trailSpread if order.HasField("trailSpread") else 'N/A',
            "currency": Currency.to_string2(order.currency) if order.HasField("currency") else 'N/A',
        }
        return order_dict

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        raw_order_list = rsp_pb.s2c.orderList
        order_list = [OrderListQuery.parse_order(rsp_pb, order) for order in raw_order_list]
        return RET_OK, "", order_list


class PlaceOrder:
    """Palce order class"""
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, trd_side, order_type, price, qty,
                 code, adjust_limit, trd_env, sec_mkt_str, acc_id, trd_mkt, conn_id, remark,
                 time_in_force, fill_outside_rth, session, aux_price, trail_type ,trail_value ,trail_spread):
        """Convert from user request for place order to PLS request"""
        from ..common.pb.Trd_PlaceOrder_pb2 import Request
        req = Request()
        serial_no = get_unique_id32()
        req.c2s.packetID.serialNo = serial_no
        req.c2s.packetID.connID = conn_id

        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)

        _, req.c2s.trdSide = TrdSide.to_number(trd_side)
        _, req.c2s.orderType = OrderType.to_number(order_type)
        req.c2s.code = code
        req.c2s.qty = qty
        req.c2s.price = price
        req.c2s.adjustPrice = adjust_limit != 0
        req.c2s.adjustSideAndLimit = adjust_limit
        if remark is not None:
            req.c2s.remark = remark
        r, proto_qot_mkt = Market.to_number(sec_mkt_str)
        if not r:
            proto_qot_mkt = Qot_Common_pb2.QotMarket_Unknown
        proto_trd_sec_mkt = QOT_MARKET_TO_TRD_SEC_MARKET_MAP.get(proto_qot_mkt, Trd_Common_pb2.TrdSecMarket_Unknown)
        req.c2s.secMarket = proto_trd_sec_mkt
        ret, val = TimeInForce.to_number(time_in_force)
        if not ret:
            return RET_ERROR, val, None
        else:
            req.c2s.timeInForce = val
        if aux_price is not None:
            req.c2s.auxPrice = aux_price
        if trail_type is not None:
            ret, val = TrailType.to_number(trail_type)
            if not ret:
                return RET_ERROR, val, None
            else:
                req.c2s.trailType = val
        if trail_value is not None:
            req.c2s.trailValue = trail_value
        if trail_spread is not None:
            req.c2s.trailSpread = trail_spread

        req.c2s.fillOutsideRTH = fill_outside_rth
        _, req.c2s.session = Session.to_number(session)

        return pack_pb_req(req, ProtoId.Trd_PlaceOrder, conn_id, serial_no)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        order_id = str(rsp_pb.s2c.orderIDEx)

        return RET_OK, "", order_id


class ModifyOrder:
    """modify order class"""
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, modify_order_op, order_id, price, qty,
                 adjust_limit, trd_env, acc_id, trd_mkt, conn_id,
                 aux_price, trail_type, trail_value, trail_spread):
        """Convert from user request for place order to PLS request"""
        from ..common.pb.Trd_ModifyOrder_pb2 import Request
        req = Request()
        serial_no = get_unique_id32()
        req.c2s.packetID.serialNo = serial_no
        req.c2s.packetID.connID = conn_id

        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)

        # orderID不使用了，但是required字段必须传值
        req.c2s.orderID = 0
        req.c2s.orderIDEx = order_id
        _, req.c2s.modifyOrderOp = ModifyOrderOp.to_number(modify_order_op)
        req.c2s.forAll = False

        if modify_order_op == ModifyOrderOp.NORMAL:
            req.c2s.qty = qty
            req.c2s.price = price
            req.c2s.adjustPrice = adjust_limit != 0
            req.c2s.adjustSideAndLimit = adjust_limit
            if aux_price is not None:
                req.c2s.auxPrice = float(aux_price)
            if trail_type is not None:
                _, req.c2s.trailType = TrailType.to_number(trail_type)
            if trail_value is not None:
                req.c2s.trailValue = float(trail_value)
            if trail_spread is not None:
                req.c2s.trailSpread = float(trail_spread)

        return pack_pb_req(req, ProtoId.Trd_ModifyOrder, conn_id, serial_no)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        order_id = str(rsp_pb.s2c.orderIDEx)
        modify_order_list = [{
            'trd_env': TrdEnv.to_string2(rsp_pb.s2c.header.trdEnv) if rsp_pb.s2c.header.HasField('trdEnv') else 'N/A',# 初始化枚举类型
            'order_id': order_id
        }]

        return RET_OK, "", modify_order_list


class CancelOrder:
    """modify order class"""
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, trd_env, acc_id, trd_mkt, conn_id, trdmarket):
        """Convert from user request for place order to PLS request"""
        from ..common.pb.Trd_ModifyOrder_pb2 import Request
        req = Request()
        serial_no = get_unique_id32()
        req.c2s.packetID.serialNo = serial_no
        req.c2s.packetID.connID = conn_id

        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)
        _, req.c2s.trdMarket = TrdMarket.to_number(trdmarket)

        req.c2s.orderID = 0
        req.c2s.modifyOrderOp = Trd_Common_pb2.ModifyOrderOp_Cancel
        req.c2s.forAll = True
        return pack_pb_req(req, ProtoId.Trd_ModifyOrder, conn_id, serial_no)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None
        return RET_OK, "success", None


class DealListQuery:
    """Class for """
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, code, trd_env, acc_id, trd_mkt, conn_id, refresh_cache, deal_market):
        """Convert from user request for place order to PLS request"""
        from ..common.pb.Trd_GetOrderFillList_pb2 import Request
        req = Request()
        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)

        if code:
            req.c2s.filterConditions.codeList.append(code)
        _, req.c2s.filterConditions.filterMarket = TrdMarket.to_number(deal_market)

        if refresh_cache:
            req.c2s.refreshCache = refresh_cache

        return pack_pb_req(req, ProtoId.Trd_GetOrderFillList, conn_id)

    @classmethod
    def parse_deal(cls, rsp_pb, deal):
        deal_dict = {
            "code": merge_trd_mkt_stock_str(deal.secMarket, deal.code),
            "stock_name": deal.name,
            "deal_market": TrdMarket.to_string2(deal.trdMarket) if deal.HasField('trdMarket') else 'N/A',# 初始化枚举类型
            "deal_id": deal.fillID,
            "order_id": str(deal.orderIDEx) if deal.HasField('orderIDEx') else NoneDataValue,
            "qty": deal.qty,
            "price": deal.price,
            "trd_side": TrdSide.to_string2(deal.trdSide) if deal.HasField('trdSide') else 'N/A',# 初始化枚举类型
            "create_time": deal.createTime,
            "counter_broker_id": deal.counterBrokerID if deal.HasField('counterBrokerID') else NoneDataValue,
            "counter_broker_name": deal.counterBrokerName if deal.HasField('counterBrokerName') else NoneDataValue,
            "status": DealStatus.to_string2(deal.status) if deal.HasField("status") else NoneDataValue
        }
        return deal_dict

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        raw_deal_list = rsp_pb.s2c.orderFillList
        deal_list = [DealListQuery.parse_deal(rsp_pb, deal) for deal in raw_deal_list]

        return RET_OK, "", deal_list


class HistoryOrderListQuery:
    """Class for querying Histroy Order"""

    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, status_filter_list, code, start, end,
                 trd_env, acc_id, trd_mkt, conn_id, order_market):

        from ..common.pb.Trd_GetHistoryOrderList_pb2 import Request
        req = Request()
        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)

        if code:
            req.c2s.filterConditions.codeList.append(code)

        req.c2s.filterConditions.beginTime = start
        req.c2s.filterConditions.endTime = end
        _, req.c2s.filterConditions.filterMarket = TrdMarket.to_number(order_market)

        if status_filter_list:
            for order_status in status_filter_list:
                r, v = OrderStatus.to_number(order_status)
                if r:
                    req.c2s.filterStatusList.append(v)

        return pack_pb_req(req, ProtoId.Trd_GetHistoryOrderList, conn_id)

    @classmethod
    def unpack_rsp(cls, rsp_pb):

        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        raw_order_list = rsp_pb.s2c.orderList
        order_list = [{
                      "code": merge_trd_mkt_stock_str(order.secMarket, order.code),
                      "stock_name": order.name,
                      "order_market": TrdMarket.to_string2(order.trdMarket) if order.HasField('trdMarket') else 'N/A',# 初始化枚举类型
                      "trd_side": TrdSide.to_string2(order.trdSide) if order.HasField('trdSide') else 'N/A',# 初始化枚举类型
                      "order_type": OrderType.to_string2(order.orderType) if order.HasField('orderType') else 'N/A',# 初始化枚举类型
                      "order_status": OrderStatus.to_string2(order.orderStatus) if order.HasField('orderStatus') else 'N/A',# 初始化枚举类型
                      "order_id": str(order.orderIDEx),
                      "qty": order.qty,
                      "price": order.price,
                      "create_time": order.createTime,
                      "updated_time": order.updateTime,
                      "dealt_qty": order.fillQty,
                      "dealt_avg_price": order.fillAvgPrice,
                      "last_err_msg": order.lastErrMsg,
                      "remark": order.remark if order.HasField("remark") else "",
                      "time_in_force": TimeInForce.to_string2(order.timeInForce) if order.HasField('timeInForce') else 'N/A',# 初始化枚举类型
                      "fill_outside_rth": order.fillOutsideRTH if order.HasField("fillOutsideRTH") else 'N/A',
                      "aux_price": order.auxPrice if order.HasField("auxPrice") else 'N/A',
                      "trail_type": TrailType.to_string2(order.trailType) if order.HasField("trailType") else 'N/A',
                      "trail_value": order.trailValue if order.HasField("trailValue") else 'N/A',
                      "trail_spread": order.trailSpread if order.HasField("trailSpread") else 'N/A',
                      "currency": Currency.to_string2(order.currency) if order.HasField('currency') else NoneDataValue,
                      "session": Session.to_string2(order.session) if order.HasField('session') else 'N/A',
                      } for order in raw_order_list]
        return RET_OK, "", order_list


class OrderFeeQuery:
    """Class for querying order fee"""

    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, order_id_list, trd_env, acc_id, trd_mkt, conn_id):

        from ..common.pb.Trd_GetOrderFee_pb2 import Request
        req = Request()
        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)

        if order_id_list:
            for order_id in order_id_list:
                req.c2s.orderIdExList.append(str(order_id))

        return pack_pb_req(req, ProtoId.Trd_GetOrderFee, conn_id)

    @classmethod
    def get_fee_details(cls, order_fee):
        fee_details = []
        for fee_item in order_fee.feeList:
            fee_details.append((str(fee_item.title), fee_item.value))
        return fee_details

    @classmethod
    def unpack_rsp(cls, rsp_pb):

        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        none_data = 'N/A'
        raw_order_fee_list = rsp_pb.s2c.orderFeeList

        order_fee_list = [{
            "order_id": str(order_fee.orderIDEx),
            "fee_amount": order_fee.feeAmount if order_fee.HasField('feeAmount') else none_data,
            "fee_details": cls.get_fee_details(order_fee),
        } for order_fee in raw_order_fee_list]
        return RET_OK, "", order_fee_list


class HistoryDealListQuery:
    """Class for """

    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, code, start, end, trd_env, acc_id, trd_mkt, conn_id, deal_market):

        from ..common.pb.Trd_GetHistoryOrderFillList_pb2 import Request
        req = Request()
        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)

        if code:
            req.c2s.filterConditions.codeList.append(code)

        req.c2s.filterConditions.beginTime = start
        req.c2s.filterConditions.endTime = end
        _, req.c2s.filterConditions.filterMarket = TrdMarket.to_number(deal_market)

        return pack_pb_req(req, ProtoId.Trd_GetHistoryOrderFillList, conn_id)

    @classmethod
    def unpack_rsp(cls, rsp_pb):

        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        raw_deal_list = rsp_pb.s2c.orderFillList
        deal_list = [{
                    "code": merge_trd_mkt_stock_str(deal.secMarket, deal.code),
                    "stock_name": deal.name,
                    "deal_market": TrdMarket.to_string2(deal.trdMarket) if deal.HasField('trdMarket') else 'N/A',# 初始化枚举类型
                    "deal_id": deal.fillID,
                    "order_id": str(deal.orderIDEx) if deal.HasField('orderIDEx') else "",
                    "qty": deal.qty,
                    "price": deal.price,
                    "trd_side": TrdSide.to_string2(deal.trdSide) if deal.HasField('trdSide') else 'N/A',# 初始化枚举类型
                    "create_time": deal.createTime,
                    "counter_broker_id": deal.counterBrokerID if deal.HasField('counterBrokerID') else "",
                    "counter_broker_name": deal.counterBrokerName,
                    "status": DealStatus.to_string2(deal.status) if deal.HasField('status') else 'N/A'# 初始化枚举类型
                     } for deal in raw_deal_list]

        return RET_OK, "", deal_list


class UpdateOrderPush:
    """Class for order update push"""
    def __init__(self):
        pass

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg

        order_dict = OrderListQuery.parse_order(rsp_pb, rsp_pb.s2c.order)
        order_dict['trd_env'] = TrdEnv.to_string2(rsp_pb.s2c.header.trdEnv)
        order_dict['trd_market'] = TrdMarket.to_string2(rsp_pb.s2c.order.trdMarket)

        return RET_OK, order_dict


class UpdateDealPush:
    """Class for order update push"""
    def __init__(self):
        pass

    @classmethod
    def unpack_rsp(cls, rsp_pb):

        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg

        deal_dict = DealListQuery.parse_deal(rsp_pb, rsp_pb.s2c.orderFill)
        deal_dict['trd_env'] = TrdEnv.to_string2(rsp_pb.s2c.header.trdEnv)
        deal_dict['trd_market'] = TrdMarket.to_string2(rsp_pb.s2c.header.trdMarket)

        return RET_OK, deal_dict


class AccTradingInfoQuery:
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, order_type, code, price, order_id, adjust_limit, sec_mkt_str, trd_env, acc_id, trd_mkt, conn_id):

        from ..common.pb.Trd_GetMaxTrdQtys_pb2 import Request
        req = Request()
        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)

        _, req.c2s.orderType = OrderType.to_number(order_type)
        req.c2s.code = code
        req.c2s.price = price
        if order_id is not None:
            req.c2s.orderIDEx = order_id
        if adjust_limit == 0:
            req.c2s.adjustPrice = False
        else:
            req.c2s.adjustPrice = True
            req.c2s.adjustSideAndLimit = adjust_limit

        r, proto_qot_mkt = Market.to_number(sec_mkt_str)
        if not r:
            proto_qot_mkt = Qot_Common_pb2.QotMarket_Unknown

        proto_trd_sec_mkt = QOT_MARKET_TO_TRD_SEC_MARKET_MAP.get(proto_qot_mkt, Trd_Common_pb2.TrdSecMarket_Unknown)
        req.c2s.secMarket = proto_trd_sec_mkt

        return pack_pb_req(req, ProtoId.Trd_GetMaxTrdQtys, conn_id)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        from ..common.pb.Trd_Common_pb2 import MaxTrdQtys

        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        info = rsp_pb.s2c.maxTrdQtys    # type: MaxTrdQtys
        data = [{
            'max_cash_buy': info.maxCashBuy,
            'max_cash_and_margin_buy': info.maxCashAndMarginBuy if info.HasField('maxCashAndMarginBuy') else NoneDataValue,
            'max_position_sell': info.maxPositionSell,
            'max_sell_short': info.maxSellShort if info.HasField('maxSellShort') else NoneDataValue,
            'max_buy_back': info.maxBuyBack if info.HasField('maxBuyBack') else NoneDataValue,
            'long_required_im': info.longRequiredIM if info.HasField('longRequiredIM') else NoneDataValue,
            'short_required_im': info.shortRequiredIM if info.HasField('shortRequiredIM') else NoneDataValue
        }]

        return RET_OK, "", data


class MarginRatio:
    """Class for """
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, code_list, conn_id, acc_id, trd_mkt):
        """Convert from user request for place order to PLS request"""
        stock_tuple_list = []
        failure_tuple_list = []
        for stock_str in code_list:
            ret_code, content = split_stock_str(stock_str)
            if ret_code != RET_OK:
                error_str = content
                failure_tuple_list.append((ret_code, error_str))
                continue

            market_code, stock_code = content
            stock_tuple_list.append((market_code, stock_code))

        if len(failure_tuple_list) > 0:
            error_str = '\n'.join([x[1] for x in failure_tuple_list])
            return RET_ERROR, error_str, None

        from ..common.pb.Trd_GetMarginRatio_pb2 import Request
        req = Request()

        req.c2s.header.trdEnv = 1
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)

        for market, code in stock_tuple_list:
            stock_inst = req.c2s.securityList.add()
            stock_inst.market = market
            stock_inst.code = code

        return pack_pb_req(req, ProtoId.Trd_GetMarginRatio, conn_id)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        margin_ratio_list = rsp_pb.s2c.marginRatioInfoList
        ret_margin_ratio_list = []
        for margin_info in margin_ratio_list:
            margin_ratio_tmp = {}
            margin_ratio_tmp['code'] = merge_qot_mkt_stock_str(
                int(margin_info.security.market), margin_info.security.code)
            margin_ratio_tmp['is_long_permit'] = margin_info.isLongPermit if margin_info.HasField('isLongPermit') else 'N/A'  # 是否允许融资
            margin_ratio_tmp['is_short_permit'] = margin_info.isShortPermit if margin_info.HasField('isShortPermit') else 'N/A'  # 是否允许融券
            margin_ratio_tmp['short_pool_remain'] = margin_info.shortPoolRemain if margin_info.HasField('shortPoolRemain') else 'N/A'  # 卖空池剩余量
            margin_ratio_tmp['short_fee_rate'] = margin_info.shortFeeRate if margin_info.HasField('shortFeeRate') else 'N/A'   # 融券参考利率
            margin_ratio_tmp['alert_long_ratio'] = margin_info.alertLongRatio if margin_info.HasField('alertLongRatio') else 'N/A'  # 融资预警比率
            margin_ratio_tmp['alert_short_ratio'] = margin_info.alertShortRatio if margin_info.HasField('alertShortRatio') else 'N/A'   # 融券预警比率
            margin_ratio_tmp['im_long_ratio'] = margin_info.imLongRatio if margin_info.HasField('imLongRatio') else 'N/A'   # 融资初始保证金率
            margin_ratio_tmp['im_short_ratio'] = margin_info.imShortRatio if margin_info.HasField('imShortRatio') else 'N/A'   # 融券初始保证金率
            margin_ratio_tmp['mcm_long_ratio'] = margin_info.mcmLongRatio if margin_info.HasField('mcmLongRatio') else 'N/A'   # 融资 margin call 保证金率
            margin_ratio_tmp['mcm_short_ratio'] = margin_info.mcmShortRatio if margin_info.HasField('mcmShortRatio') else 'N/A'   # 融券 margin call 保证金率
            margin_ratio_tmp['mm_long_ratio'] = margin_info.mmLongRatio if margin_info.HasField('mmLongRatio') else 'N/A'  # 融资维持保证金率
            margin_ratio_tmp['mm_short_ratio'] = margin_info.mmShortRatio if margin_info.HasField('mmShortRatio') else 'N/A'   # 融券维持保证金率
            ret_margin_ratio_list.append(margin_ratio_tmp)

        return RET_OK, "", ret_margin_ratio_list


class FlowSummary:
    """Class for """
    def __init__(self):
        pass

    @classmethod
    def pack_req(cls, conn_id, acc_id, trd_mkt, clearing_date, direction, trd_env):
        """Convert from user request for place order to PLS request"""

        from ..common.pb.Trd_FlowSummary_pb2 import Request
        req = Request()

        _, req.c2s.header.trdEnv = TrdEnv.to_number(trd_env)
        req.c2s.header.accID = acc_id
        _, req.c2s.header.trdMarket = TrdMarket.to_number(trd_mkt)

        req.c2s.clearingDate = clearing_date
        _, req.c2s.cashFlowDirection = CashFlowDirection.to_number(direction)

        return pack_pb_req(req, ProtoId.Trd_FlowSummary, conn_id)

    @classmethod
    def unpack_rsp(cls, rsp_pb):
        """Convert from PLS response to user response"""
        if rsp_pb.retType != RET_OK:
            return RET_ERROR, rsp_pb.retMsg, None

        flow_summary_list = rsp_pb.s2c.flowSummaryInfoList
        ret_flow_summary_list = []
        for flow_summary in flow_summary_list:
            flow_summary_tmp = {}
            flow_summary_tmp['cashflow_id'] = flow_summary.cashFlowID if flow_summary.HasField('cashFlowID') else 'N/A'  # 清算日期
            flow_summary_tmp['clearing_date'] = flow_summary.clearingDate if flow_summary.HasField('clearingDate') else 'N/A'  # 清算日期
            flow_summary_tmp['settlement_date'] = flow_summary.settlementDate if flow_summary.HasField('settlementDate') else 'N/A'  # 結算日期
            flow_summary_tmp['currency'] = Currency.to_string2(flow_summary.currency) if flow_summary.HasField('currency') else 'N/A'  # 貨幣
            flow_summary_tmp['cashflow_type'] = flow_summary.cashFlowType if flow_summary.HasField('cashFlowType') else 'N/A'  # 類型
            flow_summary_tmp['cashflow_direction'] = CashFlowDirection.to_string2(flow_summary.cashFlowDirection) if flow_summary.HasField('cashFlowDirection') else 'N/A'  # 方向
            flow_summary_tmp['cashflow_amount'] = flow_summary.cashFlowAmount if flow_summary.HasField('cashFlowAmount') else 'N/A'  # 數量
            flow_summary_tmp['cashflow_remark'] = flow_summary.cashFlowRemark if flow_summary.HasField('cashFlowRemark') else 'N/A'  # 備註
            ret_flow_summary_list.append(flow_summary_tmp)

        return RET_OK, "", ret_flow_summary_list
