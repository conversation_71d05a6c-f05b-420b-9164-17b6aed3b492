syntax = "proto2";
package Qot_StockFilter;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotstockfilter";

import "Common.proto";
import "Qot_Common.proto";

// 使用到以下 6 个结构体（BaseFilter，AccumulateFilter，FinancialFilter，BaseData，AccumulateData，FinancialData）的用户请注意，由于属性字段名“field”与 C # 的 protobuf 保留函数名产生冲突，Futu API 将从 3.18 版本开始将这一字段统一更名为“fieldName”，请注意修改使用到对应接口的字段名。

// 简单属性
enum StockField  
{
	StockField_Unknown = 0; // 未知
	StockField_StockCode = 1; // 股票代码，不能填区间上下限值。
	StockField_StockName = 2; // 股票名称，不能填区间上下限值。
	StockField_CurPrice = 3; // 最新价（精确到小数点后 3 位，超出部分会被舍弃）例如填写[10,20]值区间
	StockField_CurPriceToHighest52WeeksRatio = 4; // (现价 - 52周最高)/52周最高，对应PC端离52周高点百分比（精确到小数点后 3 位，超出部分会被舍弃）例如填写[-30,-10]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%，如20实际对应20%）
	StockField_CurPriceToLowest52WeeksRatio = 5; // (现价 - 52周最低)/52周最低，对应PC端离52周低点百分比（精确到小数点后 3 位，超出部分会被舍弃）例如填写[20,40]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	StockField_HighPriceToHighest52WeeksRatio = 6; // (今日最高 - 52周最高)/52周最高（精确到小数点后 3 位，超出部分会被舍弃）例如填写[-3,-1]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	StockField_LowPriceToLowest52WeeksRatio = 7; // (今日最低 - 52周最低)/52周最低（精确到小数点后 3 位，超出部分会被舍弃）例如填写[10,70]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	StockField_VolumeRatio = 8; // 量比（精确到小数点后 3 位，超出部分会被舍弃）例如填写[0.5,30]值区间
	StockField_BidAskRatio = 9; // 委比（精确到小数点后 3 位，超出部分会被舍弃）例如填写[-20,80.5]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	StockField_LotPrice = 10; // 每手价格（精确到小数点后 3 位，超出部分会被舍弃）例如填写[40,100]值区间
	StockField_MarketVal = 11; // 市值（精确到小数点后 3 位，超出部分会被舍弃）例如填写[50000000,3000000000]值区间
	StockField_PeAnnual = 12; // 市盈率(静态)（精确到小数点后 3 位，超出部分会被舍弃）例如填写[-8,65.3]值区间
	StockField_PeTTM = 13; // 市盈率 TTM（精确到小数点后 3 位，超出部分会被舍弃）例如填写[-10,20.5]值区间
	StockField_PbRate = 14; // 市净率（精确到小数点后 3 位，超出部分会被舍弃）例如填写[0.5,20]值区间
	StockField_ChangeRate5min = 15; // 五分钟价格涨跌幅（精确到小数点后 3 位，超出部分会被舍弃）例如填写[-5,6.3]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	StockField_ChangeRateBeginYear = 16; // 年初至今价格涨跌幅（精确到小数点后 3 位，超出部分会被舍弃）例如填写[-50.1,400.7]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	    
	// 基础量价属性
	StockField_PSTTM = 17; // 市销率 TTM（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [100, 500] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%） 
	StockField_PCFTTM = 18; // 市现率 TTM（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [100, 1000] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	StockField_TotalShare = 19; // 总股数（精确到小数点后 0 位，超出部分会被舍弃）例如填写 [**********,**********] 值区间 (单位：股)（精确到小数点后 0 位，超出部分会被舍弃）
	StockField_FloatShare = 20; // 流通股数（精确到小数点后 0 位，超出部分会被舍弃）例如填写 [**********,**********] 值区间 (单位：股)（精确到小数点后 0 位，超出部分会被舍弃）
	StockField_FloatMarketVal = 21; // 流通市值（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [**********,**********] 值区间 (单位：元)
}

// 累积属性
enum AccumulateField
{
	AccumulateField_Unknown = 0; // 未知
	AccumulateField_ChangeRate = 1; // 涨跌幅（精确到小数点后 3 位，超出部分会被舍弃）例如填写[-10.2,20.4]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	AccumulateField_Amplitude = 2; // 振幅（精确到小数点后 3 位，超出部分会被舍弃）例如填写[0.5,20.6]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	AccumulateField_Volume = 3; // 日均成交量（精确到小数点后 0 位，超出部分会被舍弃）例如填写[2000,70000]值区间（精确到小数点后 0 位，超出部分会被舍弃）
	AccumulateField_Turnover = 4; // 日均成交额（精确到小数点后 3 位，超出部分会被舍弃）例如填写[1400,890000]值区间
	AccumulateField_TurnoverRate = 5; // 换手率（精确到小数点后 3 位，超出部分会被舍弃）例如填写[2,30]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
}

// 财务属性
enum FinancialField
{
	// 基础财务属性
	FinancialField_Unknown = 0; // 未知
	FinancialField_NetProfit = 1; // 净利润（精确到小数点后 3 位，超出部分会被舍弃）例如填写[100000000,2500000000]值区间
	FinancialField_NetProfitGrowth = 2; // 净利润增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写[-10,300]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_SumOfBusiness = 3; // 营业收入（精确到小数点后 3 位，超出部分会被舍弃）例如填写[100000000,6400000000]值区间
	FinancialField_SumOfBusinessGrowth = 4; // 营收同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写[-5,200]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_NetProfitRate = 5; // 净利率（精确到小数点后 3 位，超出部分会被舍弃）例如填写[10,113]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_GrossProfitRate = 6; // 毛利率（精确到小数点后 3 位，超出部分会被舍弃）例如填写[4,65]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_DebtAssetsRate = 7; // 资产负债率（精确到小数点后 3 位，超出部分会被舍弃）例如填写[5,470]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_ReturnOnEquityRate = 8; // 净资产收益率（精确到小数点后 3 位，超出部分会被舍弃）例如填写[20,230]值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	
	// 盈利能力属性
	FinancialField_ROIC = 9; // 投入资本回报率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_ROATTM = 10; // 资产回报率 TTM（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%。仅适用于年报。）
	FinancialField_EBITTTM = 11; // 息税前利润 TTM（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [**********,**********] 值区间（单位：元。仅适用于年报。）
	FinancialField_EBITDA = 12; // 税息折旧及摊销前利润（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [**********,**********] 值区间（单位：元）
	FinancialField_OperatingMarginTTM = 13; // 营业利润率 TTM（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%。仅适用于年报。）
	FinancialField_EBITMargin = 14; // EBIT利润率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_EBITDAMargin  = 15; // EBITDA利润率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_FinancialCostRate = 16; // 财务成本率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_OperatingProfitTTM  = 17; // 营业利润 TTM（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [**********,**********] 值区间 （单位：元。仅适用于年报。）
	FinancialField_ShareholderNetProfitTTM = 18; // 归属于母公司的净利润（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [**********,**********] 值区间 （单位：元。仅适用于年报。）
	FinancialField_NetProfitCashCoverTTM = 19; // 盈利中的现金收入比例（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,60.0] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%。仅适用于年报。）
	
	// 偿债能力属性
	FinancialField_CurrentRatio = 20; // 流动比率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [100,250] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_QuickRatio = 21; // 速动比率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [100,250] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）	
	
	// 清债能力属性
	FinancialField_CurrentAssetRatio = 22; // 流动资产率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [10,100] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_CurrentDebtRatio = 23; // 流动负债率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [10,100] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_EquityMultiplier = 24; // 权益乘数（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [100,180] 值区间
	FinancialField_PropertyRatio = 25; // 产权比率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [50,100] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%） 
	FinancialField_CashAndCashEquivalents = 26; // 现金和现金等价（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [**********,**********] 值区间（单位：元）	
	
	// 运营能力属性
	FinancialField_TotalAssetTurnover = 27; // 总资产周转率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [50,100] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_FixedAssetTurnover = 28; // 固定资产周转率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [50,100] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_InventoryTurnover = 29; // 存货周转率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [50,100] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_OperatingCashFlowTTM = 30; // 经营活动现金流 TTM（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [**********,**********] 值区间（单位：元。仅适用于年报。）
	FinancialField_AccountsReceivable = 31; // 应收账款净额（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [**********,**********] 值区间 例如填写 [**********,**********] 值区间 （单位：元）	
	
	// 成长能力属性
	FinancialField_EBITGrowthRate = 32 ; // EBIT同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_OperatingProfitGrowthRate = 33; // 营业利润同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_TotalAssetsGrowthRate = 34; // 总资产同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_ProfitToShareholdersGrowthRate = 35; // 归母净利润同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_ProfitBeforeTaxGrowthRate = 36; // 总利润同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_EPSGrowthRate = 37; // EPS同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_ROEGrowthRate = 38; // ROE同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_ROICGrowthRate = 39; // ROIC同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_NOCFGrowthRate = 40; // 经营现金流同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_NOCFPerShareGrowthRate = 41; // 每股经营现金流同比增长率（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [1.0,10.0] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）

	// 现金流属性
	FinancialField_OperatingRevenueCashCover = 42; // 经营现金收入比（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [10,100] 值区间（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）
	FinancialField_OperatingProfitToTotalProfit = 43; // 营业利润占比（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [10,100] 值区间 （该字段为百分比字段，默认不展示 %，如 20 实际对应 20%） 	

	// 市场表现属性
	FinancialField_BasicEPS = 44; // 基本每股收益（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [0.1,10] 值区间 (单位：元)
	FinancialField_DilutedEPS = 45; // 稀释每股收益（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [0.1,10] 值区间 (单位：元)
	FinancialField_NOCFPerShare = 46; // 每股经营现金净流量（精确到小数点后 3 位，超出部分会被舍弃）例如填写 [0.1,10] 值区间 (单位：元)	
}

// 自定义技术指标属性
enum CustomIndicatorField
{
	CustomIndicatorField_Unknown = 0; // 未知
	CustomIndicatorField_Price = 1; // 最新价格
	CustomIndicatorField_MA5 = 2; // 5日简单均线（不建议使用）
    CustomIndicatorField_MA10 = 3; // 10日简单均线 （不建议使用）
    CustomIndicatorField_MA20 = 4; // 20日简单均线 （不建议使用）
    CustomIndicatorField_MA30 = 5; // 30日简单均线 （不建议使用）
    CustomIndicatorField_MA60 = 6; // 60日简单均线 （不建议使用）
	CustomIndicatorField_MA120 = 7; // 120日简单均线（不建议使用）
    CustomIndicatorField_MA250 = 8; // 250日简单均线（不建议使用）
	CustomIndicatorField_RSI = 9; // RSI 指标参数的默认值为12
    CustomIndicatorField_EMA5 = 10; // 5日指数移动均线 （不建议使用）
    CustomIndicatorField_EMA10 = 11; // 10日指数移动均线 （不建议使用）
    CustomIndicatorField_EMA20 = 12; // 20日指数移动均线 （不建议使用）
	CustomIndicatorField_EMA30 = 13; // 30日指数移动均线 （不建议使用）
    CustomIndicatorField_EMA60 = 14; // 60日指数移动均线 （不建议使用）
    CustomIndicatorField_EMA120 = 15; // 120日指数移动均线（不建议使用）
    CustomIndicatorField_EMA250 = 16; // 250日指数移动均线（不建议使用）
	CustomIndicatorField_Value = 17; // 自定义数值（stock_field1 不支持此字段）
	CustomIndicatorField_MA = 30; // 简单均线
	CustomIndicatorField_EMA = 40; // 指数移动均线
	CustomIndicatorField_KDJ_K = 50; // KDJ 指标的 K 值。指标参数需要根据 KDJ 进行传参。不传则默认为 [9,3,3]
	CustomIndicatorField_KDJ_D = 51; // KDJ 指标的 D 值。指标参数需要根据 KDJ 进行传参。不传则默认为 [9,3,3]
	CustomIndicatorField_KDJ_J = 52; // KDJ 指标的 J 值。指标参数需要根据 KDJ 进行传参。不传则默认为 [9,3,3]	
	CustomIndicatorField_MACD_DIFF = 60; // MACD 指标的 DIFF 值。指标参数需要根据 MACD 进行传参。不传则默认为 [12,26,9]
	CustomIndicatorField_MACD_DEA = 61; // MACD 指标的 DEA 值。指标参数需要根据 MACD 进行传参。不传则默认为 [12,26,9]
	CustomIndicatorField_MACD = 62; // MACD 指标的 MACD 值。指标参数需要根据 MACD 进行传参。不传则默认为 [12,26,9]
	CustomIndicatorField_BOLL_UPPER = 70; // BOLL 指标的 UPPER 值。指标参数需要根据 BOLL 进行传参。不传则默认为 [20,2]
	CustomIndicatorField_BOLL_MIDDLER = 71; // BOLL 指标的 MIDDLER 值。指标参数需要根据 BOLL 进行传参。不传则默认为 [20,2]
	CustomIndicatorField_BOLL_LOWER = 72; // BOLL 指标的 LOWER 值。指标参数需要根据 BOLL 进行传参。不传则默认为 [20,2]
}

// 形态技术指标属性
enum PatternField
{
    PatternField_Unknown = 0 ; // 未知
    PatternField_MAAlignmentLong = 1 ; // MA多头排列（连续两天MA5>MA10>MA20>MA30>MA60，且当日收盘价大于前一天收盘价）
    PatternField_MAAlignmentShort = 2 ; // MA空头排列（连续两天MA5 <MA10 <MA20 <MA30 <MA60，且当日收盘价小于前一天收盘价）
    PatternField_EMAAlignmentLong = 3 ; // EMA多头排列（连续两天EMA5>EMA10>EMA20>EMA30>EMA60，且当日收盘价大于前一天收盘价）
    PatternField_EMAAlignmentShort = 4 ; // EMA空头排列（连续两天EMA5 <EMA10 <EMA20 <EMA30 <EMA60，且当日收盘价小于前一天收盘价）
    PatternField_RSIGoldCrossLow = 5 ; // RSI低位金叉（50以下，短线RSI上穿长线RSI（前一日短线RSI小于长线RSI，当日短线RSI大于长线RSI）） 
    PatternField_RSIDeathCrossHigh = 6 ; // RSI高位死叉（50以上，短线RSI下穿长线RSI（前一日短线RSI大于长线RSI，当日短线RSI小于长线RSI）） 
    PatternField_RSITopDivergence = 7 ; // RSI顶背离（相邻的两个K线波峰，后面的波峰对应的CLOSE>前面的波峰对应的CLOSE，后面波峰的RSI12值 <前面波峰的RSI12值）
    PatternField_RSIBottomDivergence = 8 ; // RSI底背离（相邻的两个K线波谷，后面的波谷对应的CLOSE <前面的波谷对应的CLOSE，后面波谷的RSI12值>前面波谷的RSI12值） 
    PatternField_KDJGoldCrossLow = 9 ; // KDJ低位金叉（KDJ的值都小于或等于30，且前一日K,J值分别小于D值，当日K,J值分别大于D值）
    PatternField_KDJDeathCrossHigh = 10 ; // KDJ高位死叉（KDJ的值都大于或等于70，且前一日K,J值分别大于D值，当日K,J值分别小于D值）
    PatternField_KDJTopDivergence = 11 ; // KDJ顶背离（相邻的两个K线波峰，后面的波峰对应的CLOSE>前面的波峰对应的CLOSE，后面波峰的J值 <前面波峰的J值）
    PatternField_KDJBottomDivergence = 12 ; // KDJ底背离（相邻的两个K线波谷，后面的波谷对应的CLOSE <前面的波谷对应的CLOSE，后面波谷的J值>前面波谷的J值）
    PatternField_MACDGoldCrossLow = 13 ; // MACD低位金叉（DIFF上穿DEA（前一日DIFF小于DEA，当日DIFF大于DEA））
    PatternField_MACDDeathCrossHigh = 14 ; // MACD高位死叉（DIFF下穿DEA（前一日DIFF大于DEA，当日DIFF小于DEA））
    PatternField_MACDTopDivergence = 15 ; // MACD顶背离（相邻的两个K线波峰，后面的波峰对应的CLOSE>前面的波峰对应的CLOSE，后面波峰的macd值 <前面波峰的macd值）
    PatternField_MACDBottomDivergence = 16 ; // MACD底背离（相邻的两个K线波谷，后面的波谷对应的CLOSE <前面的波谷对应的CLOSE，后面波谷的macd值>前面波谷的macd值）
    PatternField_BOLLBreakUpper = 17 ; // BOLL突破上轨（前一日股价低于上轨值，当日股价大于上轨值） 
    PatternField_BOLLLower = 18 ; // BOLL突破下轨（前一日股价高于下轨值，当日股价小于下轨值）
    PatternField_BOLLCrossMiddleUp = 19 ; // BOLL向上破中轨（前一日股价低于中轨值，当日股价大于中轨值）
    PatternField_BOLLCrossMiddleDown = 20 ; // BOLL向下破中轨（前一日股价大于中轨值，当日股价小于中轨值）
}

// 财务时间周期
enum FinancialQuarter
{
	FinancialQuarter_Unknown = 0; // 未知
	FinancialQuarter_Annual = 1; // 年报
	FinancialQuarter_FirstQuarter = 2; // 一季报
	FinancialQuarter_Interim = 3; // 中报
	FinancialQuarter_ThirdQuarter = 4; // 三季报
	FinancialQuarter_MostRecentQuarter = 5; // 最近季报
}

// 相对位置比较
enum RelativePosition
{
	RelativePosition_Unknown = 0; // 未知
	RelativePosition_More = 1; // 大于，first位于second的上方
	RelativePosition_Less = 2; // 小于，first位于second的下方
	RelativePosition_CrossUp = 3; // 升穿，first从下往上穿second
    RelativePosition_CrossDown = 4; // 跌穿，first从上往下穿second
}

// 排序方向
enum SortDir
{
	SortDir_No = 0; // 不排序
	SortDir_Ascend = 1; // 升序
	SortDir_Descend = 2; // 降序
}

// 简单属性筛选
message BaseFilter 
{ 
	required int32 fieldName = 1; // StockField 简单属性
	optional double filterMin = 2; // 区间下限（闭区间），不传代表下限为 -∞
	optional double filterMax = 3; // 区间上限（闭区间），不传代表上限为 +∞
	optional bool isNoFilter = 4; // 该字段是否不需要筛选，True：不筛选，False：筛选。不传默认不筛选
	optional int32 sortDir = 5; // SortDir 排序方向，默认不排序。
}

// 累积属性筛选
message AccumulateFilter
{
	required int32 fieldName = 1; // AccumulateField 累积属性
	optional double filterMin = 2; // 区间下限（闭区间），不传代表下限为 -∞
	optional double filterMax = 3; // 区间上限（闭区间），不传代表上限为 +∞
	optional bool isNoFilter = 4; // 该字段是否不需要筛选，True：不筛选，False：筛选。不传默认不筛选
	optional int32 sortDir = 5; // SortDir 排序方向，默认不排序。
	required int32 days = 6; // 近几日，累积时间
}

// 财务属性筛选
message FinancialFilter
{
	required int32 fieldName = 1; // FinancialField 财务属性
	optional double filterMin = 2; // 区间下限（闭区间），不传代表下限为 -∞
	optional double filterMax = 3; // 区间上限（闭区间），不传代表上限为 +∞
	optional bool isNoFilter = 4; // 该字段是否不需要筛选，True：不筛选，False：筛选。不传默认不筛选
	optional int32 sortDir = 5; // SortDir 排序方向，默认不排序。
	required int32 quarter = 6; // FinancialQuarter 财报累积时间
}

// 形态技术指标属性筛选
message PatternFilter
{
	required int32 fieldName = 1; // PatternField 形态技术指标属性
	required int32 klType = 2; // Qot_Common.KLType，K线类型，仅支持K_60M，K_DAY，K_WEEK，K_MON 四种时间周期
	optional bool isNoFilter = 3; // 该字段是否不需要筛选，True代表不筛选，False代表筛选。不传默认为不筛选
	optional int32 consecutivePeriod = 4; // 筛选连续周期都符合条件的数据，填写范围为[1,12]
}

// 自定义技术指标属性筛选
message CustomIndicatorFilter
{
	required int32 firstFieldName = 1; // CustomIndicatorField 自定义技术指标属性
	required int32 secondFieldName = 2; // CustomIndicatorField 自定义技术指标属性
	required int32 relativePosition = 3; // RelativePosition 相对位置
	optional double fieldValue = 4; // 自定义数值。当 stock_field2 选择自定义数值时，value 为必传参数。支持与KDJ_K，KDJ_D，KDJ_J，MACD_DIFF，MACD_DEA，MACD，BOLL_UPPER，BOLL_MIDDLER，BOLL_LOWER 进行比较（stock_field1 不支持此字段）
	required int32 klType = 5; // Qot_Common.KLType，K线类型，仅支持K_60M，K_DAY，K_WEEK，K_MON 四种时间周期	
	optional bool isNoFilter = 6; // 该字段是否不需要筛选，True代表不筛选，False代表筛选。不传默认为不筛选
	repeated int32 firstFieldParaList = 7; // 自定义指标参数 根据指标类型进行传参：1. MA：[平均移动周期] 2.EMA：[指数移动平均周期] 3.RSI：[RSI 指标周期] 4.MACD：[快速平均线值, 慢速平均线值, DIF值] 5.BOLL：[均线周期, 偏移值] 6.KDJ：[RSV 周期, K 值计算周期, D 值计算周期]
	repeated int32 secondFieldParaList = 8; // 自定义指标参数 根据指标类型进行传参：1. MA：[平均移动周期] 2.EMA：[指数移动平均周期] 3.RSI：[RSI 指标周期] 4.MACD：[快速平均线值, 慢速平均线值, DIF值] 5.BOLL：[均线周期, 偏移值] 6.KDJ：[RSV 周期, K 值计算周期, D 值计算周期]
	optional int32 consecutivePeriod = 9; // 筛选连续周期都符合条件的数据，填写范围为[1,12]
}

// 简单属性数据
message BaseData 
{ 
	required int32 fieldName = 1; // StockField 简单属性
	required double value = 2;
}

// 累积属性数据
message AccumulateData
{
	required int32 fieldName = 1; // AccumulateField 累积属性
	required double value = 2;
	required int32 days = 3; // 近几日，累积时间
}

// 财务属性数据
message FinancialData
{
	required int32 fieldName = 1; // FinancialField 财务属性
	required double value = 2;
	required int32 quarter = 3; // FinancialQuarter 财报累积时间
}

// 自定义技术指标属性数据
message CustomIndicatorData
{
	required int32 fieldName = 1; // CustomIndicatorField 自定义技术指标属性
	required double value = 2; 
	required int32 klType = 3; // Qot_Common.KLType，K线类型，仅支持K_60M，K_DAY，K_WEEK，K_MON 四种时间周期
	repeated int32 fieldParaList = 4; // 自定义指标参数 根据指标类型进行传参：1. MA：[平均移动周期] 2.EMA：[指数移动平均周期] 3.RSI：[RSI 指标周期] 4.MACD：[快速平均线值, 慢速平均线值, DIF值] 5.BOLL：[均线周期, 偏移值] 6.KDJ：[RSV 周期, K 值计算周期, D 值计算周期]
}

// 返回的股票数据
message StockData  
{
	required Qot_Common.Security security = 1; // 股票
	required string name = 2; // 股票名称
	repeated BaseData baseDataList = 3; // 筛选后的简单指标属性数据
	repeated AccumulateData accumulateDataList = 4; // 筛选后的累积指标属性数据
	repeated FinancialData financialDataList = 5; // 筛选后的财务指标属性数据
	repeated CustomIndicatorData customIndicatorDataList = 6; // 自定义技术指标属性数据
    // CustomIndicatorFilter 中的 firstFieldName 和 secondFieldName 的数值会在这个列表中分开返回
}

message C2S
{
	required int32 begin = 1; // 数据起始点
	required int32 num =  2;  // 请求数据个数，最大200        
	required int32 market= 3; // Qot_Common::QotMarket股票市场，支持沪股和深股，且沪股和深股不做区分都代表A股市场。
	// 以下为筛选条件，可选字段，不填表示不过滤
	optional Qot_Common.Security plate = 4; // 板块
	repeated BaseFilter baseFilterList = 5; // 简单指标过滤器
	repeated AccumulateFilter accumulateFilterList = 6; // 累积指标过滤器
	repeated FinancialFilter financialFilterList = 7; // 财务指标过滤器
	repeated PatternFilter patternFilterList = 8; // 形态技术指标过滤器
	repeated CustomIndicatorFilter customIndicatorFilterList = 9; // 自定义技术指标过滤器	
}

message S2C
{
	required bool lastPage = 1; // 是否最后一页了,false:非最后一页,还有窝轮记录未返回; true:已是最后一页
	required int32 allCount = 2; // 该条件请求所有数据的个数
	repeated StockData dataList = 3; // 返回的股票数据列表
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; // RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	optional S2C s2c = 4;
}
