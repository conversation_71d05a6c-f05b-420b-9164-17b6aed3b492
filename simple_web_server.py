#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版Web服務器 - 用於測試連接
"""

import json
import datetime
import random
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

class SimpleHandler(BaseHTTPRequestHandler):
    """簡化的請求處理器"""
    
    def do_GET(self):
        """處理GET請求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query_params = parse_qs(parsed_path.query)
        
        # 設置響應頭
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        # 生成模擬數據
        if path == '/api/connect':
            response = {'success': True, 'message': '模擬連接成功'}
            
        elif path == '/api/disconnect':
            response = {'success': True, 'message': '模擬斷開成功'}
            
        elif path == '/api/quote':
            product = query_params.get('product', ['HK.HSImain'])[0]
            base_price = 19850 if product == 'HK.HSImain' else 3970
            
            # 生成模擬報價數據
            current_price = base_price + random.uniform(-50, 50)
            response = {
                'success': True,
                'data': {
                    'code': product,
                    'name': '恆指期貨主連' if product == 'HK.HSImain' else '小型恆指期貨主連',
                    'last_price': round(current_price, 1),
                    'open_price': round(base_price + random.uniform(-20, 20), 1),
                    'high_price': round(current_price + random.uniform(0, 30), 1),
                    'low_price': round(current_price - random.uniform(0, 30), 1),
                    'prev_close_price': base_price,
                    'volume': random.randint(100000, 500000),
                    'turnover': random.randint(1000000000, 5000000000),
                    'data_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'timestamp': datetime.datetime.now().isoformat()
                }
            }
            
        elif path == '/api/kline':
            product = query_params.get('product', ['HK.HSImain'])[0]
            num = int(query_params.get('num', ['50'])[0])
            base_price = 19850 if product == 'HK.HSImain' else 3970
            
            # 生成模擬K線數據
            kline_data = []
            current_time = datetime.datetime.now()
            
            for i in range(num):
                time_point = current_time - datetime.timedelta(minutes=5*i)
                price = base_price + random.uniform(-100, 100)
                
                kline_item = {
                    'time': time_point.strftime('%Y-%m-%d %H:%M:%S'),
                    'open': round(price + random.uniform(-5, 5), 1),
                    'high': round(price + random.uniform(0, 10), 1),
                    'low': round(price - random.uniform(0, 10), 1),
                    'close': round(price, 1),
                    'volume': random.randint(1000, 10000),
                    'turnover': random.randint(10000000, 100000000)
                }
                kline_data.append(kline_item)
            
            # 計算移動平均線
            closes = [item['close'] for item in kline_data]
            for i, item in enumerate(kline_data):
                # 10SMA
                if i >= 9:
                    sma10 = sum(closes[i-9:i+1]) / 10
                    item['sma10'] = round(sma10, 1)
                else:
                    item['sma10'] = None
                
                # 20SMA
                if i >= 19:
                    sma20 = sum(closes[i-19:i+1]) / 20
                    item['sma20'] = round(sma20, 1)
                else:
                    item['sma20'] = None
            
            response = {'success': True, 'data': list(reversed(kline_data))}
            
        elif path == '/api/products':
            response = {
                'success': True,
                'data': {
                    'HK.HSImain': '恆指期貨主連',
                    'HK.MHImain': '小型恆指期貨主連'
                }
            }
            
        else:
            response = {'success': False, 'message': '未知的API端點'}
        
        # 發送響應
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def do_OPTIONS(self):
        """處理OPTIONS請求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定義日誌"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {format % args}")

def main():
    """主函數"""
    print("🧪 簡化版Web服務器 (模擬數據)")
    print("=" * 40)
    
    server_port = 8080
    
    try:
        with HTTPServer(('localhost', server_port), SimpleHandler) as httpd:
            print(f"🚀 服務器已啟動: http://localhost:{server_port}")
            print("📋 測試端點:")
            print("   - GET /api/connect")
            print("   - GET /api/quote?product=HK.HSImain")
            print("   - GET /api/kline?product=HK.HSImain")
            print("=" * 40)
            print("⏹️ 按 Ctrl+C 停止服務器")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n⏹️ 服務器已停止")
    except Exception as e:
        print(f"❌ 服務器錯誤: {e}")

if __name__ == "__main__":
    main()
