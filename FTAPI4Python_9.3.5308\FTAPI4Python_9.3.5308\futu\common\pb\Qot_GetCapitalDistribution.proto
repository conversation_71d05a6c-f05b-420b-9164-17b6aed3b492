syntax = "proto2";
package Qot_GetCapitalDistribution;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetcapitaldistribution";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	required Qot_Common.Security security = 1; //股票
}

message S2C
{
	optional double capitalInSuper = 9; // 流入资金额度，特大单
	required double capitalInBig = 1; //流入资金额度，大单
	required double capitalInMid = 2; //流入资金额度，中单
	required double capitalInSmall = 3; //流入资金额度，小单
	optional double capitalOutSuper = 10; // 流出资金额度，特大单
	required double capitalOutBig = 4; //流出资金额度，大单
	required double capitalOutMid = 5; //流出资金额度，中单
	required double capitalOutSmall = 6; //流出资金额度，小单
	optional string updateTime = 7; //更新时间字符串
	optional double updateTimestamp = 8; //更新时间戳
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
