syntax = "proto2";
package Qot_GetOrderBook;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetorderbook";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	required Qot_Common.Security security = 1; //股票
	required int32 num = 2; //请求的摆盘个数
}

message S2C
{
	required Qot_Common.Security security = 1; //股票
	optional string name = 8; //股票名称
	repeated Qot_Common.OrderBook orderBookAskList = 2; //卖盘
	repeated Qot_Common.OrderBook orderBookBidList = 3; //买盘
	optional string svrRecvTimeBid = 4; // 富途服务器从交易所收到数据的时间(for bid)部分数据的接收时间为零，例如服务器重启或第一次推送的缓存数据。该字段暂时只支持港股。
	optional double svrRecvTimeBidTimestamp = 5; // 富途服务器从交易所收到数据的时间戳(for bid)
	optional string svrRecvTimeAsk = 6; // 富途服务器从交易所收到数据的时间(for ask)
	optional double svrRecvTimeAskTimestamp = 7; // 富途服务器从交易所收到数据的时间戳(for ask)
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	optional S2C s2c = 4;
}
