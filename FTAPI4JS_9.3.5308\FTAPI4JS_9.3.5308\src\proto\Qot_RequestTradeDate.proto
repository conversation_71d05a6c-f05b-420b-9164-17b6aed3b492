syntax = "proto2";
package Qot_RequestTradeDate;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotrequesttradedate";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	// 当 market 和 security 同时存在，会忽略 market，仅对 security 进行查询。
	required int32 market = 1; //Qot_Common.TradeDateMarket,要查询的市场
	required string beginTime = 2; //开始时间字符串
	required string endTime = 3; //结束时间字符串
	optional Qot_Common.Security security = 4; // 指定标的
}

message TradeDate
{
	required string time = 1; //时间字符串
	optional double timestamp = 2; //时间戳
	optional int32 tradeDateType = 3; //Qot_Common.TradeDateType,交易时间类型
}

message S2C
{
	repeated TradeDate tradeDateList = 1; //交易日,注意该交易日是通过自然日去除周末以及节假日得到，不包括临时休市数据。
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
