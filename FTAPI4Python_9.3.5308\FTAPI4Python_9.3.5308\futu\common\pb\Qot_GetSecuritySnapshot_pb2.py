# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Qot_GetSecuritySnapshot.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Common_pb2 as Common__pb2
import Qot_Common_pb2 as Qot__Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Qot_GetSecuritySnapshot.proto',
  package='Qot_GetSecuritySnapshot',
  syntax='proto2',
  serialized_pb=_b('\n\x1dQot_GetSecuritySnapshot.proto\x12\x17Qot_GetSecuritySnapshot\x1a\x0c\x43ommon.proto\x1a\x10Qot_Common.proto\"1\n\x03\x43\x32S\x12*\n\x0csecurityList\x18\x01 \x03(\x0b\x32\x14.Qot_Common.Security\"\xf8\x02\n\x14\x45quitySnapshotExData\x12\x14\n\x0cissuedShares\x18\x01 \x02(\x03\x12\x17\n\x0fissuedMarketVal\x18\x02 \x02(\x01\x12\x10\n\x08netAsset\x18\x03 \x02(\x01\x12\x11\n\tnetProfit\x18\x04 \x02(\x01\x12\x18\n\x10\x65\x61rningsPershare\x18\x05 \x02(\x01\x12\x19\n\x11outstandingShares\x18\x06 \x02(\x03\x12\x1c\n\x14outstandingMarketVal\x18\x07 \x02(\x01\x12\x18\n\x10netAssetPershare\x18\x08 \x02(\x01\x12\x0e\n\x06\x65yRate\x18\t \x02(\x01\x12\x0e\n\x06peRate\x18\n \x02(\x01\x12\x0e\n\x06pbRate\x18\x0b \x02(\x01\x12\x11\n\tpeTTMRate\x18\x0c \x02(\x01\x12\x13\n\x0b\x64ividendTTM\x18\r \x01(\x01\x12\x18\n\x10\x64ividendRatioTTM\x18\x0e \x01(\x01\x12\x13\n\x0b\x64ividendLFY\x18\x0f \x01(\x01\x12\x18\n\x10\x64ividendLFYRatio\x18\x10 \x01(\x01\"\xd0\x04\n\x15WarrantSnapshotExData\x12\x16\n\x0e\x63onversionRate\x18\x01 \x02(\x01\x12\x13\n\x0bwarrantType\x18\x02 \x02(\x05\x12\x13\n\x0bstrikePrice\x18\x03 \x02(\x01\x12\x14\n\x0cmaturityTime\x18\x04 \x02(\t\x12\x14\n\x0c\x65ndTradeTime\x18\x05 \x02(\t\x12#\n\x05owner\x18\x06 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x15\n\rrecoveryPrice\x18\x07 \x02(\x01\x12\x14\n\x0cstreetVolumn\x18\x08 \x02(\x03\x12\x13\n\x0bissueVolumn\x18\t \x02(\x03\x12\x12\n\nstreetRate\x18\n \x02(\x01\x12\r\n\x05\x64\x65lta\x18\x0b \x02(\x01\x12\x19\n\x11impliedVolatility\x18\x0c \x02(\x01\x12\x0f\n\x07premium\x18\r \x02(\x01\x12\x19\n\x11maturityTimestamp\x18\x0e \x01(\x01\x12\x19\n\x11\x65ndTradeTimestamp\x18\x0f \x01(\x01\x12\x10\n\x08leverage\x18\x10 \x01(\x01\x12\x0c\n\x04ipop\x18\x11 \x01(\x01\x12\x16\n\x0e\x62reakEvenPoint\x18\x12 \x01(\x01\x12\x17\n\x0f\x63onversionPrice\x18\x13 \x01(\x01\x12\x1a\n\x12priceRecoveryRatio\x18\x14 \x01(\x01\x12\r\n\x05score\x18\x15 \x01(\x01\x12\x18\n\x10upperStrikePrice\x18\x16 \x01(\x01\x12\x18\n\x10lowerStrikePrice\x18\x17 \x01(\x01\x12\x19\n\x11inLinePriceStatus\x18\x18 \x01(\x05\x12\x12\n\nissuerCode\x18\x19 \x01(\t\"\x82\x04\n\x14OptionSnapshotExData\x12\x0c\n\x04type\x18\x01 \x02(\x05\x12#\n\x05owner\x18\x02 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x12\n\nstrikeTime\x18\x03 \x02(\t\x12\x13\n\x0bstrikePrice\x18\x04 \x02(\x01\x12\x14\n\x0c\x63ontractSize\x18\x05 \x02(\x05\x12\x19\n\x11\x63ontractSizeFloat\x18\x16 \x01(\x01\x12\x14\n\x0copenInterest\x18\x06 \x02(\x05\x12\x19\n\x11impliedVolatility\x18\x07 \x02(\x01\x12\x0f\n\x07premium\x18\x08 \x02(\x01\x12\r\n\x05\x64\x65lta\x18\t \x02(\x01\x12\r\n\x05gamma\x18\n \x02(\x01\x12\x0c\n\x04vega\x18\x0b \x02(\x01\x12\r\n\x05theta\x18\x0c \x02(\x01\x12\x0b\n\x03rho\x18\r \x02(\x01\x12\x17\n\x0fstrikeTimestamp\x18\x0e \x01(\x01\x12\x17\n\x0findexOptionType\x18\x0f \x01(\x05\x12\x17\n\x0fnetOpenInterest\x18\x10 \x01(\x05\x12\x1a\n\x12\x65xpiryDateDistance\x18\x11 \x01(\x05\x12\x1c\n\x14\x63ontractNominalValue\x18\x12 \x01(\x01\x12\x1a\n\x12ownerLotMultiplier\x18\x13 \x01(\x01\x12\x16\n\x0eoptionAreaType\x18\x14 \x01(\x05\x12\x1a\n\x12\x63ontractMultiplier\x18\x15 \x01(\x01\"P\n\x13IndexSnapshotExData\x12\x12\n\nraiseCount\x18\x01 \x02(\x05\x12\x11\n\tfallCount\x18\x02 \x02(\x05\x12\x12\n\nequalCount\x18\x03 \x02(\x05\"P\n\x13PlateSnapshotExData\x12\x12\n\nraiseCount\x18\x01 \x02(\x05\x12\x11\n\tfallCount\x18\x02 \x02(\x05\x12\x12\n\nequalCount\x18\x03 \x02(\x05\"\xa4\x01\n\x14\x46utureSnapshotExData\x12\x17\n\x0flastSettlePrice\x18\x01 \x02(\x01\x12\x10\n\x08position\x18\x02 \x02(\x05\x12\x16\n\x0epositionChange\x18\x03 \x02(\x05\x12\x15\n\rlastTradeTime\x18\x04 \x02(\t\x12\x1a\n\x12lastTradeTimestamp\x18\x05 \x01(\x01\x12\x16\n\x0eisMainContract\x18\x06 \x02(\x08\"\x8f\x01\n\x13TrustSnapshotExData\x12\x15\n\rdividendYield\x18\x01 \x02(\x01\x12\x0b\n\x03\x61um\x18\x02 \x02(\x01\x12\x18\n\x10outstandingUnits\x18\x03 \x02(\x03\x12\x15\n\rnetAssetValue\x18\x04 \x02(\x01\x12\x0f\n\x07premium\x18\x05 \x02(\x01\x12\x12\n\nassetClass\x18\x06 \x02(\x05\"\x8a\x08\n\x11SnapshotBasicData\x12&\n\x08security\x18\x01 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x0c\n\x04name\x18) \x01(\t\x12\x0c\n\x04type\x18\x02 \x02(\x05\x12\x11\n\tisSuspend\x18\x03 \x02(\x08\x12\x10\n\x08listTime\x18\x04 \x02(\t\x12\x0f\n\x07lotSize\x18\x05 \x02(\x05\x12\x13\n\x0bpriceSpread\x18\x06 \x02(\x01\x12\x12\n\nupdateTime\x18\x07 \x02(\t\x12\x11\n\thighPrice\x18\x08 \x02(\x01\x12\x11\n\topenPrice\x18\t \x02(\x01\x12\x10\n\x08lowPrice\x18\n \x02(\x01\x12\x16\n\x0elastClosePrice\x18\x0b \x02(\x01\x12\x10\n\x08\x63urPrice\x18\x0c \x02(\x01\x12\x0e\n\x06volume\x18\r \x02(\x03\x12\x10\n\x08turnover\x18\x0e \x02(\x01\x12\x14\n\x0cturnoverRate\x18\x0f \x02(\x01\x12\x15\n\rlistTimestamp\x18\x10 \x01(\x01\x12\x17\n\x0fupdateTimestamp\x18\x11 \x01(\x01\x12\x10\n\x08\x61skPrice\x18\x12 \x01(\x01\x12\x10\n\x08\x62idPrice\x18\x13 \x01(\x01\x12\x0e\n\x06\x61skVol\x18\x14 \x01(\x03\x12\x0e\n\x06\x62idVol\x18\x15 \x01(\x03\x12\x14\n\x0c\x65nableMargin\x18\x16 \x01(\x08\x12\x15\n\rmortgageRatio\x18\x17 \x01(\x01\x12\x1e\n\x16longMarginInitialRatio\x18\x18 \x01(\x01\x12\x17\n\x0f\x65nableShortSell\x18\x19 \x01(\x08\x12\x15\n\rshortSellRate\x18\x1a \x01(\x01\x12\x1c\n\x14shortAvailableVolume\x18\x1b \x01(\x03\x12\x1f\n\x17shortMarginInitialRatio\x18\x1c \x01(\x01\x12\x11\n\tamplitude\x18\x1d \x01(\x01\x12\x10\n\x08\x61vgPrice\x18\x1e \x01(\x01\x12\x13\n\x0b\x62idAskRatio\x18\x1f \x01(\x01\x12\x13\n\x0bvolumeRatio\x18  \x01(\x01\x12\x1b\n\x13highest52WeeksPrice\x18! \x01(\x01\x12\x1a\n\x12lowest52WeeksPrice\x18\" \x01(\x01\x12\x1b\n\x13highestHistoryPrice\x18# \x01(\x01\x12\x1a\n\x12lowestHistoryPrice\x18$ \x01(\x01\x12\x31\n\tpreMarket\x18% \x01(\x0b\x32\x1e.Qot_Common.PreAfterMarketData\x12\x33\n\x0b\x61\x66terMarket\x18& \x01(\x0b\x32\x1e.Qot_Common.PreAfterMarketData\x12\x11\n\tsecStatus\x18\' \x01(\x05\x12\x19\n\x11\x63losePrice5Minute\x18( \x01(\x01\x12\x31\n\tovernight\x18* \x01(\x0b\x32\x1e.Qot_Common.PreAfterMarketData\"\xa4\x04\n\x08Snapshot\x12\x39\n\x05\x62\x61sic\x18\x01 \x02(\x0b\x32*.Qot_GetSecuritySnapshot.SnapshotBasicData\x12\x43\n\x0c\x65quityExData\x18\x02 \x01(\x0b\x32-.Qot_GetSecuritySnapshot.EquitySnapshotExData\x12\x45\n\rwarrantExData\x18\x03 \x01(\x0b\x32..Qot_GetSecuritySnapshot.WarrantSnapshotExData\x12\x43\n\x0coptionExData\x18\x04 \x01(\x0b\x32-.Qot_GetSecuritySnapshot.OptionSnapshotExData\x12\x41\n\x0bindexExData\x18\x05 \x01(\x0b\x32,.Qot_GetSecuritySnapshot.IndexSnapshotExData\x12\x41\n\x0bplateExData\x18\x06 \x01(\x0b\x32,.Qot_GetSecuritySnapshot.PlateSnapshotExData\x12\x43\n\x0c\x66utureExData\x18\x07 \x01(\x0b\x32-.Qot_GetSecuritySnapshot.FutureSnapshotExData\x12\x41\n\x0btrustExData\x18\x08 \x01(\x0b\x32,.Qot_GetSecuritySnapshot.TrustSnapshotExData\">\n\x03S2C\x12\x37\n\x0csnapshotList\x18\x01 \x03(\x0b\x32!.Qot_GetSecuritySnapshot.Snapshot\"4\n\x07Request\x12)\n\x03\x63\x32s\x18\x01 \x02(\x0b\x32\x1c.Qot_GetSecuritySnapshot.C2S\"m\n\x08Response\x12\x15\n\x07retType\x18\x01 \x02(\x05:\x04-400\x12\x0e\n\x06retMsg\x18\x02 \x01(\t\x12\x0f\n\x07\x65rrCode\x18\x03 \x01(\x05\x12)\n\x03s2c\x18\x04 \x01(\x0b\x32\x1c.Qot_GetSecuritySnapshot.S2CBM\n\x13\x63om.futu.openapi.pbZ6github.com/futuopen/ftapi4go/pb/qotgetsecuritysnapshot')
  ,
  dependencies=[Common__pb2.DESCRIPTOR,Qot__Common__pb2.DESCRIPTOR,])




_C2S = _descriptor.Descriptor(
  name='C2S',
  full_name='Qot_GetSecuritySnapshot.C2S',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='securityList', full_name='Qot_GetSecuritySnapshot.C2S.securityList', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=90,
  serialized_end=139,
)


_EQUITYSNAPSHOTEXDATA = _descriptor.Descriptor(
  name='EquitySnapshotExData',
  full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='issuedShares', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.issuedShares', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='issuedMarketVal', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.issuedMarketVal', index=1,
      number=2, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='netAsset', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.netAsset', index=2,
      number=3, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='netProfit', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.netProfit', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='earningsPershare', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.earningsPershare', index=4,
      number=5, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outstandingShares', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.outstandingShares', index=5,
      number=6, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outstandingMarketVal', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.outstandingMarketVal', index=6,
      number=7, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='netAssetPershare', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.netAssetPershare', index=7,
      number=8, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='eyRate', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.eyRate', index=8,
      number=9, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='peRate', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.peRate', index=9,
      number=10, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pbRate', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.pbRate', index=10,
      number=11, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='peTTMRate', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.peTTMRate', index=11,
      number=12, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dividendTTM', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.dividendTTM', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dividendRatioTTM', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.dividendRatioTTM', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dividendLFY', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.dividendLFY', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dividendLFYRatio', full_name='Qot_GetSecuritySnapshot.EquitySnapshotExData.dividendLFYRatio', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=142,
  serialized_end=518,
)


_WARRANTSNAPSHOTEXDATA = _descriptor.Descriptor(
  name='WarrantSnapshotExData',
  full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='conversionRate', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.conversionRate', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warrantType', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.warrantType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strikePrice', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.strikePrice', index=2,
      number=3, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maturityTime', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.maturityTime', index=3,
      number=4, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endTradeTime', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.endTradeTime', index=4,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='owner', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.owner', index=5,
      number=6, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recoveryPrice', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.recoveryPrice', index=6,
      number=7, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='streetVolumn', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.streetVolumn', index=7,
      number=8, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='issueVolumn', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.issueVolumn', index=8,
      number=9, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='streetRate', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.streetRate', index=9,
      number=10, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delta', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.delta', index=10,
      number=11, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='impliedVolatility', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.impliedVolatility', index=11,
      number=12, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='premium', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.premium', index=12,
      number=13, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maturityTimestamp', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.maturityTimestamp', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endTradeTimestamp', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.endTradeTimestamp', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='leverage', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.leverage', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ipop', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.ipop', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='breakEvenPoint', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.breakEvenPoint', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conversionPrice', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.conversionPrice', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priceRecoveryRatio', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.priceRecoveryRatio', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='score', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.score', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upperStrikePrice', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.upperStrikePrice', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lowerStrikePrice', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.lowerStrikePrice', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inLinePriceStatus', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.inLinePriceStatus', index=23,
      number=24, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='issuerCode', full_name='Qot_GetSecuritySnapshot.WarrantSnapshotExData.issuerCode', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=521,
  serialized_end=1113,
)


_OPTIONSNAPSHOTEXDATA = _descriptor.Descriptor(
  name='OptionSnapshotExData',
  full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.type', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='owner', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.owner', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strikeTime', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.strikeTime', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strikePrice', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.strikePrice', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractSize', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.contractSize', index=4,
      number=5, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractSizeFloat', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.contractSizeFloat', index=5,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='openInterest', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.openInterest', index=6,
      number=6, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='impliedVolatility', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.impliedVolatility', index=7,
      number=7, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='premium', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.premium', index=8,
      number=8, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delta', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.delta', index=9,
      number=9, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gamma', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.gamma', index=10,
      number=10, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vega', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.vega', index=11,
      number=11, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.theta', index=12,
      number=12, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rho', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.rho', index=13,
      number=13, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strikeTimestamp', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.strikeTimestamp', index=14,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='indexOptionType', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.indexOptionType', index=15,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='netOpenInterest', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.netOpenInterest', index=16,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expiryDateDistance', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.expiryDateDistance', index=17,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractNominalValue', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.contractNominalValue', index=18,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ownerLotMultiplier', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.ownerLotMultiplier', index=19,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='optionAreaType', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.optionAreaType', index=20,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contractMultiplier', full_name='Qot_GetSecuritySnapshot.OptionSnapshotExData.contractMultiplier', index=21,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1116,
  serialized_end=1630,
)


_INDEXSNAPSHOTEXDATA = _descriptor.Descriptor(
  name='IndexSnapshotExData',
  full_name='Qot_GetSecuritySnapshot.IndexSnapshotExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='raiseCount', full_name='Qot_GetSecuritySnapshot.IndexSnapshotExData.raiseCount', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fallCount', full_name='Qot_GetSecuritySnapshot.IndexSnapshotExData.fallCount', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='equalCount', full_name='Qot_GetSecuritySnapshot.IndexSnapshotExData.equalCount', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1632,
  serialized_end=1712,
)


_PLATESNAPSHOTEXDATA = _descriptor.Descriptor(
  name='PlateSnapshotExData',
  full_name='Qot_GetSecuritySnapshot.PlateSnapshotExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='raiseCount', full_name='Qot_GetSecuritySnapshot.PlateSnapshotExData.raiseCount', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fallCount', full_name='Qot_GetSecuritySnapshot.PlateSnapshotExData.fallCount', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='equalCount', full_name='Qot_GetSecuritySnapshot.PlateSnapshotExData.equalCount', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1714,
  serialized_end=1794,
)


_FUTURESNAPSHOTEXDATA = _descriptor.Descriptor(
  name='FutureSnapshotExData',
  full_name='Qot_GetSecuritySnapshot.FutureSnapshotExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lastSettlePrice', full_name='Qot_GetSecuritySnapshot.FutureSnapshotExData.lastSettlePrice', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position', full_name='Qot_GetSecuritySnapshot.FutureSnapshotExData.position', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='positionChange', full_name='Qot_GetSecuritySnapshot.FutureSnapshotExData.positionChange', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastTradeTime', full_name='Qot_GetSecuritySnapshot.FutureSnapshotExData.lastTradeTime', index=3,
      number=4, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastTradeTimestamp', full_name='Qot_GetSecuritySnapshot.FutureSnapshotExData.lastTradeTimestamp', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isMainContract', full_name='Qot_GetSecuritySnapshot.FutureSnapshotExData.isMainContract', index=5,
      number=6, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1797,
  serialized_end=1961,
)


_TRUSTSNAPSHOTEXDATA = _descriptor.Descriptor(
  name='TrustSnapshotExData',
  full_name='Qot_GetSecuritySnapshot.TrustSnapshotExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dividendYield', full_name='Qot_GetSecuritySnapshot.TrustSnapshotExData.dividendYield', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='aum', full_name='Qot_GetSecuritySnapshot.TrustSnapshotExData.aum', index=1,
      number=2, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outstandingUnits', full_name='Qot_GetSecuritySnapshot.TrustSnapshotExData.outstandingUnits', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='netAssetValue', full_name='Qot_GetSecuritySnapshot.TrustSnapshotExData.netAssetValue', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='premium', full_name='Qot_GetSecuritySnapshot.TrustSnapshotExData.premium', index=4,
      number=5, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='assetClass', full_name='Qot_GetSecuritySnapshot.TrustSnapshotExData.assetClass', index=5,
      number=6, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1964,
  serialized_end=2107,
)


_SNAPSHOTBASICDATA = _descriptor.Descriptor(
  name='SnapshotBasicData',
  full_name='Qot_GetSecuritySnapshot.SnapshotBasicData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='security', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.security', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.name', index=1,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.type', index=2,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isSuspend', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.isSuspend', index=3,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listTime', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.listTime', index=4,
      number=4, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lotSize', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.lotSize', index=5,
      number=5, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priceSpread', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.priceSpread', index=6,
      number=6, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updateTime', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.updateTime', index=7,
      number=7, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='highPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.highPrice', index=8,
      number=8, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='openPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.openPrice', index=9,
      number=9, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lowPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.lowPrice', index=10,
      number=10, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastClosePrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.lastClosePrice', index=11,
      number=11, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='curPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.curPrice', index=12,
      number=12, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volume', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.volume', index=13,
      number=13, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turnover', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.turnover', index=14,
      number=14, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='turnoverRate', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.turnoverRate', index=15,
      number=15, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listTimestamp', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.listTimestamp', index=16,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updateTimestamp', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.updateTimestamp', index=17,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='askPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.askPrice', index=18,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bidPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.bidPrice', index=19,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='askVol', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.askVol', index=20,
      number=20, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bidVol', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.bidVol', index=21,
      number=21, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='enableMargin', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.enableMargin', index=22,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mortgageRatio', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.mortgageRatio', index=23,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='longMarginInitialRatio', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.longMarginInitialRatio', index=24,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='enableShortSell', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.enableShortSell', index=25,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shortSellRate', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.shortSellRate', index=26,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shortAvailableVolume', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.shortAvailableVolume', index=27,
      number=27, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shortMarginInitialRatio', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.shortMarginInitialRatio', index=28,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amplitude', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.amplitude', index=29,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='avgPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.avgPrice', index=30,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bidAskRatio', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.bidAskRatio', index=31,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='volumeRatio', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.volumeRatio', index=32,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='highest52WeeksPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.highest52WeeksPrice', index=33,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lowest52WeeksPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.lowest52WeeksPrice', index=34,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='highestHistoryPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.highestHistoryPrice', index=35,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lowestHistoryPrice', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.lowestHistoryPrice', index=36,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='preMarket', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.preMarket', index=37,
      number=37, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='afterMarket', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.afterMarket', index=38,
      number=38, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='secStatus', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.secStatus', index=39,
      number=39, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='closePrice5Minute', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.closePrice5Minute', index=40,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='overnight', full_name='Qot_GetSecuritySnapshot.SnapshotBasicData.overnight', index=41,
      number=42, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2110,
  serialized_end=3144,
)


_SNAPSHOT = _descriptor.Descriptor(
  name='Snapshot',
  full_name='Qot_GetSecuritySnapshot.Snapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='basic', full_name='Qot_GetSecuritySnapshot.Snapshot.basic', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='equityExData', full_name='Qot_GetSecuritySnapshot.Snapshot.equityExData', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warrantExData', full_name='Qot_GetSecuritySnapshot.Snapshot.warrantExData', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='optionExData', full_name='Qot_GetSecuritySnapshot.Snapshot.optionExData', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='indexExData', full_name='Qot_GetSecuritySnapshot.Snapshot.indexExData', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plateExData', full_name='Qot_GetSecuritySnapshot.Snapshot.plateExData', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='futureExData', full_name='Qot_GetSecuritySnapshot.Snapshot.futureExData', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trustExData', full_name='Qot_GetSecuritySnapshot.Snapshot.trustExData', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3147,
  serialized_end=3695,
)


_S2C = _descriptor.Descriptor(
  name='S2C',
  full_name='Qot_GetSecuritySnapshot.S2C',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='snapshotList', full_name='Qot_GetSecuritySnapshot.S2C.snapshotList', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3697,
  serialized_end=3759,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='Qot_GetSecuritySnapshot.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='c2s', full_name='Qot_GetSecuritySnapshot.Request.c2s', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3761,
  serialized_end=3813,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Qot_GetSecuritySnapshot.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='retType', full_name='Qot_GetSecuritySnapshot.Response.retType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=True, default_value=-400,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retMsg', full_name='Qot_GetSecuritySnapshot.Response.retMsg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errCode', full_name='Qot_GetSecuritySnapshot.Response.errCode', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s2c', full_name='Qot_GetSecuritySnapshot.Response.s2c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3815,
  serialized_end=3924,
)

_C2S.fields_by_name['securityList'].message_type = Qot__Common__pb2._SECURITY
_WARRANTSNAPSHOTEXDATA.fields_by_name['owner'].message_type = Qot__Common__pb2._SECURITY
_OPTIONSNAPSHOTEXDATA.fields_by_name['owner'].message_type = Qot__Common__pb2._SECURITY
_SNAPSHOTBASICDATA.fields_by_name['security'].message_type = Qot__Common__pb2._SECURITY
_SNAPSHOTBASICDATA.fields_by_name['preMarket'].message_type = Qot__Common__pb2._PREAFTERMARKETDATA
_SNAPSHOTBASICDATA.fields_by_name['afterMarket'].message_type = Qot__Common__pb2._PREAFTERMARKETDATA
_SNAPSHOTBASICDATA.fields_by_name['overnight'].message_type = Qot__Common__pb2._PREAFTERMARKETDATA
_SNAPSHOT.fields_by_name['basic'].message_type = _SNAPSHOTBASICDATA
_SNAPSHOT.fields_by_name['equityExData'].message_type = _EQUITYSNAPSHOTEXDATA
_SNAPSHOT.fields_by_name['warrantExData'].message_type = _WARRANTSNAPSHOTEXDATA
_SNAPSHOT.fields_by_name['optionExData'].message_type = _OPTIONSNAPSHOTEXDATA
_SNAPSHOT.fields_by_name['indexExData'].message_type = _INDEXSNAPSHOTEXDATA
_SNAPSHOT.fields_by_name['plateExData'].message_type = _PLATESNAPSHOTEXDATA
_SNAPSHOT.fields_by_name['futureExData'].message_type = _FUTURESNAPSHOTEXDATA
_SNAPSHOT.fields_by_name['trustExData'].message_type = _TRUSTSNAPSHOTEXDATA
_S2C.fields_by_name['snapshotList'].message_type = _SNAPSHOT
_REQUEST.fields_by_name['c2s'].message_type = _C2S
_RESPONSE.fields_by_name['s2c'].message_type = _S2C
DESCRIPTOR.message_types_by_name['C2S'] = _C2S
DESCRIPTOR.message_types_by_name['EquitySnapshotExData'] = _EQUITYSNAPSHOTEXDATA
DESCRIPTOR.message_types_by_name['WarrantSnapshotExData'] = _WARRANTSNAPSHOTEXDATA
DESCRIPTOR.message_types_by_name['OptionSnapshotExData'] = _OPTIONSNAPSHOTEXDATA
DESCRIPTOR.message_types_by_name['IndexSnapshotExData'] = _INDEXSNAPSHOTEXDATA
DESCRIPTOR.message_types_by_name['PlateSnapshotExData'] = _PLATESNAPSHOTEXDATA
DESCRIPTOR.message_types_by_name['FutureSnapshotExData'] = _FUTURESNAPSHOTEXDATA
DESCRIPTOR.message_types_by_name['TrustSnapshotExData'] = _TRUSTSNAPSHOTEXDATA
DESCRIPTOR.message_types_by_name['SnapshotBasicData'] = _SNAPSHOTBASICDATA
DESCRIPTOR.message_types_by_name['Snapshot'] = _SNAPSHOT
DESCRIPTOR.message_types_by_name['S2C'] = _S2C
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

C2S = _reflection.GeneratedProtocolMessageType('C2S', (_message.Message,), dict(
  DESCRIPTOR = _C2S,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.C2S)
  ))
_sym_db.RegisterMessage(C2S)

EquitySnapshotExData = _reflection.GeneratedProtocolMessageType('EquitySnapshotExData', (_message.Message,), dict(
  DESCRIPTOR = _EQUITYSNAPSHOTEXDATA,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.EquitySnapshotExData)
  ))
_sym_db.RegisterMessage(EquitySnapshotExData)

WarrantSnapshotExData = _reflection.GeneratedProtocolMessageType('WarrantSnapshotExData', (_message.Message,), dict(
  DESCRIPTOR = _WARRANTSNAPSHOTEXDATA,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.WarrantSnapshotExData)
  ))
_sym_db.RegisterMessage(WarrantSnapshotExData)

OptionSnapshotExData = _reflection.GeneratedProtocolMessageType('OptionSnapshotExData', (_message.Message,), dict(
  DESCRIPTOR = _OPTIONSNAPSHOTEXDATA,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.OptionSnapshotExData)
  ))
_sym_db.RegisterMessage(OptionSnapshotExData)

IndexSnapshotExData = _reflection.GeneratedProtocolMessageType('IndexSnapshotExData', (_message.Message,), dict(
  DESCRIPTOR = _INDEXSNAPSHOTEXDATA,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.IndexSnapshotExData)
  ))
_sym_db.RegisterMessage(IndexSnapshotExData)

PlateSnapshotExData = _reflection.GeneratedProtocolMessageType('PlateSnapshotExData', (_message.Message,), dict(
  DESCRIPTOR = _PLATESNAPSHOTEXDATA,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.PlateSnapshotExData)
  ))
_sym_db.RegisterMessage(PlateSnapshotExData)

FutureSnapshotExData = _reflection.GeneratedProtocolMessageType('FutureSnapshotExData', (_message.Message,), dict(
  DESCRIPTOR = _FUTURESNAPSHOTEXDATA,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.FutureSnapshotExData)
  ))
_sym_db.RegisterMessage(FutureSnapshotExData)

TrustSnapshotExData = _reflection.GeneratedProtocolMessageType('TrustSnapshotExData', (_message.Message,), dict(
  DESCRIPTOR = _TRUSTSNAPSHOTEXDATA,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.TrustSnapshotExData)
  ))
_sym_db.RegisterMessage(TrustSnapshotExData)

SnapshotBasicData = _reflection.GeneratedProtocolMessageType('SnapshotBasicData', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTBASICDATA,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.SnapshotBasicData)
  ))
_sym_db.RegisterMessage(SnapshotBasicData)

Snapshot = _reflection.GeneratedProtocolMessageType('Snapshot', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOT,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.Snapshot)
  ))
_sym_db.RegisterMessage(Snapshot)

S2C = _reflection.GeneratedProtocolMessageType('S2C', (_message.Message,), dict(
  DESCRIPTOR = _S2C,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.S2C)
  ))
_sym_db.RegisterMessage(S2C)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'Qot_GetSecuritySnapshot_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetSecuritySnapshot.Response)
  ))
_sym_db.RegisterMessage(Response)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ6github.com/futuopen/ftapi4go/pb/qotgetsecuritysnapshot'))
# @@protoc_insertion_point(module_scope)
