#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恆生指數期貨實時價格監控程式
使用 FUTU API 獲取實時行情數據

作者: AI Assistant
日期: 2025-01-14
"""

import time
import datetime
from futu import *

class HSIFuturesMonitor:
    """恆生指數期貨實時監控類"""
    
    def __init__(self, host='127.0.0.1', port=11111):
        """
        初始化監控器
        
        Args:
            host (str): OpenD 服務器地址
            port (int): OpenD 服務器端口
        """
        self.host = host
        self.port = port
        self.quote_ctx = None
        self.hsi_code = "HK.HSImain"  # 恆生指數期貨主連代碼
        self.is_running = False
        
    def connect(self):
        """連接到 FUTU OpenD"""
        try:
            self.quote_ctx = OpenQuoteContext(host=self.host, port=self.port)
            print(f"✅ 成功連接到 FUTU OpenD ({self.host}:{self.port})")
            return True
        except Exception as e:
            print(f"❌ 連接失敗: {e}")
            return False
    
    def subscribe_hsi_futures(self):
        """訂閱恆生指數期貨實時行情"""
        try:
            # 訂閱實時報價
            ret_sub, err_message = self.quote_ctx.subscribe(
                [self.hsi_code], 
                [SubType.QUOTE], 
                subscribe_push=True  # 啟用推送
            )
            
            if ret_sub == RET_OK:
                print(f"✅ 成功訂閱 {self.hsi_code} 實時行情")
                
                # 設置實時報價回調
                self.quote_ctx.set_handler(QuoteHandlerBase())
                self.quote_ctx.set_handler(StockQuoteHandler())
                
                return True
            else:
                print(f"❌ 訂閱失敗: {err_message}")
                return False
                
        except Exception as e:
            print(f"❌ 訂閱過程發生錯誤: {e}")
            return False
    
    def get_current_price(self):
        """獲取當前價格"""
        try:
            ret, data = self.quote_ctx.get_stock_quote([self.hsi_code])
            if ret == RET_OK and not data.empty:
                return data.iloc[0]
            else:
                print(f"❌ 獲取價格失敗: {data}")
                return None
        except Exception as e:
            print(f"❌ 獲取價格時發生錯誤: {e}")
            return None
    
    def display_price_info(self, quote_data):
        """顯示價格信息"""
        if quote_data is None:
            return
            
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print("\n" + "="*60)
        print(f"📊 恆生指數期貨實時行情 - {current_time}")
        print("="*60)
        print(f"代碼: {quote_data['code']}")
        print(f"名稱: {quote_data['name']}")
        print(f"最新價: {quote_data['last_price']:.1f}")
        print(f"開盤價: {quote_data['open_price']:.1f}")
        print(f"最高價: {quote_data['high_price']:.1f}")
        print(f"最低價: {quote_data['low_price']:.1f}")
        print(f"昨收價: {quote_data['prev_close_price']:.1f}")
        
        # 計算漲跌
        change = quote_data['last_price'] - quote_data['prev_close_price']
        change_percent = (change / quote_data['prev_close_price']) * 100
        
        change_symbol = "📈" if change > 0 else "📉" if change < 0 else "➡️"
        print(f"漲跌額: {change_symbol} {change:+.1f}")
        print(f"漲跌幅: {change_symbol} {change_percent:+.2f}%")
        
        print(f"成交量: {quote_data['volume']:,}")
        print(f"成交額: {quote_data['turnover']:,.0f}")
        print(f"數據時間: {quote_data['data_time']}")
        print("="*60)
    
    def start_monitoring(self, refresh_interval=5):
        """
        開始監控
        
        Args:
            refresh_interval (int): 刷新間隔（秒）
        """
        if not self.connect():
            return
            
        if not self.subscribe_hsi_futures():
            return
            
        self.is_running = True
        print(f"\n🚀 開始監控恆生指數期貨，每 {refresh_interval} 秒刷新一次")
        print("按 Ctrl+C 停止監控\n")
        
        try:
            while self.is_running:
                quote_data = self.get_current_price()
                self.display_price_info(quote_data)
                time.sleep(refresh_interval)
                
        except KeyboardInterrupt:
            print("\n\n⏹️  用戶中斷，停止監控...")
        except Exception as e:
            print(f"\n❌ 監控過程中發生錯誤: {e}")
        finally:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止監控"""
        self.is_running = False
        if self.quote_ctx:
            try:
                # 取消訂閱
                self.quote_ctx.unsubscribe([self.hsi_code], [SubType.QUOTE])
                self.quote_ctx.close()
                print("✅ 已斷開連接並清理資源")
            except Exception as e:
                print(f"⚠️  清理資源時發生錯誤: {e}")


class StockQuoteHandler(StockQuoteHandlerBase):
    """實時報價推送處理器"""
    
    def on_recv_rsp(self, rsp_pb):
        """接收實時報價推送"""
        ret_code, data = super(StockQuoteHandler, self).on_recv_rsp(rsp_pb)
        if ret_code != RET_OK:
            print(f"❌ 推送數據錯誤: {data}")
            return RET_ERROR, data
            
        # 處理推送的實時數據
        for index, row in data.iterrows():
            if row['code'] == 'HK.HSImain':
                current_time = datetime.datetime.now().strftime("%H:%M:%S")
                change = row['last_price'] - row['prev_close_price']
                change_percent = (change / row['prev_close_price']) * 100
                change_symbol = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                
                print(f"\n🔔 [{current_time}] 實時推送更新:")
                print(f"   恆指期貨: {row['last_price']:.1f} ({change_symbol}{change:+.1f}, {change_percent:+.2f}%)")
                
        return RET_OK, data


def main():
    """主函數"""
    print("🎯 恆生指數期貨實時監控程式")
    print("=" * 50)
    print("📋 程式信息:")
    print("   - 數據來源: FUTU API")
    print("   - 監控標的: 恆生指數期貨主連 (HK.HSImain)")
    print("   - 更新頻率: 每5秒")
    print("   - 實時推送: 啟用")
    print("=" * 50)
    
    # 檢查 OpenD 連接配置
    print("\n🔧 連接配置:")
    print("   - OpenD 地址: 127.0.0.1:11111")
    print("   - 請確保 FUTU OpenD 已啟動並登錄")
    print("   - 請確保已有期貨 LV1 權限")
    
    input("\n按 Enter 鍵開始監控...")
    
    # 創建監控器並開始監控
    monitor = HSIFuturesMonitor()
    monitor.start_monitoring(refresh_interval=5)


if __name__ == "__main__":
    main()
