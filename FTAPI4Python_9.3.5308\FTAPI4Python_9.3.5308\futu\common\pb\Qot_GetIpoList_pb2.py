# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Qot_GetIpoList.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import Common_pb2 as Common__pb2
import Qot_Common_pb2 as Qot__Common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='Qot_GetIpoList.proto',
  package='Qot_GetIpoList',
  syntax='proto2',
  serialized_pb=_b('\n\x14Qot_GetIpoList.proto\x12\x0eQot_GetIpoList\x1a\x0c\x43ommon.proto\x1a\x10Qot_Common.proto\"m\n\x0c\x42\x61sicIpoData\x12&\n\x08security\x18\x01 \x02(\x0b\x32\x14.Qot_Common.Security\x12\x0c\n\x04name\x18\x02 \x02(\t\x12\x10\n\x08listTime\x18\x03 \x01(\t\x12\x15\n\rlistTimestamp\x18\x04 \x01(\x01\"\xb9\x03\n\x0b\x43NIpoExData\x12\x11\n\tapplyCode\x18\x01 \x02(\t\x12\x11\n\tissueSize\x18\x02 \x02(\x03\x12\x17\n\x0fonlineIssueSize\x18\x03 \x02(\x03\x12\x17\n\x0f\x61pplyUpperLimit\x18\x04 \x02(\x03\x12\x1d\n\x15\x61pplyLimitMarketValue\x18\x05 \x02(\x03\x12\x1a\n\x12isEstimateIpoPrice\x18\x06 \x02(\x08\x12\x10\n\x08ipoPrice\x18\x07 \x02(\x01\x12\x16\n\x0eindustryPeRate\x18\x08 \x02(\x01\x12\x1e\n\x16isEstimateWinningRatio\x18\t \x02(\x08\x12\x14\n\x0cwinningRatio\x18\n \x02(\x01\x12\x13\n\x0bissuePeRate\x18\x0b \x02(\x01\x12\x11\n\tapplyTime\x18\x0c \x01(\t\x12\x16\n\x0e\x61pplyTimestamp\x18\r \x01(\x01\x12\x13\n\x0bwinningTime\x18\x0e \x01(\t\x12\x18\n\x10winningTimestamp\x18\x0f \x01(\x01\x12\x10\n\x08isHasWon\x18\x10 \x02(\x08\x12\x36\n\x0ewinningNumData\x18\x11 \x03(\x0b\x32\x1e.Qot_GetIpoList.WinningNumData\":\n\x0eWinningNumData\x12\x13\n\x0bwinningName\x18\x01 \x02(\t\x12\x13\n\x0bwinningInfo\x18\x02 \x02(\t\"\xbe\x01\n\x0bHKIpoExData\x12\x13\n\x0bipoPriceMin\x18\x01 \x02(\x01\x12\x13\n\x0bipoPriceMax\x18\x02 \x02(\x01\x12\x11\n\tlistPrice\x18\x03 \x02(\x01\x12\x0f\n\x07lotSize\x18\x04 \x02(\x05\x12\x15\n\rentrancePrice\x18\x05 \x02(\x01\x12\x19\n\x11isSubscribeStatus\x18\x06 \x02(\x08\x12\x14\n\x0c\x61pplyEndTime\x18\x07 \x01(\t\x12\x19\n\x11\x61pplyEndTimestamp\x18\x08 \x01(\x01\"J\n\x0bUSIpoExData\x12\x13\n\x0bipoPriceMin\x18\x01 \x02(\x01\x12\x13\n\x0bipoPriceMax\x18\x02 \x02(\x01\x12\x11\n\tissueSize\x18\x03 \x02(\x03\"\xc3\x01\n\x07IpoData\x12+\n\x05\x62\x61sic\x18\x01 \x02(\x0b\x32\x1c.Qot_GetIpoList.BasicIpoData\x12-\n\x08\x63nExData\x18\x02 \x01(\x0b\x32\x1b.Qot_GetIpoList.CNIpoExData\x12-\n\x08hkExData\x18\x03 \x01(\x0b\x32\x1b.Qot_GetIpoList.HKIpoExData\x12-\n\x08usExData\x18\x04 \x01(\x0b\x32\x1b.Qot_GetIpoList.USIpoExData\"\x15\n\x03\x43\x32S\x12\x0e\n\x06market\x18\x01 \x02(\x05\"/\n\x03S2C\x12(\n\x07ipoList\x18\x01 \x03(\x0b\x32\x17.Qot_GetIpoList.IpoData\"+\n\x07Request\x12 \n\x03\x63\x32s\x18\x01 \x02(\x0b\x32\x13.Qot_GetIpoList.C2S\"d\n\x08Response\x12\x15\n\x07retType\x18\x01 \x02(\x05:\x04-400\x12\x0e\n\x06retMsg\x18\x02 \x01(\t\x12\x0f\n\x07\x65rrCode\x18\x03 \x01(\x05\x12 \n\x03s2c\x18\x04 \x01(\x0b\x32\x13.Qot_GetIpoList.S2CBD\n\x13\x63om.futu.openapi.pbZ-github.com/futuopen/ftapi4go/pb/qotgetipolist')
  ,
  dependencies=[Common__pb2.DESCRIPTOR,Qot__Common__pb2.DESCRIPTOR,])




_BASICIPODATA = _descriptor.Descriptor(
  name='BasicIpoData',
  full_name='Qot_GetIpoList.BasicIpoData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='security', full_name='Qot_GetIpoList.BasicIpoData.security', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Qot_GetIpoList.BasicIpoData.name', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listTime', full_name='Qot_GetIpoList.BasicIpoData.listTime', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listTimestamp', full_name='Qot_GetIpoList.BasicIpoData.listTimestamp', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=72,
  serialized_end=181,
)


_CNIPOEXDATA = _descriptor.Descriptor(
  name='CNIpoExData',
  full_name='Qot_GetIpoList.CNIpoExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='applyCode', full_name='Qot_GetIpoList.CNIpoExData.applyCode', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='issueSize', full_name='Qot_GetIpoList.CNIpoExData.issueSize', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='onlineIssueSize', full_name='Qot_GetIpoList.CNIpoExData.onlineIssueSize', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='applyUpperLimit', full_name='Qot_GetIpoList.CNIpoExData.applyUpperLimit', index=3,
      number=4, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='applyLimitMarketValue', full_name='Qot_GetIpoList.CNIpoExData.applyLimitMarketValue', index=4,
      number=5, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isEstimateIpoPrice', full_name='Qot_GetIpoList.CNIpoExData.isEstimateIpoPrice', index=5,
      number=6, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ipoPrice', full_name='Qot_GetIpoList.CNIpoExData.ipoPrice', index=6,
      number=7, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='industryPeRate', full_name='Qot_GetIpoList.CNIpoExData.industryPeRate', index=7,
      number=8, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isEstimateWinningRatio', full_name='Qot_GetIpoList.CNIpoExData.isEstimateWinningRatio', index=8,
      number=9, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='winningRatio', full_name='Qot_GetIpoList.CNIpoExData.winningRatio', index=9,
      number=10, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='issuePeRate', full_name='Qot_GetIpoList.CNIpoExData.issuePeRate', index=10,
      number=11, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='applyTime', full_name='Qot_GetIpoList.CNIpoExData.applyTime', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='applyTimestamp', full_name='Qot_GetIpoList.CNIpoExData.applyTimestamp', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='winningTime', full_name='Qot_GetIpoList.CNIpoExData.winningTime', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='winningTimestamp', full_name='Qot_GetIpoList.CNIpoExData.winningTimestamp', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isHasWon', full_name='Qot_GetIpoList.CNIpoExData.isHasWon', index=15,
      number=16, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='winningNumData', full_name='Qot_GetIpoList.CNIpoExData.winningNumData', index=16,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=184,
  serialized_end=625,
)


_WINNINGNUMDATA = _descriptor.Descriptor(
  name='WinningNumData',
  full_name='Qot_GetIpoList.WinningNumData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='winningName', full_name='Qot_GetIpoList.WinningNumData.winningName', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='winningInfo', full_name='Qot_GetIpoList.WinningNumData.winningInfo', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=627,
  serialized_end=685,
)


_HKIPOEXDATA = _descriptor.Descriptor(
  name='HKIpoExData',
  full_name='Qot_GetIpoList.HKIpoExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ipoPriceMin', full_name='Qot_GetIpoList.HKIpoExData.ipoPriceMin', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ipoPriceMax', full_name='Qot_GetIpoList.HKIpoExData.ipoPriceMax', index=1,
      number=2, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='listPrice', full_name='Qot_GetIpoList.HKIpoExData.listPrice', index=2,
      number=3, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lotSize', full_name='Qot_GetIpoList.HKIpoExData.lotSize', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='entrancePrice', full_name='Qot_GetIpoList.HKIpoExData.entrancePrice', index=4,
      number=5, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isSubscribeStatus', full_name='Qot_GetIpoList.HKIpoExData.isSubscribeStatus', index=5,
      number=6, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='applyEndTime', full_name='Qot_GetIpoList.HKIpoExData.applyEndTime', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='applyEndTimestamp', full_name='Qot_GetIpoList.HKIpoExData.applyEndTimestamp', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=688,
  serialized_end=878,
)


_USIPOEXDATA = _descriptor.Descriptor(
  name='USIpoExData',
  full_name='Qot_GetIpoList.USIpoExData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ipoPriceMin', full_name='Qot_GetIpoList.USIpoExData.ipoPriceMin', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ipoPriceMax', full_name='Qot_GetIpoList.USIpoExData.ipoPriceMax', index=1,
      number=2, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='issueSize', full_name='Qot_GetIpoList.USIpoExData.issueSize', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=880,
  serialized_end=954,
)


_IPODATA = _descriptor.Descriptor(
  name='IpoData',
  full_name='Qot_GetIpoList.IpoData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='basic', full_name='Qot_GetIpoList.IpoData.basic', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cnExData', full_name='Qot_GetIpoList.IpoData.cnExData', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hkExData', full_name='Qot_GetIpoList.IpoData.hkExData', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='usExData', full_name='Qot_GetIpoList.IpoData.usExData', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=957,
  serialized_end=1152,
)


_C2S = _descriptor.Descriptor(
  name='C2S',
  full_name='Qot_GetIpoList.C2S',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='market', full_name='Qot_GetIpoList.C2S.market', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1154,
  serialized_end=1175,
)


_S2C = _descriptor.Descriptor(
  name='S2C',
  full_name='Qot_GetIpoList.S2C',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ipoList', full_name='Qot_GetIpoList.S2C.ipoList', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1177,
  serialized_end=1224,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='Qot_GetIpoList.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='c2s', full_name='Qot_GetIpoList.Request.c2s', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1226,
  serialized_end=1269,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Qot_GetIpoList.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='retType', full_name='Qot_GetIpoList.Response.retType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=True, default_value=-400,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retMsg', full_name='Qot_GetIpoList.Response.retMsg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errCode', full_name='Qot_GetIpoList.Response.errCode', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s2c', full_name='Qot_GetIpoList.Response.s2c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1271,
  serialized_end=1371,
)

_BASICIPODATA.fields_by_name['security'].message_type = Qot__Common__pb2._SECURITY
_CNIPOEXDATA.fields_by_name['winningNumData'].message_type = _WINNINGNUMDATA
_IPODATA.fields_by_name['basic'].message_type = _BASICIPODATA
_IPODATA.fields_by_name['cnExData'].message_type = _CNIPOEXDATA
_IPODATA.fields_by_name['hkExData'].message_type = _HKIPOEXDATA
_IPODATA.fields_by_name['usExData'].message_type = _USIPOEXDATA
_S2C.fields_by_name['ipoList'].message_type = _IPODATA
_REQUEST.fields_by_name['c2s'].message_type = _C2S
_RESPONSE.fields_by_name['s2c'].message_type = _S2C
DESCRIPTOR.message_types_by_name['BasicIpoData'] = _BASICIPODATA
DESCRIPTOR.message_types_by_name['CNIpoExData'] = _CNIPOEXDATA
DESCRIPTOR.message_types_by_name['WinningNumData'] = _WINNINGNUMDATA
DESCRIPTOR.message_types_by_name['HKIpoExData'] = _HKIPOEXDATA
DESCRIPTOR.message_types_by_name['USIpoExData'] = _USIPOEXDATA
DESCRIPTOR.message_types_by_name['IpoData'] = _IPODATA
DESCRIPTOR.message_types_by_name['C2S'] = _C2S
DESCRIPTOR.message_types_by_name['S2C'] = _S2C
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

BasicIpoData = _reflection.GeneratedProtocolMessageType('BasicIpoData', (_message.Message,), dict(
  DESCRIPTOR = _BASICIPODATA,
  __module__ = 'Qot_GetIpoList_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetIpoList.BasicIpoData)
  ))
_sym_db.RegisterMessage(BasicIpoData)

CNIpoExData = _reflection.GeneratedProtocolMessageType('CNIpoExData', (_message.Message,), dict(
  DESCRIPTOR = _CNIPOEXDATA,
  __module__ = 'Qot_GetIpoList_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetIpoList.CNIpoExData)
  ))
_sym_db.RegisterMessage(CNIpoExData)

WinningNumData = _reflection.GeneratedProtocolMessageType('WinningNumData', (_message.Message,), dict(
  DESCRIPTOR = _WINNINGNUMDATA,
  __module__ = 'Qot_GetIpoList_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetIpoList.WinningNumData)
  ))
_sym_db.RegisterMessage(WinningNumData)

HKIpoExData = _reflection.GeneratedProtocolMessageType('HKIpoExData', (_message.Message,), dict(
  DESCRIPTOR = _HKIPOEXDATA,
  __module__ = 'Qot_GetIpoList_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetIpoList.HKIpoExData)
  ))
_sym_db.RegisterMessage(HKIpoExData)

USIpoExData = _reflection.GeneratedProtocolMessageType('USIpoExData', (_message.Message,), dict(
  DESCRIPTOR = _USIPOEXDATA,
  __module__ = 'Qot_GetIpoList_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetIpoList.USIpoExData)
  ))
_sym_db.RegisterMessage(USIpoExData)

IpoData = _reflection.GeneratedProtocolMessageType('IpoData', (_message.Message,), dict(
  DESCRIPTOR = _IPODATA,
  __module__ = 'Qot_GetIpoList_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetIpoList.IpoData)
  ))
_sym_db.RegisterMessage(IpoData)

C2S = _reflection.GeneratedProtocolMessageType('C2S', (_message.Message,), dict(
  DESCRIPTOR = _C2S,
  __module__ = 'Qot_GetIpoList_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetIpoList.C2S)
  ))
_sym_db.RegisterMessage(C2S)

S2C = _reflection.GeneratedProtocolMessageType('S2C', (_message.Message,), dict(
  DESCRIPTOR = _S2C,
  __module__ = 'Qot_GetIpoList_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetIpoList.S2C)
  ))
_sym_db.RegisterMessage(S2C)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'Qot_GetIpoList_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetIpoList.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'Qot_GetIpoList_pb2'
  # @@protoc_insertion_point(class_scope:Qot_GetIpoList.Response)
  ))
_sym_db.RegisterMessage(Response)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023com.futu.openapi.pbZ-github.com/futuopen/ftapi4go/pb/qotgetipolist'))
# @@protoc_insertion_point(module_scope)
