{"files": [{"name": "Common.proto", "description": "", "package": "Common", "hasEnums": true, "hasExtensions": false, "hasMessages": true, "hasServices": false, "enums": [{"name": "PacketEncAlgo", "longName": "PacketEncAlgo", "fullName": "Common.PacketEncAlgo", "description": "包加密算法", "values": [{"name": "PacketEncAlgo_FTAES_ECB", "number": "0", "description": "富途修改过的AES加密的ECB模式"}, {"name": "PacketEncAlgo_None", "number": "-1", "description": "不加密"}, {"name": "PacketEncAlgo_AES_ECB", "number": "1", "description": "标准的AES加密的ECB模式"}]}, {"name": "RetType", "longName": "RetType", "fullName": "Common.RetType", "description": "返回结果", "values": [{"name": "RetType_Succeed", "number": "0", "description": "成功"}, {"name": "RetType_Failed", "number": "-1", "description": "失败"}, {"name": "RetType_TimeOut", "number": "-100", "description": "超时"}, {"name": "RetType_Unknown", "number": "-400", "description": "未知结果"}]}], "extensions": [], "messages": [{"name": "PacketID", "longName": "PacketID", "fullName": "Common.PacketID", "description": "包的唯一标识，用于回放攻击的识别和保护", "hasExtensions": false, "hasFields": true, "extensions": [], "fields": [{"name": "connID", "description": "当前TCP连接的连接ID，一条连接的唯一标识，InitConnect协议会返回", "label": "required", "type": "uint64", "longType": "uint64", "fullType": "uint64", "ismap": false, "defaultValue": ""}, {"name": "serialNo", "description": "包头中的包自增序列号", "label": "required", "type": "uint32", "longType": "uint32", "fullType": "uint32", "ismap": false, "defaultValue": ""}]}], "services": []}], "scalarValueTypes": [{"protoType": "double", "notes": "", "cppType": "double", "csType": "double", "goType": "float64", "javaType": "double", "phpType": "float", "pythonType": "float", "rubyType": "Float"}, {"protoType": "float", "notes": "", "cppType": "float", "csType": "float", "goType": "float32", "javaType": "float", "phpType": "float", "pythonType": "float", "rubyType": "Float"}, {"protoType": "int32", "notes": "Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint32 instead.", "cppType": "int32", "csType": "int", "goType": "int32", "javaType": "int", "phpType": "integer", "pythonType": "int", "rubyType": "Bignum or Fixnum (as required)"}, {"protoType": "int64", "notes": "Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint64 instead.", "cppType": "int64", "csType": "long", "goType": "int64", "javaType": "long", "phpType": "integer/string", "pythonType": "int/long", "rubyType": "<PERSON><PERSON>"}, {"protoType": "uint32", "notes": "Uses variable-length encoding.", "cppType": "uint32", "csType": "uint", "goType": "uint32", "javaType": "int", "phpType": "integer", "pythonType": "int/long", "rubyType": "Bignum or Fixnum (as required)"}, {"protoType": "uint64", "notes": "Uses variable-length encoding.", "cppType": "uint64", "csType": "<PERSON><PERSON>", "goType": "uint64", "javaType": "long", "phpType": "integer/string", "pythonType": "int/long", "rubyType": "Bignum or Fixnum (as required)"}, {"protoType": "sint32", "notes": "Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int32s.", "cppType": "int32", "csType": "int", "goType": "int32", "javaType": "int", "phpType": "integer", "pythonType": "int", "rubyType": "Bignum or Fixnum (as required)"}, {"protoType": "sint64", "notes": "Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int64s.", "cppType": "int64", "csType": "long", "goType": "int64", "javaType": "long", "phpType": "integer/string", "pythonType": "int/long", "rubyType": "<PERSON><PERSON>"}, {"protoType": "fixed32", "notes": "Always four bytes. More efficient than uint32 if values are often greater than 2^28.", "cppType": "uint32", "csType": "uint", "goType": "uint32", "javaType": "int", "phpType": "integer", "pythonType": "int", "rubyType": "Bignum or Fixnum (as required)"}, {"protoType": "fixed64", "notes": "Always eight bytes. More efficient than uint64 if values are often greater than 2^56.", "cppType": "uint64", "csType": "<PERSON><PERSON>", "goType": "uint64", "javaType": "long", "phpType": "integer/string", "pythonType": "int/long", "rubyType": "<PERSON><PERSON>"}, {"protoType": "sfixed32", "notes": "Always four bytes.", "cppType": "int32", "csType": "int", "goType": "int32", "javaType": "int", "phpType": "integer", "pythonType": "int", "rubyType": "Bignum or Fixnum (as required)"}, {"protoType": "sfixed64", "notes": "Always eight bytes.", "cppType": "int64", "csType": "long", "goType": "int64", "javaType": "long", "phpType": "integer/string", "pythonType": "int/long", "rubyType": "<PERSON><PERSON>"}, {"protoType": "bool", "notes": "", "cppType": "bool", "csType": "bool", "goType": "bool", "javaType": "boolean", "phpType": "boolean", "pythonType": "boolean", "rubyType": "TrueClass/FalseClass"}, {"protoType": "string", "notes": "A string must always contain UTF-8 encoded or 7-bit ASCII text.", "cppType": "string", "csType": "string", "goType": "string", "javaType": "String", "phpType": "string", "pythonType": "str/unicode", "rubyType": "String (UTF-8)"}, {"protoType": "bytes", "notes": "May contain any arbitrary sequence of bytes.", "cppType": "string", "csType": "ByteString", "goType": "[]byte", "javaType": "ByteString", "phpType": "string", "pythonType": "str", "rubyType": "String (ASCII-8BIT)"}]}