#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恆生指數期貨Web監控啟動器
自動啟動後端服務器並打開瀏覽器

作者: AI Assistant
日期: 2025-01-14
"""

import os
import sys
import time
import threading
import webbrowser
import subprocess
from pathlib import Path

def check_dependencies():
    """檢查依賴項"""
    try:
        import futu
        print("✅ futu-api 已安裝")
        return True
    except ImportError:
        print("❌ 未安裝 futu-api")
        print("請運行: pip install futu-api")
        return False

def check_files():
    """檢查必要文件"""
    required_files = [
        'hsi_web_server.py',
        'hsi_web_monitor.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def start_backend_server():
    """啟動後端服務器"""
    try:
        print("🚀 正在啟動後端服務器...")
        
        # 啟動服務器進程
        process = subprocess.Popen(
            [sys.executable, 'hsi_web_server.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        return process
        
    except Exception as e:
        print(f"❌ 啟動後端服務器失敗: {e}")
        return None

def wait_for_server(max_wait=10):
    """等待服務器啟動"""
    import urllib.request
    import urllib.error
    
    print("⏳ 等待服務器啟動...")
    
    for i in range(max_wait):
        try:
            urllib.request.urlopen('http://localhost:8080/api/products', timeout=1)
            print("✅ 服務器已啟動")
            return True
        except (urllib.error.URLError, urllib.error.HTTPError):
            time.sleep(1)
            print(f"   等待中... ({i+1}/{max_wait})")
    
    print("❌ 服務器啟動超時")
    return False

def open_browser():
    """打開瀏覽器"""
    try:
        html_file = os.path.abspath('hsi_web_monitor.html')
        file_url = f'file://{html_file}'
        
        print(f"🌐 正在打開瀏覽器: {file_url}")
        webbrowser.open(file_url)
        
        return True
        
    except Exception as e:
        print(f"❌ 打開瀏覽器失敗: {e}")
        return False

def monitor_server_output(process):
    """監控服務器輸出"""
    try:
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(f"[服務器] {output.strip()}")
    except Exception as e:
        print(f"⚠️ 監控服務器輸出錯誤: {e}")

def main():
    """主函數"""
    print("🎯 恆生指數期貨Web監控啟動器")
    print("=" * 60)
    
    # 檢查環境
    print("\n🔍 檢查環境...")
    if not check_dependencies():
        input("\n按 Enter 鍵退出...")
        return
    
    if not check_files():
        input("\n按 Enter 鍵退出...")
        return
    
    # 檢查FUTU OpenD
    print("\n🔧 環境檢查:")
    print("   ✅ Python 依賴項正常")
    print("   ✅ 必要文件存在")
    print("   ⚠️  請確保 FUTU OpenD 已啟動並登錄")
    print("   ⚠️  請確保已有期貨 LV1 權限")
    
    choice = input("\n是否繼續啟動? (y/n): ").strip().lower()
    if choice != 'y':
        print("👋 已取消啟動")
        return
    
    # 啟動後端服務器
    print("\n" + "=" * 60)
    server_process = start_backend_server()
    
    if not server_process:
        input("\n按 Enter 鍵退出...")
        return
    
    # 在後台監控服務器輸出
    output_thread = threading.Thread(
        target=monitor_server_output, 
        args=(server_process,),
        daemon=True
    )
    output_thread.start()
    
    # 等待服務器啟動
    if not wait_for_server():
        print("❌ 服務器啟動失敗，正在終止...")
        server_process.terminate()
        input("\n按 Enter 鍵退出...")
        return
    
    # 打開瀏覽器
    print("\n" + "=" * 60)
    if open_browser():
        print("✅ 瀏覽器已打開")
    else:
        print("⚠️ 自動打開瀏覽器失敗，請手動打開:")
        print(f"   file://{os.path.abspath('hsi_web_monitor.html')}")
    
    # 顯示使用說明
    print("\n" + "=" * 60)
    print("📋 使用說明:")
    print("   1. 在瀏覽器中點擊 '連接' 按鈕")
    print("   2. 選擇要監控的期貨產品")
    print("   3. 查看實時價格和K線圖")
    print("   4. 數據每1秒自動更新")
    print("\n📊 功能特色:")
    print("   ✅ 實時價格監控")
    print("   ✅ 5分鐘K線圖")
    print("   ✅ 10SMA & 20SMA 技術指標")
    print("   ✅ 實時成交量圖表")
    print("   ✅ 支持恆指和小型恆指期貨")
    print("\n🌐 Web界面地址:")
    print(f"   file://{os.path.abspath('hsi_web_monitor.html')}")
    print("\n🔗 API服務器地址:")
    print("   http://localhost:8080")
    print("\n⏹️ 按 Ctrl+C 停止服務器")
    print("=" * 60)
    
    try:
        # 保持主進程運行
        while True:
            time.sleep(1)
            
            # 檢查服務器進程是否還在運行
            if server_process.poll() is not None:
                print("\n❌ 服務器進程已退出")
                break
                
    except KeyboardInterrupt:
        print("\n\n⏹️ 正在停止服務器...")
        
    finally:
        # 清理資源
        try:
            server_process.terminate()
            server_process.wait(timeout=5)
            print("✅ 服務器已停止")
        except subprocess.TimeoutExpired:
            print("⚠️ 強制終止服務器進程")
            server_process.kill()
        except Exception as e:
            print(f"⚠️ 清理資源時發生錯誤: {e}")


if __name__ == "__main__":
    main()
