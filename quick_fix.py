#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修復腳本
解決Web監控系統的連接問題
"""

import os
import sys
import time
import subprocess
import webbrowser
import threading

def check_and_kill_port_8080():
    """檢查並釋放8080端口"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if ':8080' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) > 4:
                        pid = parts[-1]
                        print(f"發現端口8080被進程{pid}占用，正在終止...")
                        subprocess.run(['taskkill', '/F', '/PID', pid], capture_output=True)
                        time.sleep(1)
                        return True
        else:  # Linux/Mac
            result = subprocess.run(['lsof', '-ti:8080'], capture_output=True, text=True)
            if result.stdout.strip():
                pid = result.stdout.strip()
                print(f"發現端口8080被進程{pid}占用，正在終止...")
                subprocess.run(['kill', '-9', pid], capture_output=True)
                time.sleep(1)
                return True
    except Exception as e:
        print(f"檢查端口時發生錯誤: {e}")
    
    return False

def start_simple_server():
    """啟動簡化服務器"""
    try:
        print("🚀 啟動簡化測試服務器...")
        
        # 檢查文件是否存在
        if not os.path.exists('simple_web_server.py'):
            print("❌ 找不到 simple_web_server.py 文件")
            return None
        
        # 啟動服務器
        process = subprocess.Popen(
            [sys.executable, 'simple_web_server.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1
        )
        
        return process
        
    except Exception as e:
        print(f"❌ 啟動服務器失敗: {e}")
        return None

def wait_for_server_ready():
    """等待服務器準備就緒"""
    import urllib.request
    import urllib.error
    
    print("⏳ 等待服務器啟動...")
    
    for i in range(10):
        try:
            urllib.request.urlopen('http://localhost:8080/api/products', timeout=2)
            print("✅ 服務器已準備就緒")
            return True
        except:
            time.sleep(1)
            print(f"   等待中... ({i+1}/10)")
    
    print("❌ 服務器啟動超時")
    return False

def open_browser_with_instructions():
    """打開瀏覽器並顯示使用說明"""
    try:
        html_file = os.path.abspath('hsi_web_monitor.html')
        if not os.path.exists(html_file):
            print("❌ 找不到 hsi_web_monitor.html 文件")
            return False
        
        file_url = f'file://{html_file}'
        print(f"🌐 正在打開瀏覽器: {file_url}")
        webbrowser.open(file_url)
        
        return True
        
    except Exception as e:
        print(f"❌ 打開瀏覽器失敗: {e}")
        return False

def monitor_server_output(process):
    """監控服務器輸出"""
    try:
        for line in iter(process.stdout.readline, ''):
            if line:
                print(f"[服務器] {line.strip()}")
            if process.poll() is not None:
                break
    except Exception as e:
        print(f"監控輸出錯誤: {e}")

def main():
    """主函數"""
    print("🔧 Web監控系統快速修復工具")
    print("=" * 50)
    
    # 步驟1: 檢查並釋放端口
    print("\n📋 步驟1: 檢查端口占用...")
    check_and_kill_port_8080()
    
    # 步驟2: 啟動簡化服務器
    print("\n📋 步驟2: 啟動測試服務器...")
    server_process = start_simple_server()
    
    if not server_process:
        print("❌ 無法啟動服務器")
        input("\n按 Enter 鍵退出...")
        return
    
    # 在後台監控服務器輸出
    output_thread = threading.Thread(
        target=monitor_server_output,
        args=(server_process,),
        daemon=True
    )
    output_thread.start()
    
    # 步驟3: 等待服務器準備就緒
    print("\n📋 步驟3: 等待服務器準備...")
    if not wait_for_server_ready():
        print("❌ 服務器未能正常啟動")
        server_process.terminate()
        input("\n按 Enter 鍵退出...")
        return
    
    # 步驟4: 打開瀏覽器
    print("\n📋 步驟4: 打開Web界面...")
    if open_browser_with_instructions():
        print("✅ 瀏覽器已打開")
    else:
        print("⚠️ 請手動打開瀏覽器並訪問:")
        print(f"   file://{os.path.abspath('hsi_web_monitor.html')}")
    
    # 顯示使用說明
    print("\n" + "=" * 50)
    print("🎯 快速修復完成！")
    print("=" * 50)
    print("📋 接下來的步驟:")
    print("   1. 在瀏覽器中點擊 '診斷連接' 按鈕測試")
    print("   2. 如果診斷通過，點擊 '連接' 按鈕")
    print("   3. 選擇要監控的期貨產品")
    print("   4. 查看實時數據（模擬數據）")
    print("\n🔧 如果仍有問題:")
    print("   1. 檢查控制台錯誤信息")
    print("   2. 運行 python diagnose_connection.py")
    print("   3. 確認防火牆設置")
    print("\n⚠️ 注意:")
    print("   - 當前使用模擬數據進行測試")
    print("   - 要使用真實數據，需要啟動 FUTU OpenD")
    print("   - 然後運行 python hsi_web_server.py")
    print("\n⏹️ 按 Ctrl+C 停止服務器")
    print("=" * 50)
    
    try:
        # 保持服務器運行
        while True:
            time.sleep(1)
            if server_process.poll() is not None:
                print("\n❌ 服務器進程已退出")
                break
                
    except KeyboardInterrupt:
        print("\n\n⏹️ 正在停止服務器...")
        
    finally:
        # 清理資源
        try:
            server_process.terminate()
            server_process.wait(timeout=3)
            print("✅ 服務器已停止")
        except:
            print("⚠️ 強制終止服務器")
            server_process.kill()

if __name__ == "__main__":
    main()
