syntax = "proto2";
package Qot_GetUserSecurity;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetusersecurity";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	required string groupName = 1; //分组名,有同名的返回排序首个
}

message S2C
{
	repeated Qot_Common.SecurityStaticInfo staticInfoList = 1; //自选股分组下的股票列表
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
