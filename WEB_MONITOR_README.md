# 恆生指數期貨Web實時監控系統

一個基於HTML + Python的恆生指數期貨實時監控系統，支持實時價格、K線圖和技術指標。

## 🎯 功能特色

### 📊 實時數據監控
- ✅ **1秒更新頻率** - 超快速實時數據刷新
- ✅ **雙產品支持** - 恆生指數期貨 + 小型恆生指數期貨
- ✅ **完整報價信息** - 開高低收、成交量、漲跌幅等

### 📈 專業K線圖表
- ✅ **5分鐘K線圖** - 參考富途牛牛交易程式風格
- ✅ **技術指標** - 10SMA、20SMA移動平均線
- ✅ **成交量圖表** - 實時成交量柱狀圖
- ✅ **響應式設計** - 支持桌面和移動設備

### 🎨 現代化界面
- ✅ **漸變背景** - 專業的視覺效果
- ✅ **實時色彩** - 漲綠跌紅的價格顯示
- ✅ **毛玻璃效果** - 現代化UI設計
- ✅ **中文界面** - 完全繁體中文支持

## 📦 系統架構

```
┌─────────────────┐    HTTP API    ┌──────────────────┐    FUTU API    ┌─────────────┐
│   Web前端       │ ◄──────────── │   Python後端     │ ◄──────────── │ FUTU OpenD  │
│ (HTML/JS/CSS)   │               │  (hsi_web_server) │               │             │
└─────────────────┘               └──────────────────┘               └─────────────┘
```

## 🚀 快速開始

### 1. 環境準備
```bash
# 安裝Python依賴
pip install futu-api

# 確保FUTU OpenD已啟動並登錄
# 牛牛號: 22188140
# 確認有期貨LV1權限
```

### 2. 一鍵啟動
```bash
python start_web_monitor.py
```

### 3. 使用步驟
1. 啟動器會自動打開瀏覽器
2. 在網頁中點擊「連接」按鈕
3. 選擇要監控的期貨產品
4. 開始查看實時數據和圖表

## 📁 文件說明

### 核心文件
- **`hsi_web_monitor.html`** - Web前端界面
- **`hsi_web_server.py`** - Python API後端服務器
- **`start_web_monitor.py`** - 一鍵啟動腳本

### 支持文件
- **`fixed_hsi_monitor.py`** - 命令行版本監控程式
- **`config.py`** - 配置文件
- **`README.md`** - 完整使用說明

## 🔧 API端點

後端服務器提供以下API端點：

| 端點 | 方法 | 說明 |
|------|------|------|
| `/api/connect` | GET | 連接到FUTU OpenD |
| `/api/disconnect` | GET | 斷開FUTU連接 |
| `/api/quote?product=HK.HSImain` | GET | 獲取實時報價 |
| `/api/kline?product=HK.HSImain&num=50` | GET | 獲取K線數據 |
| `/api/products` | GET | 獲取產品列表 |

## 📊 支持的期貨產品

| 代碼 | 名稱 | 說明 |
|------|------|------|
| `HK.HSImain` | 恆生指數期貨主連 | 標準恆指期貨 |
| `HK.MHImain` | 小型恆生指數期貨主連 | 小型恆指期貨 |

## 🎨 界面預覽

### 主要功能區域
```
┌─────────────────────────────────────────────────────────┐
│                    🎯 恆生指數期貨實時監控                    │
├─────────────────┬───────────────────────────────────────┤
│   📊 實時報價    │           📈 5分鐘K線圖                │
│   - 最新價      │           (含10SMA, 20SMA)            │
│   - 開高低收    │                                       │
│   - 漲跌幅      │                                       │
│   - 成交量      │                                       │
├─────────────────┴───────────────────────────────────────┤
│                    📊 成交量圖表                         │
└─────────────────────────────────────────────────────────┘
```

## ⚙️ 配置選項

### 更新頻率
- **實時報價**: 1秒更新
- **K線圖表**: 每5次報價更新一次（減少API調用）

### 技術指標
- **10SMA**: 10期簡單移動平均線（橙色線）
- **20SMA**: 20期簡單移動平均線（藍色線）

### 圖表設置
- **K線週期**: 5分鐘
- **數據點數**: 最近50個數據點
- **顏色方案**: 漲綠跌紅

## 🔍 故障排除

### 常見問題

1. **連接失敗**
   ```
   ❌ 連接失敗: 未連接到FUTU
   ```
   **解決方案**: 確保FUTU OpenD已啟動並登錄

2. **API調用失敗**
   ```
   ❌ API調用失敗: 網絡錯誤
   ```
   **解決方案**: 檢查後端服務器是否正常運行

3. **數據獲取失敗**
   ```
   ❌ 獲取報價失敗: 訂閱失敗
   ```
   **解決方案**: 確認帳號有期貨LV1權限

### 檢查清單
- [ ] FUTU OpenD已啟動
- [ ] 使用牛牛號22188140登錄
- [ ] 有期貨LV1權限
- [ ] 端口8080未被占用
- [ ] 瀏覽器支持現代JavaScript

## 📱 移動設備支持

系統採用響應式設計，支持：
- 📱 手機瀏覽器
- 📱 平板電腦
- 💻 桌面瀏覽器

在移動設備上，界面會自動調整為單列布局。

## ⚠️ 重要提醒

1. **數據使用**
   - 本系統僅供學習和個人使用
   - 請勿用於商業用途
   - 投資有風險，請謹慎決策

2. **API限制**
   - 遵守FUTU API使用條款
   - 注意請求頻率限制
   - 避免過度頻繁的數據請求

3. **安全提醒**
   - 保護好您的帳號信息
   - 不要在公共場所使用
   - 定期更改密碼

## 📞 技術支持

如遇到問題，請：
1. 檢查控制台錯誤信息
2. 確認FUTU OpenD狀態
3. 查看後端服務器日誌
4. 參考FUTU API官方文檔

## 📄 許可證

本系統僅供學習和研究使用，請遵守相關法律法規和FUTU API使用條款。
